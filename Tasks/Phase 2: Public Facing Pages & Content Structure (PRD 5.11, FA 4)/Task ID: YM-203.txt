Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
(For all public pages: ensure usage of next/image for all imagery, implement Quechua-inspired design, full-screen imagery, horizontal scrolling where appropriate, minimalist navigation, smooth transitions, excellent readability, responsiveness, and basic SEO metadata using Next.js Metadata API)
Task ID: YM-203
Title: Implement Informational Pages (About, Blog, Careers)
Status: Done
Dependencies: YM-201
Priority: Medium
Description: Create static informational pages. PRD 5.11.3, FA 4.3.
Details:
* Pages: /about, /careers.
* Blog: Basic list page (/blog) and detail page structure (/blog/[postId]). Content can be static initially or placeholder for CMS.
* Testimonials section/page, potentially using Shadcn Carousel.
Test Strategy: Pages are accessible, display content clearly, and maintain visual/UX quality.