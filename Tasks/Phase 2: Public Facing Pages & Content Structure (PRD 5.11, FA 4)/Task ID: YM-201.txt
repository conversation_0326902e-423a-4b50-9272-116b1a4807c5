Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
(For all public pages: ensure usage of next/image for all imagery, implement Quechua-inspired design, full-screen imagery, horizontal scrolling where appropriate, minimalist navigation, smooth transitions, excellent readability, responsiveness, and basic SEO metadata using Next.js Metadata API)
Task ID: YM-201
Title: Implement Main Landing Page (/)
Status: Done
Dependencies: YM-004 (Layout, Quechua UI Foundation)
Priority: High
Description: Develop the main landing page with Hero, Challenges/Solutions, Benefits, CTAs, adhering to Quechua design. PRD 5.11.1, FA 4.1.
Details:
* Structure content as per PRD/FA.
* Incorporate high-quality full-screen imagery and tasteful horizontal scrolling sections.
* Craft compelling CTAs (e.g., "Book a Transfer", "Become a YoungMover").
* Implement with semantic HTML for SEO.
* Ensure fast load times (LCP, FCP) and high Lighthouse scores (Performance, Accessibility, SEO).
Test Strategy: Page renders correctly, is visually stunning, and responsive. Content aligns with requirements. CTAs link correctly. High Lighthouse scores.