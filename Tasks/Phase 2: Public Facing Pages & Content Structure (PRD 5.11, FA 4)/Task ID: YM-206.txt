Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
(For all public pages: ensure usage of next/image for all imagery, implement Quechua-inspired design, full-screen imagery, horizontal scrolling where appropriate, minimalist navigation, smooth transitions, excellent readability, responsiveness, and basic SEO metadata using Next.js Metadata API)
Task ID: YM-206
Title: Implement Advanced SEO (Sitemap, Robots.txt) & Structured Data (JSON-LD)
Status: Done
Dependencies: YM-201, YM-202, YM-203, YM-204 (Basis-Inhalte der öffentlichen Seiten müssen stehen)
Priority: Medium
Description: Enhance Search Engine Optimization beyond basic metadata by generating sitemap.xml, robots.txt, and implementing JSON-LD structured data for relevant public pages to improve search engine understanding and visibility.
Details:
* Sitemap Generation:
* Programmatically generate sitemap.xml (e.g., using a script during build time or a Next.js API route) to include all public, indexable pages (landing page, solution pages, blog posts, etc.).
* Ensure it's correctly formatted and submitted to search engines (e.g., Google Search Console).
* Robots.txt:
* Create/Configure public/robots.txt to guide search engine crawlers (e.g., disallow crawling of admin sections, private API routes, allow sitemap location).
* Structured Data (JSON-LD):
* Implement appropriate structured data markup for key public pages:
* Organization schema for the "About Us" page or globally.
* Service schema for "Solution Pages" detailing vehicle transfer services.
* WebSite schema for site-wide search box potential.
* BlogPosting or Article schema for blog posts.
* FAQPage schema for the FAQ page.
* (If applicable) JobPosting schema for the "Careers" page.
* Embed JSON-LD directly in the <head> of relevant pages using Next.js Metadata API or by generating script tags.
* Regularly validate structured data using tools like Google's Rich Results Test or Schema Markup Validator.
Test Strategy:
* sitemap.xml is generated correctly, is valid, and includes all intended public URLs.
* robots.txt is correctly configured and accessible.
* Structured data (JSON-LD) is present on relevant pages and validates successfully with testing tools.
* Improved visibility/understanding by search engines (monitored via Google Search Console over time).
* Lighthouse SEO score remains high or improves.