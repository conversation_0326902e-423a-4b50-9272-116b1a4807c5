Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-006
Title: Setup Database & ORM (Prisma)
Status: Done
Dependencies: YM-005
Priority: Critical
Description: Set up the chosen database (PostgreSQL) and integrate Prisma ORM into the project.
Details:
* Install PostgreSQL locally or via Docker.
* Install Prisma (npm install prisma --save-dev, npx prisma init --datasource-provider postgresql).
* Connect Prisma to the database via .env.
* Translate YM-005 schema into schema.prisma.
* Run initial migration (npx prisma migrate dev --name init) to create tables.
* Generate Prisma Client (npx prisma generate).
Test Strategy: Can connect to DB. Prisma Client is generated. Initial tables are created in the database.