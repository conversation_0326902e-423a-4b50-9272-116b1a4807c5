Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-005
Title: Database Schema Design
Status: Done
Dependencies: -
Priority: Critical
Description: Design the initial database schema to support core entities: Users (Clients, Drivers, Admins), Orders, Vehicles, Bids/Assignments, Documents, Messages, Ratings.
Details:
* Choose a database technology (e.g., PostgreSQL).
* Choose an ORM (e.g., Prisma).
* Define tables and relationships for User (with roles), UserProfileClient, UserProfileDriver, Vehicle, Order, Bid, Assignment, Document (for driver uploads), Message, Rating, Invoice, Payment.
* Include fields for timestamps (createdAt, updatedAt), soft deletes if needed.
* Plan for secure password storage (hashing + salting).
* Consider indexes for performance on frequently queried fields.
Test Strategy: Schema is documented and peer-reviewed. ORM migrations can be generated. Schema supports planned core functionalities.