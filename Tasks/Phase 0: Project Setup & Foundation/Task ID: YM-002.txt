Status: Done | In Progress | Blocked | In Review | To Do
Priority: Critical | High | Medium | Low
Task ID: YM-002
Title: Configure Lin<PERSON>, Formatters & Pre-commit Hooks
Status: Done
Dependencies: YM-001
Priority: High
Description: Set up ESLint and Prettier for code consistency and quality, and <PERSON><PERSON> for pre-commit checks.
Details:
* Install Prettier and ESLint plugins for Tailwind CSS.
* Configure .eslintrc.json and .prettierrc.json.
* Add linting and formatting scripts to package.json.
* Setup pre-commit hooks (Husky + lint-staged) to run linters/formatters before commits.
Test Strategy: Linting and formatting commands run without errors. Pre-commit hook works as expected. Code consistency is enforced.