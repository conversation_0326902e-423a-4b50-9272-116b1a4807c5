Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-402
Title: Driver Views Available Jobs with Advanced Filtering & Transparency
Status: Done
Dependencies: YM-401, YM-302 (needs orders to exist)
Priority: High
Description: Allow drivers to view and filter available jobs effectively, with transparency on job volume. Addresses PRD 5.2.2, 5.9.1, 5.10.1, Pain Point 8 (FR-P8), Pain Point 9 (FR-P9).
Details:
* Available Jobs page (/driver/jobs) using Shadcn Table or custom job cards.
* Display key job details: Pickup/Dropoff (show distance from driver if location available), Vehicle Type, Estimated Pay, Date, Urgency.
* Advanced Filtering (PP8): Location/Radius (requires driver location permission/input), Vehicle Type, Distance, Urgency, Pay Range. Filters update list via React Query.
* Sorting options: Newest, Highest Pay, Closest.
* Job Volume Transparency (PP9): Display indicators or information on available job volume (e.g., "X jobs in your preferred region", "High demand for [vehicle type] transfers"). This could be a summary above the list or integrated contextually.
* Use next/image if any generic vehicle type images are used. Skeleton loading for job list.
Test Strategy: Driver can see available jobs. Advanced filters and sorting work accurately and efficiently. Job volume indicators are present and helpful. UI is clean, responsive, and provides all necessary information quickly.

Implementation Notes:
* Created available jobs page with custom job cards
* Implemented advanced filtering system with location, vehicle type, distance range, pay range, and urgency filters
* Added sorting options (newest, highest pay, closest)
* Implemented job volume transparency with a summary card showing job counts and demand indicators
* Added loading states with Skeleton components
* Implemented responsive design with a sidebar for filters on larger screens
* Used Lucide icons for visual clarity