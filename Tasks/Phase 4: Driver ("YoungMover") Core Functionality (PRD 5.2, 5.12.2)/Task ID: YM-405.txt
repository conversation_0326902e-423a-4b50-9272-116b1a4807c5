Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-405
Title: Driver Views Earnings & Payment History
Status: Done
Dependencies: YM-404 (needs completed jobs)
Priority: Medium
Description: Provide drivers with a clear overview of their earnings and payment history. Addresses PRD 5.4.4.
Details:
* Earnings page (/driver/earnings).
* Summary: Total Earnings, Pending Payments, Paid Amounts (Shadcn Cards).
* Detailed breakdown of payments per completed job (Shadcn Table).
* Status of each payment (e.g., "Processing", "Paid").
* Use React Query to fetch earnings data. Skeleton loading for tables/summaries.

Implementation Notes:
* Created earnings page with summary cards for key metrics
* Implemented payment history table with filtering tabs (all, pending, paid)
* Added status badges to clearly indicate payment status
* Implemented loading states with Skeleton components
* Added payment information section with details about payment methods and schedule
* Used Shadcn components (Card, Table, Tabs) for consistent UI
* Added gradient styling for summary cards to match Quechua-inspired design
* Implemented responsive design for all screen sizes
Test Strategy: Driver can clearly see earnings information. Data is accurate and well-presented. UI is clean and responsive.