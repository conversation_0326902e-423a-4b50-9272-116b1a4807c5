Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-602
Title: Mutual Rating & Feedback System
Status: Done
Dependencies: YM-303, YM-404 (Order Completion)
Priority: Medium
Description: Allow clients and drivers to rate each other and provide feedback post-transfer. Addresses PRD 5.7.1, 5.7.2.
Details:
* Rating mechanism (e.g., 1-5 stars using Shadcn RadioGroup styled as stars, comments via Textarea) available after order status is "Completed".
* Forms for submitting ratings/feedback in client/driver portals.
* API endpoints to store Rating records (linked to user, ratedUser, order).
* Display average ratings on user profiles (client for driver, driver for client) where appropriate and GDPR compliant.
* Platform feedback mechanism (e.g., a separate "Feedback" link/modal).
Test Strategy: Users can submit ratings/feedback after order completion. Ratings are stored and displayed appropriately. UI is intuitive.