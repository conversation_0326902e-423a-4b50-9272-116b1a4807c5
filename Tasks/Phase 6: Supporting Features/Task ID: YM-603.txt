Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-603
Title: Admin Panel: User Management (Client & Driver Verification - ST-302)
Status: Done
Dependencies: YM-102, YM-103 (Registrations)
Priority: High
Description: Implement admin panel for user account management, including verification and moderation. Addresses PRD 5.1.4.
Details:
* Secure admin section/layout (src/app/(platform)/admin/layout.tsx).
* User list page (/admin/users): Display all users (clients, drivers) using Shadcn Table.
* Filter by role (client/driver), status (pending verification, active, suspended). Search by name/email.
* User detail view:
* Approve/reject pending registrations (clients after email verification, drivers after initial submission). Provide reason for rejection. Triggers notification (YM-505).
* View user details, uploaded documents (for drivers).
* Suspend/reactivate accounts.
* Use React Query for data fetching and mutations.
Test Strategy: Admin can log in. Can view, filter, and search users. Can approve/reject registrations. Can suspend/reactivate accounts. Actions trigger appropriate status changes and notifications.