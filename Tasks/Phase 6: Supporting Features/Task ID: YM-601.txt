Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-601
Title: Secure In-App Messaging (Client-Driver)
Status: Done
Dependencies: YM-501 (Driver Assigned)
Priority: Medium
Description: Implement secure, order-specific messaging between clients and assigned drivers. Addresses PRD 5.6.1.
Details:
* Database schema for Message (senderId, receiverId, orderId, content, timestamp, isRead).
* Messaging UI within the order detail page for both client and driver. Use Shadcn components for input, message bubbles.
* API endpoints to send messages and fetch message history for an order (use React Query, consider refetchInterval for polling or WebSockets for real-time).
* Mark messages as read.
* Notification (YM-505) for new messages.
Test Strategy: Client and assigned driver can exchange messages. Messages are stored and displayed correctly. Read status works. New message notifications are triggered.