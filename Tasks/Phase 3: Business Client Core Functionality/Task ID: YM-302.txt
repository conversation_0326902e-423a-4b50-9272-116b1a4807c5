Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-302
Title: Business Client Creates Transfer Order with Superior UX & Transparency
Status: Done
Dependencies: YM-301 (Client Dashboard), YM-006 (DB Models), YM-007 (React Query), YM-106
Priority: Critical
Description: Implement a highly intuitive, efficient, and transparent order creation process for business clients. Addresses PRD 5.2.1, 5.3, 5.4.1, Pain Point 1 (FR-P1), Pain Point 3 (FR-P3).
Details:
* Order creation page (/client/orders/create) using Shadcn UI, react-hook-form, Zod.
* Fields: vehicle details (VIN, make, model, type - consider Shadcn Combobox with search for makes/models), pickup/delivery locations (Shadcn Input with potential Google Places Autocomplete integration via custom component for better UX), desired pickup/delivery dates/times (Shadcn DatePicker and custom TimePicker or combined DateTimePicker), special instructions.
* UX: Simplified multi-step "wizard" flow if form is extensive, with clear progress indication (Shadcn Progress or custom steps). Each step validated before proceeding. Minimal redundancies.
* Pricing Transparency (PP1): Dynamically calculate and display a transparent, upfront pricing estimate with a clear cost breakdown (e.g., base fee, distance fee, vehicle type surcharge) before final submission. Mechanism to ensure fair pricing.
* Templates (PP3): (Initial thought, can be deferred) Mechanism to save order as template.
* API endpoint (/api/orders POST): Server-side validation (using Zod schema from src/lib/validators/order.schemas.ts). Creates Order and related Vehicle record. Status: "Posted".
* Use React Query's useMutation for order submission, providing optimistic updates if sensible, clear loading states on submit button (spinner), and toast notifications (Shadcn Toast) for success/error.
* Ensure full keyboard accessibility, responsive design for all devices.
Test Strategy: Client can easily and efficiently create an order. Validation is robust and user-friendly. Pricing is transparent. Order saved correctly. UI is polished, responsive, and accessible. User journey feels trustworthy and addresses booking simplification.