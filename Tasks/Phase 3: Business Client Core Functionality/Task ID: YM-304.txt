Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-304
Title: Business Client Billing & Invoicing
Status: Done
Dependencies: YM-303 (needs completed orders)
Priority: Medium
Description: Allow clients to view and download their invoices for completed transfers. Addresses PRD 5.4.3, 5.4.4.
Details:
* Billing/Invoices page (/client/billing) listing invoices (Shadcn Table).
* Details per invoice: Invoice ID, Order ID, Date, Amount, Status (Paid/Unpaid).
* API endpoint to generate/fetch invoice data.
* Functionality to download invoices as PDF (e.g., using react-pdf/renderer or a server-side PDF generation library).
* Secure online payment processing for service fees (placeholder for Stripe/PayPal integration later).
* Automated invoicing logic after order completion.
Test Strategy: Client can view invoice history. Invoices display correct information. PDF download works. UI is clear and professional.