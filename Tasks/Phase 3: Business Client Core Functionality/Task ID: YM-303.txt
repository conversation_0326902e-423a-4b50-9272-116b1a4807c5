Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-303
Title: Business Client Order Listing & Management (View, Filter, Track - ST-104)
Status: Done
Dependencies: YM-302
Priority: High
Description: Allow clients to view, filter, and manage their created orders, including real-time tracking. Addresses PRD 5.2.1, 5.5.1, 5.5.2, 5.9.2, Pain Point 7 (FR-P7), Pain Point 8 (FR-P8).
Details:
* Order list page (/client/orders) displaying orders in Shadcn Table or custom cards.
* Display key order info: Order ID, Status, Vehicle, Pickup/Destination, Dates, Price.
* Filtering (PP8): Advanced filters (Shadcn DropdownMenu with Checkbox or Select): status, date range, vehicle type. Filters should update list dynamically using React Query parameters.
* Order Detail View (/client/orders/[orderId]):
* Display all order details.
* Real-time Tracking (PP7): Show current order status (e.g., "Posted", "Driver Assigned", "In Transit", "Completed") fetched via React Query (consider refetch intervals or WebSockets later for true real-time).
* Display assigned driver's details (if applicable).
* (Later) Map integration for vehicle's last known location if GPS tracking is part of driver's service.
* Persistent order tracking (easily accessible upon re-login).
* Use next/image for any vehicle images. Skeleton loading for table/list and detail view.
Test Strategy: Client can view a list of their orders. Filters work effectively. Order details are accurate. Status tracking is clear and updates. UI is clean, responsive, and efficient.