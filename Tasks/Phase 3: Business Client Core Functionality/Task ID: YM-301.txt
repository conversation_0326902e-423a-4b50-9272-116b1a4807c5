Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-301
Title: Business Client Dashboard Foundation
Status: Done
Dependencies: YM-104 (Login), YM-004 (Layout), YM-007 (React Query)
Priority: High
Description: Design and implement the main dashboard for logged-in business clients, providing an overview and easy navigation. Addresses PRD 5.12.1.
Details:
* Implement authenticated layout for client users (src/app/(platform)/client/layout.tsx), potentially including a sidebar navigation (Shadcn Sheet for mobile, persistent for desktop).
* Dashboard page (/client/dashboard):
* Display summary widgets (e.g., "Active Orders", "Recent Activity", "Pending Actions") using Shadcn Card components. Style according to Quechua aesthetic.
* Use React Query to fetch summary data. Implement Skeleton Screens (Shadcn Skeleton) for loading states of these widgets.
* Clear CTAs to primary actions (e.g., "Create New Order").
* Ensure responsive design and clear information hierarchy.
Test Strategy: Client dashboard loads after login. Displays relevant summary info (or placeholders). Navigation to other client sections works. UI is polished, responsive, and provides good overview. Loading states are handled gracefully.