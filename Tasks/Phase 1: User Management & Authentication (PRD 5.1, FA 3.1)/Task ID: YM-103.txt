Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-103
Title: Driver ("YoungMover") Registration with Simplified UX & Document Upload
Status: Done
Dependencies: YM-101, YM-003 (Shadcn), YM-004
Priority: High
Description: Implement a simplified, user-friendly registration for Drivers, including secure document uploads. Addresses PRD 5.1.1, Pain Point 6 (FR-P6).
Details:
* Create /register-driver page with Shadcn UI forms.
* Fields: personal details, contact info, password, terms acceptance.
* Document upload component (Shadcn Input type="file" styled or custom): driver's license, business registration, insurance. Upload to secure storage (e.g., S3 presigned URLs, or Vercel Blob).
* Client-side validation (react-hook-form + Zod).
* API endpoint (/api/auth/register-driver): Creates User (role: DRIVER, status: PENDING_ADMIN_APPROVAL), UserProfileDriver, and links Document records.
* Confirmation email/page about application status.
* Loading states and clear feedback during upload and submission.
* Ensure accessibility and responsive design.
Test Strategy: Driver can register. Documents upload successfully and are associated with the driver. Application status is PENDING_ADMIN_APPROVAL. Process feels efficient. UI is polished.