Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-101
Title: Implement User Model & Authentication (NextAuth.js)
Status: Done
Dependencies: YM-006, YM-007 (for potential user data fetching post-auth)
Priority: Critical
Description: Implement the User model in Prisma, set up NextAuth.js with Credentials and potentially OAuth providers, and create core authentication API routes.
Details:
* Refine User table in schema.prisma (roles, emailVerified, etc.). Add related profile tables.
* Setup NextAuth.js: configure [...nextauth]/route.ts.
* Implement Credentials provider for email/password login.
* Implement password hashing (bcrypt) and comparison logic.
* Define NextAuth session strategy (JWT or database).
* Create signIn, signUp (internal), signOut, getSession API functionalities.
* Secure API routes using NextAuth session.
Test Strategy: User can be created via Prisma Studio/seed. Passwords are hashed. NextAuth session is established on login. Protected API routes are inaccessible without authentication.