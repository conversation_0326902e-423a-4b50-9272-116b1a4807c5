Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-102
Title: Business Client Registration with Superior UX
Status: Done
Dependencies: YM-101, YM-003 (Shadcn), YM-004 (Layout, Fonts, Colors)
Priority: High
Description: Implement a streamlined and visually appealing registration flow for Business Clients. Addresses PRD 5.1.1.
Details:
* Create /register-client page with Shadcn UI form components (Input, Button, Checkbox), styled according to Quechua aesthetic.
* Fields: company name, contact person, email, password, address, VAT ID, terms acceptance.
* Implement robust client-side validation using react-hook-form and Zod, providing clear, inline error messages and real-time feedback.
* API endpoint (/api/auth/register-client): Validates data server-side, creates User (role: CLIENT, status: PENDING_EMAIL_VERIFICATION), and UserProfileClient.
* Send email verification email (using a service like Resend/SendGrid).
* Confirmation page/toast message indicating next steps (email verification, then admin approval).
* Implement loading states (e.g., button spinner) during form submission.
* Ensure full keyboard accessibility for the form.
Test Strategy: Client can register smoothly. Validation is intuitive. Email verification link works. Account status is PENDING_EMAIL_VERIFICATION, then PENDING_ADMIN_APPROVAL. Data stored correctly. Form is accessible (keyboard nav, ARIA). UI is polished and responsive.