Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-105
Title: Password Reset/Recovery with Clear UX
Status: Done
Dependencies: YM-101
Priority: Medium
Description: Implement a secure and user-friendly password reset functionality.
Details:
* "Forgot Password" form (/forgot-password) to input email.
* API to generate a secure, unique, time-limited reset token (store hashed token in DB associated with user).
* Send password reset email with a link containing the token.
* Reset password page (/reset-password/[token]) to enter and confirm new password.
* API to validate token, check expiry, and update password (re-hash new password).
* Clear UI feedback throughout the process (confirmation messages, error handling).
Test Strategy: User can request and complete password reset. Email is sent. Token works and expires correctly. Password is updated securely. UX is clear.