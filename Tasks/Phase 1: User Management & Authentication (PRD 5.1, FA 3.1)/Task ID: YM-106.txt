Status: To Do | In Progress | Blocked | In Review | Done
Priority: Critical | High | Medium | Low
Task ID: YM-106
Title: User Profile Management (Client & Driver)
Status: Done
Dependencies: YM-104, YM-007 (React Query for data)
Priority: Medium
Description: Allow authenticated clients and drivers to view and edit their profile information.
Details:
* Client profile page (/client/profile): View/edit company details, contact person, address.
* Driver profile page (/driver/profile): View/edit personal details, contact info, manage/update uploaded documents (re-upload, delete if allowed).
* Use React Query for fetching profile data and useMutation for updates, providing optimistic updates and clear loading/success/error feedback (toasts).
* Forms built with Shadcn UI, react-hook-form, and Zod for validation.
Test Strategy: Users can view and update their profile data. Document management for drivers works. Changes are reflected correctly. UI provides good feedback.