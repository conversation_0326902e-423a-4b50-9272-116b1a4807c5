# YoungMobility: Tech-Stack Regeln

## 1. Overview
Diese Regeln definieren den technologischen Rahmen für die Entwicklung der YoungMobility-Plattform. Ziel ist es, eine moderne, skalierbare, wartbare und sichere Anwendung zu erstellen, die eine herausragende User Experience bietet, inspiriert von `https://quechua-lookbook.com/ss25/` und die Kernfunktionalitäten von onlogist.com übertrifft. Alle Entscheidungen müssen die definierten Ziele für Performance, Skalierbarkeit, Sicherheit und Wartbarkeit unterstützen.

## 2. Core Framework & Language
*   **Primärsprache:** TypeScript wird für Frontend-Entwicklung und Backend-Entwicklung eingesetzt, um Typsicherheit und bessere Code-Wartbarkeit zu gewährleisten.
*   **Code-Konventionen:** Es sind durchgehend konsistente Coding-Standards, Linting- (ESLint mit TypeScript-Plugin) und Formatierungsregeln (Prettier) einzuhalten.

## 3. Frontend
*   **Framework/Build-Tool:** Next.js (neueste stabile Version) mit React (neueste stabile Version).
*   **Styling:** Tailwind CSS (neueste stabile Version) für Utility-First-CSS.
*   **UI-Komponenten:** Shadcn UI (neueste stabile Version) für wiederverwendbare UI-Komponenten.
*   **State Management:** Zustand oder Redux Toolkit für komplexere globale Zustände, React Context API für einfachere, lokale Zustände.
*   **Performance:** Fokus auf schnelle Ladezeiten (LCP < 2.5s, FID < 100ms, FCP < 1.8s) durch Code-Splitting, Lazy Loading, Bildoptimierung und andere Next.js-Optimierungsfeatures.

## 4. Backend / API
*   **Sprache/Framework:** Node.js mit NestJS (neueste stabile Version). NestJS bietet eine strukturierte Architektur, ist TypeScript-basiert und gut für skalierbare Anwendungen geeignet.
*   **API-Typ:** RESTful API. Diese bietet breite Kompatibilität und ein ausgereiftes Ökosystem. Für spezifische, hochkomplexe Datenabfragen kann optional eine GraphQL-Schnittstelle für interne Zwecke evaluiert werden.
*   **Datenbank:** PostgreSQL (neueste stabile Version). Eine leistungsstarke, quelloffene relationale Datenbank, die Skalierbarkeit, komplexe Abfragen und Datenintegrität unterstützt.
*   **Authentifizierung & Autorisierung:**
    *   Sichere Mechanismen für Benutzer-Login mittels JWT (JSON Web Tokens).
    *   Für externe API-Integrationen: OAuth2.
*   **ORM/Query Builder:** Prisma. Bietet exzellente TypeScript-Unterstützung, Typsicherheit und eine moderne Entwicklererfahrung für die Datenbankinteraktion.

## 5. Logging
*   **Backend:** Umfassendes Logging von Anfragen, Fehlern und wichtigen Systemereignissen mit einer Bibliothek wie Winston oder Pino. Logs werden im JSON-Format strukturiert.
*   **Frontend:** Fehler-Logging und Performance-Metriken mittels Sentry oder einem ähnlichen Dienst.
*   **Zentralisierung:** Logs werden an ein zentrales Log-Management-System (z.B. ELK Stack – Elasticsearch, Logstash, Kibana – oder eine Cloud-native Lösung wie AWS CloudWatch Logs / Google Cloud Logging) gesendet.

## 6. Testing
*   **Strategie:** Umfassende Testabdeckung mit einem Fokus auf automatisierte Tests.
*   **Frontend:**
    *   Unit-Tests mit Vitest oder Jest und React Testing Library.
    *   Component-Tests mit Storybook oder Vitest.
    *   End-to-End-Tests mit Playwright oder Cypress für kritische User-Flows.
*   **Backend:**
    *   Unit-Tests mit Jest (integriert in NestJS).
    *   Integrationstests für API-Endpunkte und Service-Interaktionen.
*   **Allgemein:**
    *   Performance-Tests mit Tools wie Lighthouse, WebPageTest (für Frontend) und k6 oder JMeter (für Backend/API).
    *   Regelmäßige Sicherheitsscans (SAST/DAST Tools) und manuelle Penetrationstests vor größeren Releases.
    *   Automatisierte Tests als integraler Bestandteil der CI/CD-Pipeline.

## 7. Key Architectural Patterns
*   **Modularität:** Das Backend wird nach den Prinzipien von NestJS modular aufgebaut. Das Frontend wird in wiederverwendbare Komponenten und Features unterteilt.
*   **Skalierbarkeit:** Die Architektur ist auf horizontale Skalierbarkeit ausgelegt (z.B. durch Containerisierung mit Docker und Orchestrierung mit Kubernetes oder Nutzung serverloser Funktionen wo sinnvoll).
*   **Clean Architecture Prinzipien:** Strikte Trennung von Concerns (Presentation, Application, Domain, Infrastructure) sowohl im Backend (NestJS fördert dies) als auch im Frontend.
*   **Responsive Design:** Frontend muss auf allen Geräten (Desktop, Tablet, Mobil) optimal funktionieren.

## 8. External APIs / Integrations (Beispiele)
*   **Payment Gateway:** Stripe für sichere Online-Zahlungsabwicklung.
*   **Mapping Services:** Google Maps Platform APIs (oder Mapbox als Alternative) für ortsbezogene Suchen, Distanzberechnungen und Kartendarstellungen.
*   **Dokumenten-Speicher:** AWS S3 (oder Google Cloud Storage / Azure Blob Storage) für den sicheren Upload und die Verwaltung von Fahrer-Dokumenten.
*   **Email Service:** SendGrid (oder Amazon SES / Mailgun) für Transaktions-E-Mails und Benachrichtigungen.
*   **Monitoring/APM:** Sentry für Fehler-Tracking und Basis-APM, ergänzt durch Prometheus/Grafana oder Datadog für tiefgreifenderes Monitoring.

-

# Current Project Structure Documentation

This document outlines the current project directory structure for YoungMobility. It details the purpose of each main folder and provides guidance on where new files and modules should be added.

## 1. `Database_sql/`

*   **Explanation:**
    *   This directory is intended for raw SQL files that are *not* directly managed by the ORM (Prisma) migration system.
    *   This includes:
        *   Initial database schema setup scripts (if used before Prisma took over or for specific bootstrapping).
        *   Complex SQL views, stored procedures, or functions that are difficult or inefficient to express using the ORM.
        *   Database diagrams (e.g., ERDs exported as images or SQL).
        *   One-off data migration or cleanup scripts written in raw SQL.
*   **Where New Additions Should Go:**
    *   New complex SQL views or functions required by the backend.
    *   Manually crafted SQL scripts for specific data operations not handled by application logic.
    *   Archived or versioned database diagrams.
    *   **Note:** Standard schema changes and migrations should be handled by Prisma (see `src/prisma/`).

## 2. `docs/`

*   **Explanation:**
    *   Contains all project-related documentation.
    *   This includes:
        *   Product Requirements Documents (PRD).
        *   Functional Requirements.
        *   Architectural diagrams and decision logs.
        *   API documentation (e.g., generated OpenAPI/Swagger specifications from NestJS, or supplementary Markdown docs).
        *   Style guides or coding convention documents specific to this project.
        *   Onboarding guides for new developers.
        *   Meeting notes or summaries if relevant for broader project context.
*   **Where New Additions Should Go:**
    *   New architectural decision records.
    *   Updated API specifications.
    *   How-to guides for specific development tasks or features.
    *   Release notes or change logs (if not managed elsewhere).

## 3. `public/`

*   **Explanation:**
    *   This directory is managed by Next.js and contains static assets that are served directly from the root of the application domain.
    *   Files here are accessible via `yourdomain.com/filename.ext`.
    *   Examples: `favicon.ico`, `robots.txt`, `sitemap.xml`, manifest files, static images or fonts not processed by Next.js's optimization pipelines (though Next.js `Image` component is preferred for images).
*   **Where New Additions Should Go:**
    *   Root-level static assets like verification files for third-party services.
    *   Brand logos or images that need to be referenced by absolute paths without Next.js processing.
    *   Static data files (e.g., JSON) that need to be publicly accessible.

## 4. `src/`

*The core application code resides here, for both Frontend (Next.js) and Backend (NestJS).*

*   **`src/app/` (Frontend - Next.js App Router)**
    *   **Explanation:** Core of the Next.js application using the App Router. Contains layouts, pages, loading UI, error handlers, and route handlers (Next.js API routes).
    *   Each route segment is represented by a folder. `page.tsx` defines the UI for a route, `layout.tsx` defines a shared UI structure.
    *   `api/` subfolders here are for Next.js specific API routes (serverless functions), distinct from the main NestJS backend API.
    *   **Where New Additions Should Go:** New pages, layouts for new sections, Next.js API routes for frontend-specific backend tasks (e.g., BFF patterns directly related to a page).

*   **`src/components/` (Frontend - React Components)**
    *   **Explanation:** Reusable React components used across the Next.js application.
    *   Organized into subfolders by feature, type (e.g., `ui/` for generic Shadcn-based components, `layout/`, `forms/`), or domain.
    *   Includes both Client Components (`'use client'`) and Server Components.
    *   **Where New Additions Should Go:** New UI elements, shared presentational or container components.

*   **`src/lib/` or `src/utils/` (Frontend - Utilities)**
    *   **Explanation:** Utility functions, helper modules, client-side API fetching logic (e.g., functions to call the NestJS backend), constants, and other shared client-side logic for the Next.js application.
    *   **Where New Additions Should Go:** New helper functions, utility classes, API client configurations.

*   **`src/hooks/` (Frontend - Custom React Hooks)**
    *   **Explanation:** Custom React hooks used in the Next.js application to encapsulate and reuse stateful logic.
    *   **Where New Additions Should Go:** New custom hooks.

*   **`src/store/` or `src/contexts/` (Frontend - State Management)**
    *   **Explanation:** Global or feature-specific state management logic using Zustand, Redux Toolkit, or React Context API.
    *   **Where New Additions Should Go:** New state slices, stores, or context providers.

*   **`src/styles/` (Frontend - Styling)**
    *   **Explanation:** Global stylesheets (`globals.css` for Tailwind base, components, utilities), Tailwind CSS configuration extensions, and potentially CSS Modules for specific component styling if not purely using Tailwind.
    *   **Where New Additions Should Go:** Custom global styles, additional Tailwind CSS plugin configurations.

*   **`src/server/` (Backend - NestJS Application)**
    *   **Explanation:** The main NestJS backend application code. This is where the primary API logic resides.
    *   Organized into NestJS modules (e.g., `src/server/auth/`, `src/server/users/`, `src/server/orders/`).
    *   Each module typically contains:
        *   `*.module.ts`: Module definition.
        *   `*.controller.ts`: API route handlers.
        *   `*.service.ts`: Business logic.
        *   `*.entity.ts` or `dto/`: Data Transfer Objects and entity definitions (if not fully covered by Prisma schema).
        *   `*.guard.ts`, `*.interceptor.ts`, `*.pipe.ts`: NestJS-specific constructs.
    *   `src/server/main.ts`: Entry point for the NestJS application.
    *   `src/server/app.module.ts`: Root module for the NestJS application.
    *   **Where New Additions Should Go:** New NestJS modules for new features/domains, new controllers for API endpoints, new services for business logic.

*   **`src/prisma/` (Backend - ORM & Database Schema)**
    *   **Explanation:** Contains Prisma-related files.
        *   `schema.prisma`: The single source of truth for your database schema and Prisma client generation.
        *   `migrations/`: Directory containing all database migration files generated by `prisma migrate dev`. Each subfolder represents a single migration.
        *   (Optionally) `seed.ts`: Script for seeding the database with initial data, typically run with `prisma db seed`.
    *   **Where New Additions Should Go:**
        *   Modifications to `schema.prisma` to define new tables, fields, or relations.
        *   Running `npx prisma migrate dev` will automatically generate new migration files in `migrations/`.
        *   Updates or additions to the seed script.
    *   The generated Prisma Client is imported from `@prisma/client` in your NestJS services.

*   **`src/types/` (Shared or Frontend/Backend Specific)**
    *   **Explanation:** TypeScript type definitions and interfaces that might be shared across the application (frontend/backend if applicable via path aliases or as a separate package in a true monorepo) or are specific to either side but centrally located.
    *   **Where New Additions Should Go:** New shared type definitions or complex types that benefit from central definition.

## 5. Root Directory (`./`)

*   **Explanation:**
    *   Contains project-level configuration files, scripts, and essential project identifiers.
    *   Examples:
        *   `package.json`: Project dependencies, scripts (e.g., for starting dev servers, building, testing).
        *   `package-lock.json` or `yarn.lock` or `pnpm-lock.yaml`: Exact dependency versions.
        *   `next.config.js`: Next.js configuration.
        *   `nest-cli.json`: NestJS CLI configuration.
        *   `tsconfig.json` (often one root, potentially extended by `src/tsconfig.json` or separate ones for frontend/backend if needed for strictness).
        *   `.env`, `.env.local`, `.env.production`: Environment variable files (ensure `.env*.local` are in `.gitignore`).
        *   `.gitignore`: Specifies intentionally untracked files that Git should ignore.
        *   `README.md`: Top-level project overview, setup instructions, and quick start guide.
        *   `prettierrc.js` or `.prettierrc.json`: Prettier formatting configuration.
        *   `eslintrc.js` or `.eslintrc.json`: ESLint linting configuration.
        *   CI/CD configuration (e.g., `.github/workflows/`, `gitlab-ci.yml`, `Jenkinsfile`).
        *   `docker-compose.yml`, `Dockerfile` (if using Docker for development or deployment).
*   **Where New Additions Should Go:**
    *   New top-level configuration files for tools (e.g., testing frameworks, linters).
    *   New NPM scripts in `package.json`.
    *   CI/CD pipeline definitions.
    *   Docker configurations.

    -

# Project Rules: API Authentication & Validation Guidelines

    ## 1. Goal
The primary goal of these guidelines is to ensure consistent, secure, and robust API authentication, authorization, and data validation practices across the YoungMobility platform. Adherence to these rules will improve security, reduce bugs, enhance developer experience, and maintain data integrity.

    ## 2. Core Principles
*   **Security First:** Authentication and authorization mechanisms must be robust, preventing unauthorized access and actions. All sensitive data in transit must be encrypted (HTTPS).
*   **Defense in Depth:** Implement multiple layers of security controls. Do not rely on a single point of security.
*   **Explicit is Better Than Implicit:** Authentication requirements and validation rules should be clearly defined and enforced.
*   **Validate Early, Validate Often:** Input data must be validated at the earliest possible point (ideally at the API gateway/controller level) and potentially re-validated at critical business logic stages if necessary.
*   **Principle of Least Privilege:** Users and services should only have access to the resources and actions necessary to perform their legitimate tasks.
*   **DRY (Don't Repeat Yourself):** Use shared DTOs, guards, and pipes to avoid redundant validation and authentication logic.
*   **Clear Error Reporting:** API responses for authentication failures or validation errors must be clear, consistent, and provide enough information for the client to understand the issue without exposing sensitive system details.

    ## 3. Mandatory Backend Implementation Pattern (Defining API Endpoints in NestJS)

### 3.1. Authentication
*   **Mechanism:** JSON Web Tokens (JWT) will be used for stateless authentication.
*   **JWT Generation:** Upon successful login, the `AuthService` will generate a JWT containing essential, non-sensitive user claims (e.g., `userId`, `role`, `email`).
*   **JWT Protection:**
    *   Access tokens should have a short expiry time (e.g., 15-60 minutes).
    *   Refresh tokens (if implemented) should be stored securely (e.g., HttpOnly cookies or a secure backend store) and used to obtain new access tokens.
*   **Guards:**
    *   All protected API endpoints **MUST** be decorated with the `@UseGuards(JwtAuthGuard)` (a custom guard extending `AuthGuard('jwt')`).
    *   The `JwtAuthGuard` will validate the JWT present in the `Authorization: Bearer <token>` header.
    *   Unauthenticated requests to protected routes **MUST** return a `401 Unauthorized` HTTP status code.

### 3.2. Authorization (Role-Based Access Control - RBAC)
*   **Mechanism:** Roles will be assigned to users (e.g., 'business-client', 'driver', 'admin').
*   **Guards & Decorators:**
    *   A custom `RolesGuard` **MUST** be used to check if the authenticated user has the required role(s) to access an endpoint.
    *   A custom `@Roles(Role.Admin, Role.BusinessClient)` decorator will specify the roles allowed for an endpoint.
    *   Example: `@UseGuards(JwtAuthGuard, RolesGuard) @Roles(Role.Admin)`
*   **Granular Permissions:** For more complex authorization beyond simple roles, consider attribute-based access control (ABAC) or claim-based authorization, potentially implemented within services or more specific guards.
*   Unauthorized access due to insufficient permissions **MUST** return a `403 Forbidden` HTTP status code.

### 3.3. Data Validation (Input Validation)
*   **Mechanism:** Data Transfer Objects (DTOs) decorated with `class-validator` decorators **MUST** be used for request body, query parameters, and path parameters. `class-transformer` will be used for transforming plain objects to DTO instances.
*   **Global Validation Pipe:**
    *   The NestJS application **MUST** use a global `ValidationPipe` configured with `whitelist: true` and `forbidNonWhitelisted: true` to automatically strip unspecified properties and reject requests with non-whitelisted properties.
    *   `transform: true` should also be enabled to automatically transform incoming payloads to DTO instances.
    *   `app.useGlobalPipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true, transform: true }));`
*   **DTO Structure:**
    *   DTOs should be specific to an API operation (e.g., `CreateOrderDto`, `UpdateUserProfileDto`).
    *   Use appropriate `class-validator` decorators (e.g., `@IsString()`, `@IsEmail()`, `@IsNotEmpty()`, `@IsNumber()`, `@IsEnum()`, `@ValidateNested()`, `@Type()`).
    *   For nested objects, use `@ValidateNested()` and `@Type(() => NestedDto)`.
*   **Error Response:** Validation failures **MUST** return a `400 Bad Request` (or `422 Unprocessable Entity` if preferred for semantic purity of invalid data) HTTP status code with a structured error message detailing the validation errors. NestJS's default `ValidationPipe` handles this well.

### 3.4. Controller and Service Structure
*   **Controllers:** Responsible for request handling, extracting data from requests (validated DTOs, authenticated user from `req.user`), and calling services. Controllers **SHOULD NOT** contain business logic.
*   **Services:** Contain the core business logic. They receive validated data and user context from controllers. Further business-specific validation can occur here if necessary.

    ## 4. Choosing the Right Validation Method

*   **Primary Method (Mandatory): DTOs with `class-validator` and Global `ValidationPipe`.**
    *   **Use For:** Most input validation scenarios including type checks, presence checks, format checks (email, UUID), range checks, enum checks.
    *   **Benefits:** Declarative, easy to read, automatically enforced, integrated with NestJS, good for common validation rules.

*   **Custom ValidationPipes:**
    *   **Use For:** Complex validation logic that cannot be easily expressed with `class-validator` decorators alone, or when a specific transformation is needed before validation that `class-transformer` doesn't cover easily for that specific case. This should be rare.
    *   **Benefits:** Centralized logic for a specific complex validation scenario tied to a parameter or payload.

*   **Service-Level Validation:**
    *   **Use For:**
        *   Business rule validation that depends on the current state of the database (e.g., "is this username already taken?", "does this product ID exist?"). This type of validation often requires database lookups.
        *   Cross-field validation that is too complex for DTO decorators (though some can be handled with custom decorators or `class-validator` advanced features).
        *   Ensuring data integrity *after* initial DTO validation but *before* database persistence, especially when combining or deriving data.
    *   **Implementation:** Throw custom NestJS exceptions (e.g., `BadRequestException`, `NotFoundException`, `ConflictException`) from the service layer.
    *   **Benefits:** Keeps business logic within services, can handle dynamic validation rules.

    ## 5. Mandatory Frontend API Interaction Pattern (Calling APIs from UI - Next.js)

### 5.1. API Client/Service Layer
*   A centralized API client or service layer **MUST** be implemented (e.g., in `src/lib/apiClient.ts` or feature-specific services).
*   This layer will encapsulate `fetch` calls or a library like `axios`.
*   Functions in this layer should be strongly typed for requests and responses.

### 5.2. Authentication (JWT Handling)
*   **Token Storage:**
    *   **Recommended for Web:** JWT access tokens are best managed by the backend and set as `HttpOnly`, `Secure`, `SameSite=Strict` (or `Lax`) cookies. The frontend then doesn't need to manage them directly. The NestJS backend can be configured to expect JWTs from cookies as well as Bearer tokens.
    *   **Alternative (If HttpOnly cookies are not used):** If tokens are sent in the response body, the access token can be stored in memory (e.g., Zustand/Redux store). Refresh tokens (if used) must be handled with extreme care (e.g., rotated, potentially stored in a more secure browser mechanism if absolutely necessary, though server-side HttpOnly cookie is best). **Local Storage or Session Storage are NOT recommended for JWTs due to XSS risks.**
*   **Sending Tokens:** The API client layer must automatically include the JWT in the `Authorization: Bearer <token>` header for all authenticated requests (if not using cookies).
*   **Token Refresh:** If using short-lived access tokens and refresh tokens, the API client layer should implement logic to transparently refresh the access token when it expires (e.g., by intercepting 401 responses).

### 5.3. Data Validation (Client-Side - Optional but Recommended)
*   Client-side validation (e.g., in forms using libraries like `react-hook-form` with Zod/Yup) should be implemented for a better user experience (immediate feedback).
*   **However, client-side validation is NOT a substitute for mandatory backend validation.** The backend MUST always re-validate all incoming data.

### 5.4. Error Handling
*   The API client layer **MUST** provide a consistent way to handle API errors.
*   **401 Unauthorized:** Trigger a logout or redirect to the login page. If token refresh is implemented, attempt refresh first.
*   **403 Forbidden:** Display an appropriate "access denied" message to the user.
*   **400 Bad Request / 422 Unprocessable Entity:** Parse validation errors (if structured in the response) and display them to the user (e.g., inline with form fields).
*   **5xx Server Errors:** Display a generic error message and potentially log the error for diagnostics.
*   Implement loading states and user feedback during API calls (e.g., spinners, notifications).

### 5.5. Using Next.js Features
*   **Server Components & Server Actions:** For data fetching and mutations that can run on the server, leverage Next.js Server Components and Server Actions to call backend services (either the main NestJS API or internal service logic). This can simplify state management and reduce client-side JavaScript. Authentication context needs to be passed or accessible.
*   **Route Handlers (Next.js API Routes):** Can be used as a Backend-For-Frontend (BFF) layer to call the main NestJS API, aggregate data, or handle frontend-specific concerns, especially if direct NestJS calls from client components are too complex or expose too much.

    ## 6. Server-to-Server API Calls (Internal Backend Communication)

### 6.1. Intra-Service Communication (Within the same NestJS Monolith)
*   **Primary Method:** Direct service injection and method calls. NestJS's dependency injection system should be leveraged.
*   Example: `OrderService` calling `UserService` or `NotificationService`.
*   Authentication and authorization are typically handled at the entry point (controller) before internal service calls. Internal service methods generally trust that the calling service has already passed through necessary auth checks if called as part of an external request flow.

### 6.2. Inter-Service Communication (If YoungMobility evolves to Microservices)
*   If the YoungMobility backend evolves into multiple, separately deployed microservices:
    *   **Authentication:** Secure communication between services is paramount.
        *   **Recommended:** mTLS (mutual TLS) for transport layer security and service identity verification.
        *   Alternatively, service-to-service JWTs or API keys, where each service acts as a client to another. These tokens should have specific, narrow scopes.
    *   **Service Discovery:** Use a service discovery mechanism (e.g., Consul, Kubernetes DNS).
    *   **Resiliency:** Implement patterns like retries, circuit breakers (e.g., using a library like `@nestjs/terminus` or resilience4j concepts) to handle transient failures in inter-service communication.
    *   **Asynchronous Communication:** For non-blocking operations or decoupling services, consider message queues (e.g., RabbitMQ, Kafka, AWS SQS/SNS).

### 6.3. Communication with External Trusted Services (e.g., Payment Gateway, Email Service)
*   **Authentication:** Use the specific authentication mechanism required by the third-party service (e.g., API Keys, OAuth2 client credentials).
*   Store API keys and secrets securely using environment variables and a secret management system (e.g., HashiCorp Vault, AWS Secrets Manager, Doppler). **Never hardcode secrets.**
*   Calls to these services should be encapsulated within dedicated NestJS services (e.g., `PaymentService`, `EmailNotificationService`).

-

# Codebase Commenting Guidelines

## 1. Philosophy

*   **Clarity Over Brevity:** Comments should make the code easier to understand. If the code is complex or non-obvious, a comment is warranted.
*   **"Why" not "What":** Good comments explain *why* the code is written a certain way, its purpose, or the business logic it implements, not just *what* it does (which should be evident from well-written code itself).
*   **Code as the Primary Source of Truth:** Strive to write self-documenting code. Comments should supplement, not duplicate or contradict, the code.
*   **Maintainability:** Comments must be maintained alongside the code. Outdated or misleading comments are worse than no comments.
*   **Consistency:** Follow these guidelines consistently across the entire codebase to ensure uniformity and predictability.

## 2. Core Standard: JSDoc

For all JavaScript and TypeScript code (Frontend with Next.js/React, Backend with NestJS), **JSDoc** is the standard for documenting functions, classes, methods, types, and complex logic.

*   **Benefits of JSDoc:**
    *   Provides rich type information and descriptions that IDEs (like VS Code) can use for IntelliSense, autocompletion, and type checking, especially in JavaScript files.
    *   Can be used to generate API documentation (e.g., using tools like TypeDoc for TypeScript or JSDoc itself).
    *   Improves code readability and maintainability.
    *   Facilitates easier onboarding for new developers.

## 3. When to Comment

Comments are necessary in the following situations:

*   **Public API Documentation (Essential):**
    *   Every exported function, class, method, constant, and type definition that forms a public or module-level API.
    *   All NestJS Controllers, Services, Modules, Guards, Pipes, Interceptors.
    *   All React components (especially shared ones), custom hooks, and context providers.
    *   Utility functions.

*   **Complex Logic:**
    *   Code blocks that implement complex algorithms, business rules, or non-obvious workflows.
    *   Workarounds for bugs or limitations in external libraries.
    *   Optimizations that might make the code harder to read at first glance.

*   **Non-Obvious Decisions or Intent:**
    *   When the reason for a particular implementation detail isn't immediately clear from the code itself.
    *   To explain the trade-offs made for a particular solution.

*   **Important Assumptions or Preconditions:**
    *   If a function relies on specific preconditions or assumptions about its input or the system state that are not enforced by types alone.

*   **Marking Areas for Future Work:**
    *   `// TODO:` For tasks that need tobe done. Include a brief description and optionally a reference (e.g., JIRA ticket ID or GitHub issue #).
        *   *Example:* `// TODO: (YM-123) Refactor this to use the new PricingService.`
    *   `// FIXME:` For known bugs that need to be fixed. Include a brief description of the problem.
        *   *Example:* `// FIXME: This calculation is off by one under certain conditions.`
    *   `// HACK:` For temporary or less-than-ideal solutions. Explain why it's a hack and what the proper solution would be.

*   **Explaining Regular Expressions:**
    *   Complex regular expressions should always be accompanied by a comment explaining their parts and purpose.

*   **Deprecation Notices:**
    *   Use the `@deprecated` JSDoc tag to mark functions or features that are no longer recommended for use and will be removed in a future version. Include a reason and suggest alternatives.

## 4. How to Comment with JSDoc

### 4.1. Basic Structure
JSDoc comments start with `/**` and end with `*/`. Each line within the block usually starts with an asterisk `*`.

```typescript
/**
 * A brief description of what the function, class, or variable does.
 *
 * This can be followed by a more detailed explanation if necessary,
 * spread across multiple lines.
 *
 * @module MyModule (Optional: if part of a larger logical module)
 */
```

### 4.2. Documenting Functions and Methods

```typescript
// Define a type for Order for the example
type OrderItemData = { price: number; quantity?: number };
type Order = { items: OrderItemData[]; customerId?: string; isValid?: () => boolean };

// Example of a function used within another
function orderIsValid(order: Order): boolean {
  if (!order || !order.items || order.items.length === 0) return false;
  return order.items.every(item => typeof item.price === 'number' && item.price >= 0);
}

/**
 * Calculates the total price of an order, including taxes and discounts.
 * This function assumes that the order items have valid prices.
 *
 * @param {Order} order - The order object to calculate the total for.
 * @param {number} [taxRate=0.19] - The applicable tax rate (e.g., 0.19 for 19%). Defaults to 19%.
 * @param {string[]} [appliedDiscountCodes=[]] - An array of discount codes to apply.
 * @returns {Promise<number>} A promise that resolves to the calculated total price.
 * @throws {Error} If the order object is invalid or missing required item prices.
 * @async
 *
 * @example
 * const order = { items: [{ price: 100 }, { price: 50 }] };
 * calculateTotalPrice(order, 0.07)
 *   .then(total => console.log(total)) // Example output might be 160.5 (150 * 1.07)
 *   .catch(error => console.error(error));
 */
async function calculateTotalPrice(order: Order, taxRate: number = 0.19, appliedDiscountCodes: string[] = []): Promise<number> {
  if (!orderIsValid(order)) {
    throw new Error("Invalid order data provided or items missing prices.");
  }

  let subtotal = 0;
  for (const item of order.items) {
    subtotal += item.price * (item.quantity || 1); // Assume quantity is 1 if not specified
  }

  // Apply discounts (simplified example)
  if (appliedDiscountCodes.includes('SAVE10')) {
    subtotal *= 0.9;
  }

  const totalPrice = subtotal * (1 + taxRate);
  return totalPrice;
}
```

*   **First line:** A concise summary.
*   `@param {TypeName} parameterName - Description.`
    *   Use `{TypeName}` for types. For TypeScript, types are often inferred but explicitly stating them in JSDoc can be helpful for generated documentation or complex types.
    *   For optional parameters: `[parameterName]` or `[parameterName=defaultValue]`.
*   `@returns {TypeName} Description.` (For TypeScript, the return type from the signature is usually sufficient, but the description is valuable).
*   `@throws {ErrorType} Description of when the error is thrown.`
*   `@async` If the function is asynchronous (returns a Promise). Though `async` in the signature implies this.
*   `@deprecated {version} [description]` To mark as deprecated.
*   `@example` Provide one or more usage examples.
*   `@see {@link OtherFunction}` or `@see {@link module:ModuleName.OtherFunction}` to link to other documented parts.
*   `@remarks` For additional notes or detailed explanations beyond the main description.

### 4.3. Documenting Classes and Interfaces/Types (TypeScript)

```typescript
// Base class for example
class BaseEntity {
  protected createdAt: Date;
  constructor() {
    this.createdAt = new Date();
  }
}

// For NestJS example
class UnauthorizedException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'UnauthorizedException';
  }
}


/**
 * Represents a user in the YoungMobility system.
 * Each user can have one or more roles.
 *
 * @class User
 * @extends BaseEntity
 */
class User extends BaseEntity {
  /**
   * The unique identifier for the user.
   * @public
   * @readonly
   * @type {string}
   */
  public readonly id: string;

  /**
   * The user's email address. Must be unique.
   * @public
   * @type {string}
   */
  public email: string;

  /**
   * Indicates if the user's account is active.
   * @public
   * @type {boolean}
   * @default true
   */
  public isActive: boolean = true;

  /**
   * Creates an instance of User.
   * @param {string} id - The user's ID.
   * @param {string} email - The user's email.
   */
  constructor(id: string, email: string) {
    super();
    this.id = id;
    this.email = email;
  }

  // Methods are documented like functions (see above)
}

/**
 * Defines the structure for an order item.
 * @interface OrderItem
 */
interface OrderItem {
  /** The unique ID of the product. */
  productId: string;
  /** The quantity of this item. Must be a positive integer. */
  quantity: number;
  /** The price per unit at the time of order. */
  unitPrice: number;
}

/**
 * Represents the different roles a user can have.
 * @enum {string}
 */
export enum UserRole {
  /** Administrator with full access. */
  Admin = 'admin',
  /** Business client who creates orders. */
  Client = 'client',
  /** Driver who fulfills orders. */
  Driver = 'driver',
}
```

*   For classes, describe the class's purpose. `@class ClassName` tag.
*   For properties, describe their purpose. `@type {TypeName}` can be used, but TypeScript types are preferred. Use `@public`, `@private`, `@protected`, `@readonly` JSDoc tags if needed to clarify visibility beyond TypeScript keywords for documentation generation, though TypeScript keywords are primary.
*   Constructors are documented like functions.
*   For interfaces and type aliases, describe their purpose. `@interface InterfaceName` or `@typedef {Object} TypeName` can be used, but TypeScript syntax is primary.
*   For enums, use `@enum {UnderlyingType}` and document each member.

### 4.4. Inline Comments
For short, single-line explanations within a function body, use `//`.

```typescript
// Dummy function for example
const calculateDistance = (p1: any, p2: any) => 100;
const user = new User("1", "<EMAIL>"); // Example User instance

// Ensure the user is active before proceeding
if (!user.isActive) {
  throw new UnauthorizedException('User account is not active.');
}

const pointA = { lat: 0, lon: 0 };
const pointB = { lat: 1, lon: 1 };
// This calculation is based on the Haversine formula for distance
const distance = calculateDistance(pointA, pointB); // See geoUtils.ts for formula
```
*   Place inline comments on a new line above the code they refer to, or at the end of a line if very short and directly related.
*   Keep them concise.

### 4.5. What NOT to Comment
*   **Obvious Code:** Don't comment on code that is perfectly clear and self-explanatory.
    *   *Bad:* `// Increment i` followed by `let i = 0; i++;`
*   **Version Control Information:** Don't put commit history, author names, or dates in comments. Git handles this.
*   **Disabled Code:** Instead of commenting out large blocks of code, remove them. Version control can always bring them back. If it's temporarily disabled for a good reason, use `// TODO: Re-enable (reason) once YM-XXX is resolved.`
*   **Bad Code Excuses:** Don't write comments to excuse poorly written code. Fix the code instead. If it's a necessary "hack", explain *why* (see HACK above).

-

# Essential Hook Guidelines

## 1. Essential Frontend Hook: `useApi`

This guideline introduces the `useApi` hook, a foundational custom hook for managing asynchronous API calls within our Next.js/React frontend.

**Purpose:**
*   To provide a standardized and reusable way to handle the common states of an API request: `loading`, `data`, and `error`.
*   To encapsulate the logic for initiating an API call and updating the UI based on its outcome.
*   To reduce boilerplate code in components when fetching or submitting data.
*   To improve consistency in how API interactions are handled across the application.

**When to Use:**
*   Whenever a component needs to fetch data from the backend API.
*   When a component needs to submit data to the backend API (e.g., form submissions) and react to the response.
*   For any asynchronous operation where you want to manage loading and error states gracefully.

This hook is designed for manual invocation of the API call (e.g., on a button click, or triggered by an effect). For more advanced data fetching scenarios involving caching, automatic re-fetching, pagination, etc., consider using dedicated libraries like SWR or React Query if they are introduced into the project later. For now, `useApi` provides an essential building block.

## 2. The Essential Hook: `useApi.ts`

This is the reference implementation for the `useApi` hook. It should be placed in `src/hooks/useApi.ts`.

```typescript
import { useState, useCallback, useRef }部落格 is not needed } from 'react';

/**
 * Represents the state managed by the useApi hook.
 * @template T The type of the data returned by the API call.
 */
interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

/**
 * A custom hook to manage the state of an API call (loading, data, error).
 *
 * @template T The expected type of the data returned by the successful API call.
 * @template P The type of the arguments array that the `apiCall` function accepts.
 * @param apiCall A function that performs the asynchronous API request and returns a Promise of type T.
 *                This function will receive arguments passed to the `execute` function.
 * @returns An object containing:
 *  - `data`: The data returned from the API, or null if not yet fetched or if an error occurred.
 *  - `loading`: A boolean indicating if the API call is currently in progress.
 *  - `error`: An Error object if the API call failed, otherwise null.
 *  - `execute`: A function to manually trigger the API call. It accepts the same arguments as the original `apiCall` function.
 *               It returns the promise from the apiCall.
 *  - `reset`: A function to reset the hook's state (data, loading, error) to its initial values.
 */
function useApi<T, P extends any[] = []>(
  apiCall: (...args: P) => Promise<T>
) {
  const initialState: UseApiState<T> = {
    data: null,
    loading: false,
    error: null,
  };

  const [state, setState] = useState<UseApiState<T>>(initialState);

  // Using a ref to ensure the latest apiCall function is used in the useCallback
  // without causing re-creation of 'execute' if apiCall changes unnecessarily.
  // This is important if apiCall is defined inline in the component.
  // For stable apiCall functions (e.g., imported from an API client module), this is less critical.
  const apiCallRef = useRef(apiCall);
  apiCallRef.current = apiCall;


  const execute = useCallback(async (...args: P): Promise<T> => {
    setState(prevState => ({ ...prevState, loading: true, error: null }));
    try {
      const result = await apiCallRef.current(...args);
      setState({ data: result, loading: false, error: null });
      return result;
    } catch (err: unknown) {
      // Ensure err is an Error instance
      const errorInstance = err instanceof Error ? err : new Error(String(err || 'An unknown error occurred'));
      setState({ data: null, loading: false, error: errorInstance });
      throw errorInstance; // Re-throw for component-level specific error handling if needed
    }
  }, []); // No dependencies needed here due to ref usage for apiCall

  const reset = useCallback(() => {
    setState(initialState);
  }, [initialState]); // initialState is stable unless the generic T changes, which means a new hook instance

  return { ...state, execute, reset };
}

export default useApi;
content_copy
download
Use code with caution.
Markdown
```

## 3. Usage Reminder
Here's how to use the useApi hook in your components:

3.1. Importing and Instantiating
// src/components/MyComponent.tsx
import React, { useEffect } from 'react';
import useApi from '@/hooks/useApi'; // Adjust path as necessary
import apiClient from '@/lib/apiClient'; // Your API client (see API Interaction Guidelines)

// Define a type for the expected API response
interface UserProfile {
  id: string;
  name: string;
  email: string;
}

const MyComponent: React.FC<{ userId: string }> = ({ userId }) => {
  // 1. Define the API call function
  //    This function should use your apiClient.
  const fetchUserProfile = async (id: string): Promise<UserProfile> => {
    // Example using a generic apiClient.get method
    // Replace with your actual API client call
    return apiClient.get<UserProfile>(`/users/${id}`);
  };

  // 2. Instantiate the hook with the API call function
  const { data: userProfile, loading, error, execute: loadProfile, reset } = useApi<UserProfile, [string]>(fetchUserProfile);

  // 3. Trigger the API call (e.g., on mount, or on a button click)
  useEffect(() => {
    if (userId) {
      loadProfile(userId)
        .catch(err => {
          // Optional: specific error handling in component if `useApi` re-throws
          console.error("Failed to load profile in component:", err.message);
        });
    }

    // Optional: Reset state on component unmount or when userId changes significantly
    return () => {
      // reset(); // Uncomment if you want to clear state on unmount/dependency change
    };
  }, [userId, loadProfile, reset]); // Add execute and reset to dependencies if used in effect

  const handleRefresh = () => {
    reset(); // Clear previous state
    loadProfile(userId)
      .catch(err => console.error("Refresh failed:", err.message));
  };

  // 4. Render UI based on loading, error, and data states
  if (loading) {
    return <div>Loading profile...</div>;
  }

  if (error) {
    return (
      <div>
        <p>Error loading profile: {error.message}</p>
        <button onClick={handleRefresh}>Try Again</button>
      </div>
    );
  }

  if (!userProfile) {
    // Can happen if not yet loaded, or successfully loaded but data is null (though typically data would be T or Error)
    // Or if you called reset()
    return <div>No profile data available. <button onClick={handleRefresh}>Load Profile</button></div>;
  }

  return (
    <div>
      <h1>{userProfile.name}</h1>
      <p>Email: {userProfile.email}</p>
      <p>ID: {userProfile.id}</p>
      <button onClick={handleRefresh} disabled={loading}>
        {loading ? 'Refreshing...' : 'Refresh Profile'}
      </button>
    </div>
  );
};

export default MyComponent;
content_copy
download
Use code with caution.
TypeScript
Key Points for Usage:
Define apiCall Function: Create an async function that takes necessary parameters and returns a Promise of your expected data type. This function will typically use your shared apiClient (from src/lib/apiClient.ts or similar).
Instantiate useApi: Pass your apiCall function to useApi. Specify the expected data type and the parameters type for the apiCall.
execute Function: Call the returned execute function to trigger the API request. Pass any arguments required by your apiCall function to execute.
reset Function: Call the returned reset function to clear the hook's state (data, loading, error) back to their initial values. This is useful before re-fetching or when navigating away.
Handle States: Always check loading and error states in your component to render appropriate UI (e.g., loading indicators, error messages).
Typed: Ensure your apiCall function and the useApi instantiation are correctly typed to leverage TypeScript's benefits.
Error Propagation: The execute function in useApi re-throws the error by default. This allows components to perform additional specific error handling in a .catch() block if needed, beyond just displaying the error message from the hook's state.
Dependencies in useEffect: If you call execute or reset within a useEffect hook, ensure they are included in the dependency array. The useCallback within useApi helps stabilize these functions.
content_copy
download
Use code with caution.

-

# Logging Guidelines

## 1. Introduction

Effective logging is crucial for monitoring application health, diagnosing issues, understanding user behavior, and ensuring security. These guidelines provide a framework for consistent and meaningful logging practices across the YoungMobility platform, covering both server-side (NestJS backend) and client-side (Next.js frontend) logging.

**Goals of these Guidelines:**
*   Ensure logs are informative, structured, and actionable.
*   Standardize logging levels and formats.
*   Enable efficient searching, filtering, and analysis of logs.
*   Balance logging detail with performance considerations and cost of log storage/processing.
*   Maintain security and privacy by avoiding logging of sensitive personal information (PII) unless explicitly required, masked, and handled according to data protection regulations (GDPR).

## 2. General Logging Principles

*   **Structured Logging:**
    *   All logs **MUST** be in a structured format, preferably JSON. This allows for easy parsing, filtering, and querying by log management systems.
    *   NestJS backend logs (using Winston/Pino) will naturally adhere to this.
    *   Client-side logs sent to a remote service (e.g., Sentry) will also be structured.

*   **Log Levels (Semantic):** Use appropriate log levels to indicate the severity and nature of the log message.
    *   **`error`**: Critical errors that prevent normal operation or indicate a significant failure (e.g., unhandled exceptions, failed database connections, critical business logic failure). Action is usually required.
    *   **`warn`**: Potentially harmful situations or unexpected behavior that do not (yet) cause a system failure but might lead to errors if not addressed (e.g., deprecated API usage, resource limits approached, non-critical retriable errors).
    *   **`info`**: General operational information confirming the application is working as expected (e.g., service startup, successful completion of significant background tasks, important user actions like login/logout, order creation).
    *   **`http`** (Backend specific): HTTP request/response logging (method, URL, status code, duration). Often handled by dedicated middleware.
    *   **`verbose`**: Extended information, more granular than `info`. Generally not enabled in production unless actively debugging a specific issue.
    *   **`debug`**: Fine-grained information useful for developers during active debugging. **SHOULD NOT** be enabled in production by default.
    *   **`silly`**: The most granular level, for extreme debugging. Almost never used in production.

*   **Content of Log Messages:**
    *   **Be Concise but Informative:** Provide enough context to understand the event without being overly verbose.
    *   **Context is Key:** Include relevant identifiers such as `userId`, `orderId`, `traceId` (for distributed tracing), `sessionId`, `requestId`, etc. This is crucial for correlating logs and debugging issues.
    *   **Avoid Sensitive Data:** **DO NOT** log sensitive personal information (PII) like passwords, full credit card numbers, social security numbers, API keys, or raw access tokens directly in logs. If necessary for audit or specific debugging, mask or tokenize such data. Consider GDPR implications.
    *   **Timestamps:** All log entries **MUST** have an accurate timestamp (UTC is preferred for consistency). Log management systems usually add this.
    *   **Source Information:** Include module, class, and function name where the log originated if possible (often handled by the logging library).

*   **Performance:** While logging is important, excessive or inefficient logging can impact application performance.
    *   Avoid logging within tight loops or performance-critical code paths unless absolutely necessary.
    *   Use asynchronous logging where possible, especially for I/O-bound operations like writing to files or sending logs to a remote service. Logging libraries often handle this.

*   **Log Management System:**
    *   All production logs (server-side and critical client-side errors) **MUST** be aggregated into a centralized log management system (e.g., ELK Stack, AWS CloudWatch Logs, Google Cloud Logging, Datadog, Sentry). This allows for searching, alerting, and analysis.

## 3. Server-Side Logging (NestJS - `getServerLogger`)

The NestJS backend will use a robust logging library like **Winston** or **Pino**, configured for structured JSON logging. NestJS's built-in `LoggerService` can be extended or replaced with a custom implementation using these libraries. We'll define a utility to get a logger instance with context.

**Implementation (`src/server/common/logger/getServerLogger.ts`):**

```typescript
import { LoggerService, Injectable, Scope, Inject } from '@nestjs/common';
import * as winston from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston'; // Assuming nest-winston is used

/**
 * Represents a contextualized logger.
 * Use this interface when injecting the logger into services or controllers.
 */
export interface IServerLogger extends LoggerService {
  setContext(context: string): void;
  // Add any other custom methods if needed
}

/**
 * Factory function to create a pre-configured Winston logger instance
 * or retrieve one from a NestJS DI context if available.
 *
 * This function allows creating loggers outside of NestJS DI if needed,
 * but primarily designed to work with DI via a custom provider.
 *
 * For most cases, inject `LoggerService` or a custom `IServerLogger`
 * directly into your NestJS providers and controllers.
 * The actual Winston instance is typically configured at the AppModule level.
 *
 * This example assumes you've configured nest-winston or a similar setup
 * in your main application module (AppModule).
 */

// Default configuration for Winston (can be centralized)
const defaultWinstonConfig = {
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }), // Log stack traces for errors
    winston.format.splat(),
    winston.format.json() // Structured JSON logging
  ),
  defaultMeta: { service: 'youngmobility-backend' }, // Default metadata
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize({ all: process.env.NODE_ENV !== 'production' }),
        winston.format.simple() // For console, simple format might be more readable during dev
      ),
      stderrLevels: ['error'], // Log errors to stderr
    }),
    // Add other transports for production (e.g., File, HTTP to log management service)
    // For production, a console transport might write to stdout which is then collected by the hosting platform.
  ],
};

let globalLogger: winston.Logger;

function getGlobalWinstonInstance(): winston.Logger {
  if (!globalLogger) {
    globalLogger = winston.createLogger(defaultWinstonConfig);
  }
  return globalLogger;
}


/**
 * Creates or retrieves a contextualized server-side logger.
 * In a NestJS context, this would typically be a wrapper around an injected logger.
 *
 * @param context - The context string (e.g., 'AuthService', 'OrdersController').
 * @param injectedLogger - (Optional) An existing logger instance, typically injected by NestJS.
 * @returns A logger instance with the specified context.
 */
export function getServerLogger(context?: string, injectedLogger?: winston.Logger): IServerLogger {
  const logger = injectedLogger || getGlobalWinstonInstance(); // Use injected or global

  const contextualLogger = {
    currentContext: context || 'Application',
    setContext(newContext: string) {
      this.currentContext = newContext;
    },
    log(message: any, logContext?: string, ...optionalParams: any[]) {
      logger.info(message, { context: logContext || this.currentContext, ...optionalParams[0] });
    },
    error(message: any, trace?: string, logContext?: string, ...optionalParams: any[]) {
      // Winston handles error objects well, including stack traces.
      logger.error(message, { context: logContext || this.currentContext, trace, ...optionalParams[0] });
    },
    warn(message: any, logContext?: string, ...optionalParams: any[]) {
      logger.warn(message, { context: logContext || this.currentContext, ...optionalParams[0] });
    },
    debug(message: any, logContext?: string, ...optionalParams: any[]) {
      if (logger.isLevelEnabled('debug')) {
        logger.debug(message, { context: logContext || this.currentContext, ...optionalParams[0] });
      }
    },
    verbose(message: any, logContext?: string, ...optionalParams: any[]) {
      if (logger.isLevelEnabled('verbose')) {
        logger.verbose(message, { context: logContext || this.currentContext, ...optionalParams[0] });
      }
    },
    // Add http if needed, or rely on dedicated middleware
  };

  return contextualLogger;
}

// Example NestJS Custom Logger Provider (for DI)
// This makes `IServerLogger` injectable.
@Injectable({ scope: Scope.TRANSIENT }) // Transient scope for context
export class ContextualServerLogger implements IServerLogger {
  private loggerInstance: IServerLogger;
  private currentContextName?: string;

  constructor(@Inject(WINSTON_MODULE_PROVIDER) private readonly winstonLogger: winston.Logger) {
      this.loggerInstance = getServerLogger(undefined, this.winstonLogger);
  }

  setContext(context: string) {
      this.currentContextName = context;
      this.loggerInstance.setContext(context); // Also sets it on the wrapped instance
  }

  // Delegate methods, adding context if available
  log(message: any, context?: string, ...optionalParams: any[]) {
      this.loggerInstance.log(message, context || this.currentContextName, ...optionalParams);
  }
  error(message: any, trace?: string, context?: string, ...optionalParams: any[]) {
      this.loggerInstance.error(message, trace, context || this.currentContextName, ...optionalParams);
  }
  warn(message: any, context?: string, ...optionalParams: any[]) {
      this.loggerInstance.warn(message, context || this.currentContextName, ...optionalParams);
  }
  debug(message: any, context?: string, ...optional_params: any[]) {
      this.loggerInstance.debug(message, context || this.currentContextName, ...optional_params);
  }
  verbose(message: any, context?: string, ...optional_params: any[]) {
      this.loggerInstance.verbose(message, context || this.currentContextName, ...optional_params);
  }
}
content_copy
download
Use code with caution.
Markdown
Usage in NestJS Services/Controllers:

// src/server/auth/auth.service.ts
import { Injectable, Inject } from '@nestjs/common';
import { IServerLogger } from '../common/logger/getServerLogger'; // Adjust path
// Or use Nest's default Logger if context isn't DI driven in this way:
// import { Logger } from '@nestjs/common';

@Injectable()
export class AuthService {
  // Option 1: Injecting custom logger (if provider is set up)
  constructor(@Inject('SERVER_LOGGER') private readonly logger: IServerLogger) { // Assuming 'SERVER_LOGGER' is your custom provider token
    this.logger.setContext(AuthService.name); // Set context on initialization
  }

  // Option 2: Using NestJS default LoggerService and then perhaps a custom wrapper for features
  // private readonly logger = new Logger(AuthService.name);


  async login(credentials: any): Promise<any> {
    this.logger.info(`Login attempt for user: ${credentials.username}`);
    // ... login logic
    if (true /* login successful */) {
      const userId = 'user123'; // Get actual user ID
      this.logger.info(`User ${userId} logged in successfully.`, { userId, ipAddress: '127.0.0.1' /* get actual IP */ });
      return { token: 'some.jwt.token' };
    } else {
      this.logger.warn(`Failed login attempt for user: ${credentials.username}`, { reason: 'Invalid credentials' });
      throw new Error('Login failed');
    }
  }
}
content_copy
download
Use code with caution.
TypeScript
Key Server-Side Logging Points:

Contextual Information: Automatically include service/controller context. Manually add specific identifiers like userId, orderId.
Request Logging: Use NestJS middleware (e.g., a custom logging interceptor or morgan) for detailed HTTP request/response logging (URL, method, status code, duration, user-agent).
Error Logging:
All unhandled exceptions MUST be caught by a global exception filter and logged with error level, including the full stack trace.
Log caught exceptions appropriately (e.g., an error during a third-party API call might be a warn if retriable, or error if it breaks a core flow).
Configuration: Log levels and output destinations (console, file, remote service) MUST be configurable via environment variables. Production should typically log at info or warn, while development can use debug.
4. Client-Side Logging (Next.js/React Frontend)
Client-side logging helps debug UI issues, track user interactions leading to errors, and understand frontend performance.

Primary Tool: Sentry (or similar error reporting service)

Sentry is excellent for capturing and reporting unhandled JavaScript errors, providing stack traces, browser context, and user session information.
It should be configured in _app.tsx (or src/app/layout.tsx for App Router) to initialize as early as possible.
Manual Logging to Console (Development):

Use console.log(), console.warn(), console.error(), console.debug() judiciously during development.
These logs SHOULD generally be removed or disabled in production builds to avoid cluttering the browser console and potential minor performance impact, unless they are intended for end-user visible debugging (very rare). Tools like babel-plugin-transform-remove-console can automate this.
Sending Logs to Backend/Sentry (Production):

Errors: All unhandled errors are automatically captured by Sentry. For caught errors that are significant but handled gracefully, you can manually report them:
// Example: src/components/MyFormComponent.tsx
import * as Sentry from '@sentry/nextjs';

async function handleSubmit(data: any) {
  try {
    // ... API call
  } catch (error) {
    console.error("Form submission failed:", error);
    Sentry.captureException(error, {
      tags: { component: "MyFormComponent" },
      extra: { formData: data }, // Be careful with PII in `extra`
      user: { id: 'currentUser123' } // If user is known
    });
    // ... show error message to user
  }
}
content_copy
download
Use code with caution.
TypeScript
Performance Tracing: Sentry (and similar tools) also provide performance monitoring capabilities (e.g., tracking Web Vitals, component render times).
Important User Actions/Events (Audit Trail - If Necessary): For specific, critical user actions that need an audit trail from the frontend perspective, you could implement a lightweight trackEvent function that sends a structured log to a dedicated backend endpoint (which then logs it server-side). This is less common and should be used sparingly due to network overhead.
// Example: src/lib/logger.ts
import * as Sentry from '@sentry/nextjs';

export const clientLogger = {
  info: (message: string, context?: Record<string, any>) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[INFO] ${message}`, context || '');
    }
    // In production, you might send certain info logs as Sentry breadcrumbs or to a specific endpoint
    Sentry.addBreadcrumb({
      category: 'log',
      message: message,
      level: 'info',
      data: context,
    });
  },
  warn: (message: string, context?: Record<string, any>) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[WARN] ${message}`, context || '');
    }
    Sentry.captureMessage(message, { level: 'warning', extra: context });
  },
  error: (error: Error, context?: Record<string, any>) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(`[ERROR] ${error.message}`, context || '', error);
    }
    Sentry.captureException(error, { extra: context });
  },
  // Could add a 'track' method to send specific events to backend
  // track: (eventName: string, properties: Record<string, any>) => { /* ... send to backend ... */ }
};

// Usage:
// import { clientLogger } from '@/lib/logger';
// clientLogger.info("User viewed profile page", { userId: '123' });
// clientLogger.error(new Error("Failed to load settings"), { settingsId: 'abc' });
content_copy
download
Use code with caution.
TypeScript
Key Client-Side Logging Points:

Focus on Errors and Performance: Prioritize logging unhandled exceptions and performance metrics.
User Context in Sentry: Enrich Sentry reports by setting user context (Sentry.setUser()) and adding custom tags/breadcrumbs (Sentry.addBreadcrumb(), Sentry.setTag()).
Source Maps: Ensure source maps are correctly uploaded to Sentry for de-minified stack traces in production.
Avoid PII: Be even more cautious with logging PII on the client-side as it's directly visible in the browser. Sanitize data sent to Sentry if necessary.
Conditional Logging: Use environment variables (process.env.NODE_ENV) to enable more verbose logging only in development.
content_copy
download
Use code with caution.
```

-

# New Feature Guidelines

This document outlines the standard procedure for developing and integrating new features into the YoungMobility frontend application. Adhering to these guidelines ensures consistency, maintainability, and a smooth development workflow.

**Prerequisites:**
*   The feature has been planned, designed (UX/UI mockups available if applicable), and its requirements are understood.
*   Relevant backend API endpoints are defined, developed, or mocked (refer to *API Authentication & Validation Guidelines*).
*   Necessary user stories or tasks are created in the project management tool.

---

## 1. Create Feature Components & Logic

This phase involves building the core UI and business logic for the new feature.

### 1.1. Directory Structure
*   Create a dedicated directory for the new feature under `src/features/YourFeatureName/`.
    *   *Example:* `src/features/OrderManagement/`
*   Within this feature directory, organize your code:
    *   `components/`: Contains React components specific to this feature (e.g., `OrderTable.tsx`, `CreateOrderForm.tsx`).
    *   `pages/` (If using Pages Router for specific sub-routes or if more natural for the feature's internal structure. For App Router, top-level page/layout components go in `src/app/...`). If the feature is a single page in App Router, its main component can be `src/app/dashboard/your-feature-name/page.tsx`.
    *   `hooks/`: Custom React hooks specific to this feature.
    *   `services/` or `api/`: Functions to interact with backend APIs related to this feature (often wrappers around `useApi` or `apiClient`).
    *   `types/`: TypeScript type definitions and interfaces specific to this feature.
    *   `utils/`: Utility functions specific to this feature.
    *   `state/`: If using Zustand or a similar library, feature-specific store slices/modules.

### 1.2. Component Development
*   **Main Page/View Component:** This is the primary component that renders the feature's UI. If using Next.js App Router, this will typically be `src/app/dashboard/your-feature-name/page.tsx`.
*   **Sub-Components:** Break down the UI into smaller, reusable components within `src/features/YourFeatureName/components/`.
*   **Styling:** Use Tailwind CSS utility classes for styling. Adhere to existing design system principles.
*   **State Management:**
    *   Use local component state (`useState`, `useReducer`) for component-specific UI state.
    *   Use custom hooks (`src/features/YourFeatureName/hooks/`) to encapsulate complex stateful logic.
    *   For shared state within the feature or across features, utilize the established global state management solution (e.g., Zustand, Redux Toolkit, React Context).
*   **API Interaction:**
    *   Use the `useApi` hook (from `src/hooks/useApi.ts`) for managing API call states (loading, data, error).
    *   Define API call functions in `src/features/YourFeatureName/services/` or reuse from a central `src/lib/apiClient.ts`.
*   **Accessibility (a11y):** Ensure components are accessible (semantic HTML, ARIA attributes where necessary, keyboard navigation).
*   **Responsiveness:** Ensure the feature's UI is responsive across all target device sizes.

### 1.3. Logic & Utilities
*   Implement any business logic specific to the feature within its services, hooks, or utility functions.
*   Follow TypeScript best practices for strong typing.

### 1.4. Testing
*   Write unit tests for utility functions, custom hooks, and complex component logic using Vitest/Jest and React Testing Library.
*   Write component tests for key UI components to verify rendering and interaction.

---

## 2. Configure Routing & Side Panel Navigation

Once the core components are built, integrate the feature into the application's navigation and routing.

### 2.1. Add Route (Next.js App Router)
*   Create the necessary route segment folder under `src/app/`.
    *   For a feature accessible within a dashboard layout: `src/app/dashboard/your-feature-name/`
*   Inside this folder, create:
    *   `page.tsx`: This file will import and render your feature's main page component.
        ```typescript
        // src/app/dashboard/your-feature-name/page.tsx
        import YourFeatureMainComponent from '@/features/YourFeatureName/components/YourFeatureMainComponent'; // Adjust path
        import { Metadata } from 'next';

        export const metadata: Metadata = {
          title: 'Your Feature Name | YoungMobility',
        };

        export default function YourFeaturePage() {
          return <YourFeatureMainComponent />;
        }
        ```
    *   `layout.tsx` (Optional): If the feature requires a specific sub-layout different from its parent but shared among its own sub-routes. Usually, it will inherit the parent layout (e.g., `src/app/dashboard/layout.tsx`).
    *   `loading.tsx` (Optional): A custom loading UI for this route.
    *   `error.tsx` (Optional): A custom error UI for this route.

### 2.2. Add to Side Panel Navigation
*   Our side panel navigation is likely driven by a configuration array or a dedicated navigation component. Locate this file (e.g., `src/components/layout/DashboardSidebarNav.tsx`, `src/config/navigation.ts`).
*   Add a new entry for your feature. The entry should typically include:
    *   `path`: The route defined in step 2.1 (e.g., `/dashboard/your-feature-name`).
    *   `label`: The display name for the navigation link (e.g., "Order Management").
    *   `icon`: An SVG icon component or icon name (e.g., `<BriefcaseIcon />`, `"orders"`).
    *   `permissions` (Optional): An array of roles or permissions required to see/access this navigation item. This will be used by the navigation component to conditionally render the link.

    *Example (conceptual, adapt to actual implementation):*
    ```typescript
    // src/config/navigation.ts
    export const dashboardNavItems = [
      // ... existing items
      {
        label: 'New Feature X',
        path: '/dashboard/new-feature-x',
        icon: BriefcaseIcon, // Or some icon component
        roles: ['admin', 'business-client'], // Optional: roles that can see this
      },
    ];
    ```
*   Ensure the navigation component correctly uses this configuration to render the new link and that any role-based visibility logic is applied.

### 2.3. Protected Route (If Applicable)
*   If the new feature route requires authentication or specific user roles:
    *   Ensure that the parent layout (e.g., `src/app/dashboard/layout.tsx`) or a middleware correctly enforces authentication.
    *   If role-specific access is needed beyond just showing the link, you might need to check permissions within the `page.tsx` or its layout and potentially redirect or show an "access denied" message. Often, layout components or middleware handle this.

---

## 3. Ensure Rendering & Integration

Verify that the new feature is correctly displayed and integrated within the application.

### 3.1. Verify Layout Integration
*   Navigate to the new feature's URL.
*   Confirm that it renders within the correct application layout (e.g., with the main header, dashboard side panel, footer).
*   Ensure that page titles and any other metadata (`export const metadata` in `page.tsx`) are correctly set and displayed in the browser tab.

### 3.2. Test Navigation
*   Click the new navigation link in the side panel.
*   Verify it correctly navigates to the feature's main page.
*   Test active states: ensure the new navigation link is appropriately highlighted when the user is on the feature's page or its sub-pages.

### 3.3. Props and Data Flow
*   If the feature's main component requires props (e.g., from route parameters if using dynamic routes, or from a parent layout), ensure these are correctly passed and utilized.
*   Verify initial data fetching works as expected when the page loads.

### 3.4. Responsiveness & Cross-Browser Testing
*   Check the feature's appearance and functionality across different screen sizes (desktop, tablet, mobile).
*   Perform basic cross-browser checks (Chrome, Firefox, Safari, Edge).

### 3.5. End-to-End Flow (Manual)
*   Perform a manual walkthrough of the feature's main user flows to catch any obvious bugs or UX issues.
*   If the feature involves forms, test submissions with valid and invalid data.

---

**Final Steps:**
*   Ensure all code adheres to the *Codebase Commenting Guidelines* and *Logging Guidelines*.
*   Create a Pull Request with a clear description of the new feature and changes made.
*   Ensure all automated tests pass in the CI/CD pipeline.
*   Obtain necessary code reviews before merging.

-

# Codebase Styling Guidelines

## 1. Codebase Styling Guidelines inspiriert durch den Airbnb JavaScript Style Guide

This document outlines the styling and formatting conventions to be followed for the YoungMobility codebase. Our goal is to maintain a clean, consistent, and readable codebase that is easy to understand and contribute to. These guidelines are inspired by the principles of the Airbnb JavaScript Style Guide, adapted for our specific tech stack (Next.js, React, Tailwind CSS, TypeScript, NestJS).

---

### I. Tooling & Automation

  **1.1 Adherence to Linters & Formatters**: All code **MUST** conform to the project's configured ESLint and Prettier rules.
    > Why? Automated tooling enforces consistency, catches common errors early, and reduces cognitive load related to formatting, allowing developers to focus on logic. Run linters/formatters before committing.

  **1.2 Configuration**:
    *   ESLint configuration (`.eslintrc.js` or similar) will define code quality rules.
    *   Prettier configuration (`.prettierrc.js` or similar) will define code formatting rules.
    *   These configurations are the single source of truth for automated styling.

---

### II. General Formatting (TypeScript/JavaScript)

  **2.1 Indentation**: Use 2 spaces for indentation.
    > Why? It's a common standard, provides good readability without taking too much horizontal space.

  **2.2 Line Length**: Keep lines of code at a maximum of 120 characters.
    > Why? Improves readability on various screen sizes and prevents excessive horizontal scrolling. Long strings or template literals may be an exception if breaking them significantly harms readability.

  **2.3 Semicolons**: Always use semicolons at the end of statements.
    > Why? Avoids potential issues with Automatic Semicolon Insertion (ASI) and makes statement endings explicit.

  **2.4 Commas**: Use trailing commas for multiline arrays, objects, and function parameters.
    > Why? Leads to cleaner git diffs and makes adding/removing items easier.

    ```typescript
    // good
    const myArray = [
      'item1',
      'item2',
      'item3', // trailing comma
    ];

    // good
    function myFunc(
      param1: string,
      param2: number, // trailing comma
    ) {
      // ...
    }
    ```

  **2.5 Whitespace**:
    *   Place 1 space before the leading brace of blocks (`if () {`, `function () {`).
    *   Set off operators with spaces (`x = y + 5;`).
    *   No space between function name and argument list parentheses (`myFunction()`).
    *   Place 1 space before opening parenthesis in control statements (`if (condition)`).

  **2.6 Blank Lines**:
    *   Use blank lines to separate logical blocks of code for better readability.
    *   Avoid multiple consecutive blank lines.
    *   End files with a single newline character.

  **2.7 Braces**: Use braces with all multiline blocks (`if`, `else`, `for`, `while`). `else` statements should be on the same line as the `if` block's closing brace.

    ```typescript
    // good
    if (condition) {
      // ...
    } else {
      // ...
    }
    ```

---

### III. Naming Conventions

  **3.1 File Names**:
    *   React Components: `PascalCase.tsx` (e.g., `UserProfileCard.tsx`).
    *   Next.js App Router specific files: `lowercase.tsx` (e.g., `page.tsx`, `layout.tsx`, `error.tsx`, `loading.tsx`).
    *   Hooks: `useCamelCase.ts` (e.g., `useApi.ts`).
    *   Utilities, Services, Configs, Types: `camelCase.ts` or `kebab-case.ts` (e.g., `apiClient.ts`, `string-utils.ts`). Be consistent within a category.
    *   NestJS Modules, Controllers, Services, DTOs: `feature-name.module.ts`, `feature-name.controller.ts`, `feature-name.service.ts`, `create-feature.dto.ts`.

  **3.2 Variables & Functions**: Use `camelCase`.
    > Why? Standard JavaScript convention.

    ```typescript
    // good
    const orderCount = 10;
    function calculateTotal() { /* ... */ }
    ```

  **3.3 Classes, Interfaces, Types, Enums**: Use `PascalCase`.
    > Why? Standard convention for type-like constructs and classes.

    ```typescript
    // good
    class ApiService { /* ... */ }
    interface UserProfile { /* ... */ }
    type OrderStatus = 'pending' | 'shipped';
    enum PaymentMethod { CreditCard, PayPal }
    ```

  **3.4 Constants**: Use `UPPER_SNAKE_CASE` for truly global, immutable constants that are exported or widely used. For local, block-scoped constants, `camelCase` (with `const`) is fine.
    > Why? `UPPER_SNAKE_CASE` signals that a value is fixed and globally significant.

    ```typescript
    // good - exported constant
    export const DEFAULT_API_TIMEOUT = 5000;

    // good - local constant
    const maxRetries = 3;
    ```
  **3.5 Boolean Variables/Functions**: Prefix with `is`, `has`, `should`, `can` (e.g., `isActive`, `hasPermission`).

---

### IV. TypeScript Specifics

  **4.1 Explicit Types**: Prefer explicit type annotations for function parameters, return types (especially for exported/public functions), and complex object/variable declarations.
    > Why? Improves code clarity, reduces ambiguity, and helps catch errors early. Inference is acceptable for simple, locally-scoped variables where the type is obvious.

  **4.2 `interface` vs `type`**:
    *   Use `interface` for defining the shape of objects or classes that can be implemented or extended.
    *   Use `type` for utility types, union/intersection types, tuples, or when needing to alias primitive types or use mapped types.
    > Why? `interface` offers declaration merging and better serves OOP principles. `type` is more versatile for other type manipulations.

  **4.3 Utility Types**: Leverage built-in utility types (`Partial`, `Readonly`, `Pick`, `Omit`, etc.) to create new types based on existing ones.
    > Why? Promotes DRY principles and type safety.

  **4.4 `any` and `unknown`**:
    *   Avoid using `any` unless absolutely necessary (e.g., integrating with untyped third-party libraries). Prefer `unknown` for type safety and force type checking/assertion before use.
    > Why? `any` bypasses type checking, negating many of TypeScript's benefits.

---

### V. React & Next.js (Frontend)

  **5.1 JSX Formatting**:
    *   If a component has multiple props, put each prop on a new line, indented.
    *   Use self-closing tags for components without children (`<MyComponent />`).
    *   Wrap multi-line JSX in parentheses.

    ```tsx
    // good
    <UserProfileCard
      user={currentUser}
      onUpdate={handleUpdateProfile}
      showAvatar
    />
    ```

  **5.2 Component Structure**: Maintain a consistent order within component files:
    1.  Imports
    2.  Type/Interface definitions (if component-specific)
    3.  Component definition (`const MyComponent: React.FC<Props> = ({...}) => { ... }`)
        *   State (`useState`, `useReducer`)
        *   Other hooks (`useContext`, `useMemo`, `useCallback`, custom hooks like `useApi`)
        *   Effects (`useEffect`, `useLayoutEffect`)
        *   Helper functions defined within the component scope
        *   Return statement (JSX)
    4.  Styled-components or CSS Modules (if used, though Tailwind is primary)
    5.  `export default MyComponent;`

  **5.3 Conditional Rendering**: Strive for clarity.
    *   Use `&&` for simple conditional rendering of an element.
    *   Use ternary operators (`condition ? <A /> : <B />`) for simple if-else rendering.
    *   For more complex logic, consider extracting it into a variable or a small helper function returning JSX.
    > Why? Keeps JSX clean and readable.

  **5.4 Prop Naming**: Boolean props should be named affirmatively (e.g., `isOpen` instead of `isClosed`).
    > Why? Reads more naturally; `<Modal isOpen />` is clearer than `<Modal isClosed={false} />`.

---

### VI. NestJS (Backend)

  **6.1 Decorator Style**: Apply decorators directly above the construct they decorate, one decorator per line if multiple are used for readability.
    ```typescript
    // good
    @Injectable()
    export class AuthService {
      @ApiOperation({ summary: 'User login' })
      @ApiResponse({ status: 200, description: 'Login successful.' })
      @Post('login')
      async login(@Body() loginDto: LoginDto) {
        // ...
      }
    }
    ```

  **6.2 Module/Controller/Service Organization**: Follow the structure generated by `nest g resource` as a baseline for consistency. Ensure clear separation of concerns between controllers (request/response handling, DTO validation), services (business logic), and modules (grouping related functionality).

  **6.3 DTOs (Data Transfer Objects)**:
    *   Name DTOs clearly, e.g., `CreateUserDto.ts`, `UpdateOrderDto.ts`.
    *   Use `class-validator` decorators for input validation within DTOs.

---

### VII. Tailwind CSS (Styling)

  **7.1 Utility-First**: Embrace the utility-first approach. Apply styling directly in JSX using Tailwind classes.
    > Why? Reduces context switching, keeps styling co-located with markup, and minimizes custom CSS.

  **7.2 Class Ordering & Grouping**: While Prettier plugins for Tailwind can automate this, aim for a logical grouping of classes for better readability (e.g., layout, spacing, typography, background, borders).
    > Why? Makes it easier to scan and understand the applied styles.

  **7.3 `@apply` Sparingly**: Use `@apply` in global CSS (`src/styles/globals.css`) or highly specific component CSS files only for:
    *   Reusing complex combinations of utilities for custom components or base element styling that cannot be easily encapsulated by a React component.
    *   Abstracting sets of utilities that are frequently repeated and make JSX too verbose, but only after considering if a component abstraction is better.
    > Why? Overuse of `@apply` can negate the benefits of utility classes and lead back to writing more traditional CSS. Component abstraction is usually preferred.

  **7.4 Configuration**: Define project-specific design tokens (colors, spacing, typography) in `tailwind.config.js`.
    > Why? Centralizes design system customization and ensures consistency.

  **7.5 Conditional Classes**: Use libraries like `clsx` or `classnames` for conditionally applying Tailwind classes in a clean way.

    ```tsx
    import clsx from 'clsx';

    // good
    <button
      className={clsx(
        'py-2 px-4 rounded font-semibold',
        isActive ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700',
        { 'opacity-50 cursor-not-allowed': isDisabled },
      )}
    >
      Submit
    </button>
    ```

---

### VIII. Comments

  **8.1 Purpose**: Refer to the `Codebase Commenting Guidelines.md` for detailed rules on JSDoc, `// TODO:`, `// FIXME:`, etc.
    > Why? Well-commented code is easier to understand and maintain.

  **8.2 Tailwind Clarity**: For exceptionally long or complex strings of Tailwind utility classes, a short inline comment explaining the overall effect can be helpful if the class names themselves aren't immediately obvious in their combined effect.
    ```tsx
    <div className="flex items-center justify-between p-4 border-b md:p-6 lg:grid lg:grid-cols-3 lg:gap-8"> {/* Main header layout for responsiveness */}
      {/* ... content ... */}
    </div>
    ```

---

### IX. Simplicity & Readability

  **9.1 Avoid Overly Complex Code**: Favor clear, straightforward code over overly clever or complex one-liners that might be difficult for others (or your future self) to understand.

  **9.2 Function/Method Length**: Keep functions and methods short and focused on a single responsibility. If a function becomes too long, consider refactoring it.

  **9.3 Consistency is Key**: While these guidelines provide a framework, the most important principle is consistency. If you're modifying existing code, try to follow the established style in that file or module.

-

# Utility Files & Core Data Access Guidelines

This document outlines guidelines for creating and using utility files, with a specific focus on accessing core data entities, particularly for AI-related character interactions within the YoungMobility platform (though this seems to be a hypothetical extension to the provided project scope, I will outline it as requested).

---

## 1. Utility Files & Core Data Access

### 1.1. Purpose of Utility Files
Utility files serve to encapsulate reusable logic that doesn't belong to a specific component, feature module, or service but is general enough to be used across different parts of the application.
*   **DRY Principle:** Avoid repeating the same logic in multiple places.
*   **Maintainability:** Centralized logic is easier to update and test.
*   **Readability:** Keeps feature/component code cleaner by abstracting common operations.

### 1.2. Location of Utility Files
*   **Global Utilities (Frontend):** `src/lib/utils/` or `src/utils/` (e.g., `dateUtils.ts`, `stringUtils.ts`, `validationUtils.ts`).
*   **Global Utilities (Backend - NestJS):** `src/server/common/utils/` (e.g., `hash.utils.ts`, `pagination.utils.ts`).
*   **Feature-Specific Utilities:** If a utility is only relevant to a single feature, it can reside within that feature's directory (e.g., `src/features/OrderManagement/utils/orderCalculations.ts`).

### 1.3. Naming
*   Utility files should be named descriptively based on their content, often ending with `Utils` (e.g., `formatDate.utils.ts` or grouped like `date.utils.ts`).
*   Functions within utility files should also be descriptively named.

### 1.4. Content & Style
*   Utility functions should generally be **pure functions** where possible (i.e., given the same input, they always return the same output and have no side effects).
*   They should be well-documented, especially if their logic is complex (refer to *Codebase Commenting Guidelines*).
*   Ensure they are thoroughly unit-tested.

---

## 2. AI Core (Hypothetical Extension for YoungMobility)

This section assumes YoungMobility is being extended with AI-driven character interactions, perhaps for a more engaging support bot, a virtual assistant for drivers/clients, or a training simulator.

### 2.1. AI Core Module/Service
*   **Location (Backend):** A dedicated NestJS module, e.g., `src/server/ai-core/`, would house the primary logic for AI interactions.
    *   `ai-core.module.ts`
    *   `ai-core.service.ts` (handles interaction with AI models, personality data retrieval, etc.)
    *   `ai-core.controller.ts` (if direct API interaction with the AI core is needed)
*   **Location (Frontend):** An API service file to interact with the AI Core backend, e.g., `src/lib/apiClient/aiCore.api.ts`.

### 2.2. AI Model Interaction
*   The `AiCoreService` on the backend would be responsible for:
    *   Connecting to and querying an external AI model (e.g., OpenAI GPT, a custom-trained model).
    *   Formatting prompts based on context, user input, and character personality.
    *   Processing responses from the AI model.
*   API keys and sensitive configuration for AI models **MUST** be stored securely using environment variables and secret management.

### 2.3. Data Storage for AI (Hypothetical)
*   **Character Data:** Details about AI characters (name, avatar, base personality traits) might be stored in the main database (PostgreSQL) within a new `AiCharacters` table, managed by Prisma.
*   **Conversation History:** If needed for context, conversation logs with AI characters could be stored (e.g., in the main database or a NoSQL database optimized for text, like MongoDB, if volume is high). Consider GDPR and data retention policies.

---

## 3. Accessing Character Data and Personality (Hypothetical)

This section details how utility functions and services might be structured to access AI character data and their personalities, assuming characters are linked to authenticated users in some way (e.g., a user can create or customize their own AI assistant characters).

### 3.1. Accessing the Authenticated User's Characters

**Scenario:** A logged-in user (Client or Driver) can have one or more associated AI assistant characters.

**Backend (NestJS - `src/server/ai-characters/ai-characters.service.ts`):**
*   This service would interact with the Prisma client to fetch AI characters associated with a given `userId`.

```typescript
// src/server/ai-characters/ai-characters.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@/server/prisma/prisma.service'; // Assuming Prisma setup
import { AiCharacter } from '@prisma/client'; // Generated Prisma type
import { IServerLogger } from '@/server/common/logger/getServerLogger'; // Your logger

@Injectable()
export class AiCharactersService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject('SERVER_LOGGER') private readonly logger: IServerLogger,
  ) {
    this.logger.setContext(AiCharactersService.name);
  }

  /**
   * Retrieves all AI characters associated with a specific user.
   * @param userId The ID of the authenticated user.
   * @returns A promise resolving to an array of AiCharacter objects.
   */
  async findCharactersByUserId(userId: string): Promise<AiCharacter[]> {
    this.logger.log(`Fetching AI characters for user ${userId}`, { userId });
    try {
      const characters = await this.prisma.aiCharacter.findMany({
        where: {
          userId: userId, // Assuming an AiCharacter model with a 'userId' relation
          isActive: true, // Optional: only active characters
        },
        orderBy: {
          name: 'asc',
        },
      });
      return characters;
    } catch (error) {
      this.logger.error(`Error fetching AI characters for user ${userId}: ${error.message}`, error.stack, { userId });
      throw new Error('Could not retrieve AI characters.'); // Or a more specific custom exception
    }
  }
}
content_copy
download
Use code with caution.
Markdown
A corresponding AiCharactersController would expose an endpoint like GET /ai-characters/my-characters that uses this service, leveraging the authenticated user's ID from the JWT.
Frontend (e.g., src/features/AiAssistant/services/character.api.ts):

A function to call the backend endpoint.
// src/features/AiAssistant/services/character.api.ts
import apiClient from '@/lib/apiClient'; // Your central API client
import { AiCharacter } from '@/types/ai'; // Assuming shared types

/**
 * Fetches the authenticated user's AI characters from the backend.
 */
export async function getMyAiCharacters(): Promise<AiCharacter[]> {
  try {
    const response = await apiClient.get<AiCharacter[]>('/ai-characters/my-characters');
    return response.data; // Assuming your apiClient structures data this way
  } catch (error) {
    // Handle error appropriately, maybe re-throw or return empty array
    console.error("Error fetching user's AI characters:", error);
    throw error; // Or a custom error type
  }
}
content_copy
download
Use code with caution.
TypeScript
```

### 3.2. Creating a Dropdown for User's Characters (Frontend)
Scenario: Display a dropdown in the UI to let the user select one of their AI characters.

React Component (e.g., src/features/AiAssistant/components/CharacterSelector.tsx):

// src/features/AiAssistant/components/CharacterSelector.tsx
import React, { useEffect, useState } from 'react';
import useApi from '@/hooks/useApi';
import { getMyAiCharacters } from '../services/character.api';
import { AiCharacter } from '@/types/ai'; // Frontend type for AiCharacter

interface CharacterSelectorProps {
  selectedCharacterId: string | null;
  onCharacterSelect: (characterId: string | null) => void;
  disabled?: boolean;
}

const CharacterSelector: React.FC<CharacterSelectorProps> = ({
  selectedCharacterId,
  onCharacterSelect,
  disabled = false,
}) => {
  const {
    data: characters,
    loading,
    error,
    execute: fetchCharacters,
  } = useApi<AiCharacter[]>(getMyAiCharacters);

  useEffect(() => {
    fetchCharacters().catch(err => console.error("Error in CharacterSelector effect:", err));
  }, [fetchCharacters]);

  if (loading) {
    return <select disabled><option>Loading characters...</option></select>;
  }

  if (error) {
    return <select disabled><option>Error loading characters</option></select>;
  }

  if (!characters || characters.length === 0) {
    return <select disabled><option>No characters available</option></select>;
  }

  return (
    <select
      value={selectedCharacterId || ''}
      onChange={(e) => onCharacterSelect(e.target.value || null)}
      disabled={disabled}
      className="p-2 border rounded" // Example Tailwind styling
    >
      <option value="">-- Select a Character --</option>
      {characters.map((char) => (
        <option key={char.id} value={char.id}>
          {char.name}
        </option>
      ))}
    </select>
  );
};

export default CharacterSelector;
content_copy
download
Use code with caution.
Tsx

### 3.3. Retrieving a Character's Personality String
Scenario: Once a character is selected, you need to fetch or construct its "personality string" to prime an AI model. This personality might be a combination of stored traits and dynamic context.

Backend (NestJS - src/server/ai-core/ai-core.service.ts):

The AiCoreService would be responsible for generating or retrieving this string.
// src/server/ai-core/ai-core.service.ts
// (Assuming this service is part of the ai-core.module.ts)
import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@/server/prisma/prisma.service';
import { IServerLogger } from '@/server/common/logger/getServerLogger';

@Injectable()
export class AiCoreService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject('SERVER_LOGGER') private readonly logger: IServerLogger,
  ) {
    this.logger.setContext(AiCoreService.name);
  }

  /**
   * Constructs or retrieves the personality prompt string for a given AI character.
   * This might involve fetching base traits, user customizations, and recent interactions.
   * @param characterId The ID of the AI character.
   * @param dynamicContext Optional dynamic context (e.g., current user query).
   * @returns A promise resolving to the personality prompt string.
   */
  async getCharacterPersonalityPrompt(characterId: string, dynamicContext?: Record<string, any>): Promise<string> {
    this.logger.log(`Generating personality prompt for character ${characterId}`, { characterId, dynamicContext });

    const character = await this.prisma.aiCharacter.findUnique({
      where: { id: characterId },
      // Include relations to personality traits if they are in a separate table
      // include: { personalityTraits: true }
    });

    if (!character) {
      this.logger.warn(`AI Character with ID ${characterId} not found.`);
      throw new NotFoundException(`AI Character with ID ${characterId} not found.`);
    }

    // Example: Constructing the personality string
    // This is highly dependent on how personality is modeled.
    let personalityString = `You are ${character.name}. `;
    if (character.basePersonality) { // Assuming a field `basePersonality` on the model
      personalityString += character.basePersonality;
    }
    // Add more sophisticated logic:
    // - Concatenate from `character.personalityTraits` if it's a relation.
    // - Incorporate `dynamicContext`.
    // - Fetch recent conversation snippets for continuity.

    // Example: "You are Clara, a helpful and empathetic assistant for YoungMobility.
    // You are patient with users and always try to provide clear, concise answers.
    // Your primary goal is to assist with vehicle transfer bookings."

    this.logger.debug(`Generated personality for ${characterId}: ${personalityString.substring(0, 100)}...`);
    return personalityString;
  }

  // ... other AI interaction methods
}
content_copy
download
Use code with caution.
TypeScript
This service method would be called by other backend services (e.g., when handling a chat message) or exposed via an API endpoint if the frontend needs to fetch it directly for some reason (though usually the backend handles priming the AI).
Frontend (Using the Personality String):

The frontend typically wouldn't fetch the personality string directly if the backend is handling the AI interaction.
If the frontend were to construct a prompt itself (less common for complex AI), it would call an endpoint that returns the character details, including personality traits, and then assemble the prompt.
Example of where this is used (Conceptual Backend Logic):

// In a chat handling service or controller on the backend
async function handleUserMessage(userId: string, selectedCharacterId: string, userMessage: string) {
  // 1. Get Character's Personality
  const personalityPrompt = await this.aiCoreService.getCharacterPersonalityPrompt(selectedCharacterId, {
    userTimeZone: 'Europe/Berlin', // Example dynamic context
  });

  // 2. Construct full prompt for the AI model
  const fullPrompt = `${personalityPrompt}\n\nUser: ${userMessage}\nAI:`;

  // 3. Send to AI Model via AiCoreService
  const aiResponse = await this.aiCoreService.generateResponse(fullPrompt);

  // 4. Return AI response to user (and potentially save conversation)
  return aiResponse;
}
content_copy
download
Use code with caution.
TypeScript
This structure provides a starting point. The exact implementation of utility functions and data access patterns will evolve with the application's complexity and specific feature requirements. The key is to maintain separation of concerns, reusability, and clarity.

content_copy
download
Use code with caution.

-

# Given Project Structure:
ybconsult/
├── .env.local                  # Local environment variables (API keys, DB_URL - Gitignored)
├── .env.example                # Example environment variables
├── .eslintignore
├── .eslintrc.json
├── .gitignore
├── .prettierignore
├── .prettierrc.json
├── husky/                     # Husky pre-commit hooks configuration
│   └── pre-commit
├── lint-staged.config.js       # Lint-staged configuration
├── next-env.d.ts
├── next.config.js              # Next.js config (image domains, PWA, etc.)
├── package.json
├── postcss.config.js
├── README.md
├── tailwind.config.ts          # Tailwind CSS configuration (fonts, colors, plugins)
├── tsconfig.json
├── public/                     # Static assets
│   ├── images/
│   │   └── heros/
│   │   └── logos/
│   │   └── illustrations/
│   │   └── placeholders/       # Placeholders for next/image blur
│   ├── fonts/                  # Self-hosted fonts
│   └── favicons/
│   └── sitemap.xml             # Generated sitemap
│   └── robots.txt              # Robots configuration
├── src/
│   ├── app/                    # Next.js App Router (core application logic and routing)
│   │   ├── (auth)/             # Group for authentication routes
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   ├── register-client/
│   │   │   │   └── page.tsx
│   │   │   ├── register-driver/
│   │   │   │   └── page.tsx
│   │   │   ├── forgot-password/
│   │   │   │   └── page.tsx
│   │   │   └── reset-password/
│   │   │       └── [token]/
│   │   │           └── page.tsx
│   │   ├── (platform)/         # Group for authenticated user sections
│   │   │   ├── layout.tsx      # Shared layout for authenticated users (e.g., with sidebar)
│   │   │   ├── client/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── orders/
│   │   │   │   │   ├── create/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── [orderId]/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── billing/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── profile/
│   │   │   │       └── page.tsx
│   │   │   ├── driver/
│   │   │   │   ├── dashboard/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── jobs/
│   │   │   │   │   ├── [jobId]/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── earnings/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── profile/
│   │   │   │       └── page.tsx
│   │   │   └── admin/
│   │   │       ├── layout.tsx  # Specific layout for admin panel
│   │   │       ├── dashboard/
│   │   │       │   └── page.tsx
│   │   │       ├── users/
│   │   │       │   └── page.tsx
│   │   │       └── orders/
│   │   │           └── page.tsx
│   │   ├── (public)/           # Group for public facing pages
│   │   │   ├── layout.tsx      # Shared layout for public pages (e.g., main header/footer)
│   │   │   ├── solutions/
│   │   │   │   └── [slug]/
│   │   │   │       └── page.tsx
│   │   │   ├── youngmovers/
│   │   │   │   └── page.tsx
│   │   │   ├── about/
│   │   │   │   └── page.tsx
│   │   │   ├── blog/
│   │   │   │   ├── [postId]/
│   │   │   │   │   └── page.tsx
│   │   │   │   └── page.tsx
│   │   │   ├── contact/
│   │   │   │   └── page.tsx
│   │   │   └── api-docs/
│   │   │       └── page.tsx
│   │   ├── api/                # API Routes (Next.js serverless functions)
│   │   │   ├── auth/
│   │   │   │   ├── [...nextauth]/ # NextAuth.js handler
│   │   │   │   │   └── route.ts
│   │   │   │   ├── register-client/
│   │   │   │   │   └── route.ts
│   │   │   │   └── register-driver/
│   │   │   │       └── route.ts
│   │   │   ├── orders/
│   │   │   │   └── route.ts
│   │   │   │   └── [orderId]/
│   │   │   │       └── route.ts
│   │   │   ├── users/          # e.g., for profile updates
│   │   │   │   └── [userId]/
│   │   │   │       └── route.ts
│   │   │   ├── documents/      # For document uploads/management
│   │   │   │   └── route.ts
│   │   │   └── health/
│   │   │       └── route.ts
│   │   ├── favicon.ico
│   │   ├── globals.css         # Global styles, Tailwind base, custom global styles
│   │   ├── layout.tsx          # Root layout (html, body tags)
│   │   └── page.tsx            # Main landing page (mapped from (public) group or separate)
│   ├── components/             # Reusable UI components
│   │   ├── ui/                 # Raw Shadcn UI components (as installed by `shadcn-ui add`)
│   │   ├── layout/             # Layout components (Header, Footer, Nav, Sidebar, PageWrapper)
│   │   │   └── HorizontalScroller.tsx # Component for Quechua-style scrolling
│   │   ├── common/             # General reusable components (e.g., CustomButton, DataCard, EnhancedModal, FormFieldWrapper, LoadingSpinner, EmptyState)
│   │   │   └── ThemeToggle.tsx   # Example if you add light/dark mode
│   │   ├── forms/              # Reusable form components or complex form sections
│   │   │   └── FileUpload.tsx    # Potentially a more advanced file upload component
│   │   └── specific/           # Components highly specific to features (e.g., OrderCard, JobListItem, DriverProfileForm, ClientOrderFormSections)
│   ├── config/                 # Application-wide configurations
│   │   └── site.ts             # Site metadata, navigation links, social links
│   │   └── animations.ts       # Framer Motion animation variants
│   ├── contexts/               # React Context API (use sparingly, prefer Zustand/Jotai for complex state)
│   │   └── AppSettingsContext.tsx # e.g., for theme, language preference if not using URL
│   ├── hooks/                  # Custom React Hooks
│   │   ├── useAuthSession.ts   # Hook to easily access NextAuth session
│   │   └── useMediaQuery.ts    # For responsive logic in components
│   ├── lib/                    # Helper functions, utilities, constants
│   │   ├── auth.ts             # Authentication helpers, NextAuth options (can be in `app/api/auth/[...nextauth]/route.ts` too)
│   │   ├── db.ts               # Prisma client instance and DB helper functions
│   │   ├── utils.ts            # General utility functions (formatting, calculations)
│   │   ├── validators/         # Zod schemas for form and API validation
│   │   │   ├── auth.schemas.ts
│   │   │   └── order.schemas.ts
│   │   ├── constants.ts        # Application constants (roles, statuses, etc.)
│   │   ├── queryClient.ts      # React Query client instance (if not inlined in providers)
│   │   └── mailer.ts           # Email sending service helpers (Resend, SendGrid)
│   ├── providers/              # Wrapper components for contexts, React Query, Theme, etc.
│   │   └── AppProviders.tsx    # Combines all global providers
│   ├── services/               # API service functions (typed wrappers around fetch/axios for API calls) - alternative to direct fetch in React Query
│   │   └── orderService.ts
│   ├── styles/                 # Additional global styles, theme overrides, font definitions (if not in globals.css or tailwind.config)
│   ├── types/                  # TypeScript type definitions
│   │   ├── index.ts            # General application types
│   │   └── prisma.ts           # Types generated/extended from Prisma schema
│   │   └── next-auth.d.ts      # Augmenting NextAuth types (e.g., session.user.role)
│   ├── prisma/                 # Prisma ORM configuration
│   │   ├── schema.prisma
│   │   ├── migrations/
│   │   └── seed.ts             # Optional: script to seed database with initial data
├── storybook/                  # Storybook configuration files
│   ├── main.ts
│   └── preview.ts
├── stories/                    # Storybook stories for components
│   ├── Button.stories.tsx
├── e2e/                        # End-to-end tests (e.g., Playwright, Cypress)
│   ├── tests/
│   └── playwright.config.ts
├── scripts/                    # Utility scripts (db seed, deployment helpers)
│   └── seed-database.ts
├── docs/                       # Project documentation
│   ├── ADR/                    # Architecture Decision Records
│   └── API.md                  # API documentation outline (can be generated later)