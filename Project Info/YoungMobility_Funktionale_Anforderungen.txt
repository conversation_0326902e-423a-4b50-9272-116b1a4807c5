# Funktionale Anforderungen: YoungMobility Webanwendung

## 1. Allgemeine Anforderungen

*   **1.1 Technologie-Stack:**
-    *   Frontend: Vite mit React (neueste stabile Versionen)
+    *   Frontend: Next.js mit React (neueste stabile Versionen)
    *   Styling: Neueste Version von Tailwind CSS
    *   UI-Komponenten: Neueste Version von Shadcn UI
*   **1.2 Design-Philosophie:**
    *   Übernahme des UI/UX-Designansatzes von `https://quechua-lookbook.com/ss25/` (horizontales Scrollen, Übergangseffekte, minimalistische Navigation, Vollbild-Bildmaterial).
    *   Fokus auf eine transparente, effiziente und äußerst benutzerfreundliche Erfahrung.
*   **1.3 Kernangebot:**
    *   Identisch zum Kerngeschäft von onlogist.com: Vermittlung von Fahrzeugüberführungen.
*   **1.4 Zielsetzung:**
    *   Entwicklung einer Plattform, die onlogist.com als Marktführer durch eine überlegene User Experience ersetzen kann, während alle wesentlichen Dienste und Inhalte beibehalten werden.

## 2. UI/UX-Anforderungen (inspiriert von `https://quechua-lookbook.com/ss25/`)

*   **2.1 Horizontales Scrollen:** Implementierung von horizontalem Scrollen für Hauptsektionen und zur Präsentation von Inhalten, wo dies das Benutzererlebnis verbessert und dem Stil der Referenzseite entspricht.
*   **2.2 Übergangseffekte:** Einsatz von sanften, modernen und ansprechenden Übergängen zwischen verschiedenen Sektionen, Seiten und Elementinteraktionen.
*   **2.3 Minimalistische Navigation:**
    *   Eine klare, intuitive und unaufdringliche Navigation, die den Benutzer nicht überfordert und leicht zugänglich ist.
    *   Navigationselemente sollen sich nahtlos in das Vollbild-Design integrieren.
*   **2.4 Vollbild-Bildmaterial:** Konsequenter Einsatz von hochwertigem, bildschirmfüllendem Bildmaterial und Videos, um ein immersives und visuell beeindruckendes Erlebnis zu schaffen.
*   **2.5 Intuitive Bedienung:** Die Benutzeroberfläche muss selbsterklärend und einfach zu bedienen sein, um eine steile Lernkurve zu vermeiden.
*   **2.6 Responsive Design:** Die Anwendung muss auf allen Geräten (Desktop, Tablet, Mobiltelefon) eine optimale Darstellung und Funktionalität gewährleisten.
*   **2.7 Lesbarkeit und Interface (Pain Point 4):**
    *   Implementierung größerer, gut lesbarer Schriftgrößen auf der gesamten Plattform.
    *   Ein sauberes, aufgeräumtes und intuitives Interface, das die Informationsaufnahme erleichtert.

## 3. Kernfunktionen (analog zu onlogist.com, aber verbessert)

*   **3.1 Benutzerrollen und Authentifizierung:**
    *   3.1.1 Geschäftskunden (z.B. Autovermietungen, Leasingfirmen): Registrierung, Login, Passwort-Reset, Profilverwaltung, Verwaltung von Unternehmensdaten.
    *   3.1.2 Fahrer ("YoungMovers"): Vereinfachter Registrierungsprozess (Pain Point 6), Login, Passwort-Reset, Profilverwaltung, Upload und Verwaltung von Dokumenten (Führerschein, Gewerbeschein etc.).
    *   3.1.3 Administratoren: Systemverwaltung, Benutzerverwaltung, Content-Management, Überwachung von Transaktionen, Support-Management.
*   **3.2 Marktplatz für Fahrzeugüberführungen:**
    *   3.2.1 Auftragserstellung (durch Geschäftskunden): Detaillierte Eingabe von Auftragsdaten (Fahrzeugdetails, Abholort, Zielort, gewünschter Zeitrahmen, spezielle Anforderungen, Kontaktinformationen).
    *   3.2.2 Auftragsübersicht: Anzeige verfügbarer Überführungsaufträge für qualifizierte und eingeloggte Fahrer, filterbar und sortierbar.
    *   3.2.3 Gebotsabgabe/Interessensbekundung (durch Fahrer): Möglichkeit für Fahrer, auf Aufträge zu bieten oder ihr Interesse zu bekunden.
    *   3.2.4 Fahrer-Matching und Auftragsvergabe (Pain Point 2):
        *   Implementierung eines verbesserten Algorithmus, der Kunden effizient mit den passenden Fahrern verbindet (basierend auf Standort, Zeitfenster, Fahrzeugtyp, Fahrerbewertung, Qualifikationen und spezifischen Kundenanforderungen).
        *   Möglichkeit für Kunden, bevorzugte Fahrer zu markieren oder bestimmte Fahrer für zukünftige Aufträge auszuschließen.
    *   3.2.5 Auftragsannahme und -bestätigung: Prozess für Fahrer zur Annahme eines zugewiesenen Auftrags und Bestätigung durch den Kunden.
*   **3.3 Buchungsprozess (Pain Point 3):**
    *   3.3.1 Vereinfachter und gestraffter Buchungsablauf: Reduzierung der notwendigen Schritte und Klicks.
    *   3.3.2 Intuitive Benutzeroberfläche für die Auftragserstellung, -bearbeitung und -verwaltung.
    *   3.3.3 Vorlagenfunktion für wiederkehrende Aufträge.
*   **3.4 Preisgestaltung und Abrechnung (Pain Point 1):**
    *   3.4.1 Transparente Preisstruktur: Klare, verständliche und vorab einsehbare Preisgestaltung ohne versteckte Gebühren. Anzeige aller Kostenkomponenten vor Auftragsbestätigung.
    *   3.4.2 Mechanismen zur Vermeidung von exzessivem Unterbieten und zur Sicherstellung fairer Preise (z.B. optionale Mindestgebote, Preisindikationen basierend auf Distanz/Fahrzeugtyp).
    *   3.4.3 Sichere Online-Zahlungsabwicklung für Servicegebühren (falls zutreffend) und Auszahlung an Fahrer.
    *   3.4.4 Automatisierte Rechnungsstellung und Bereitstellung von Rechnungsübersichten für Kunden und Fahrer.
*   **3.5 Auftragsverfolgung und -management (Pain Point 7):**
    *   3.5.1 Echtzeit-Tracking des Auftragsstatus (z.B. "Ausgeschrieben", "Fahrer zugewiesen", "Unterwegs", "Abgeschlossen") für Kunden und Fahrer.
    *   3.5.2 Möglichkeit für Benutzer, den Status ihrer Aufträge einfach nachzuverfolgen, auch nach Verlassen der Seite und erneutem Login.
    *   3.5.3 Benachrichtigungssystem für wichtige Statusänderungen (E-Mail, In-App).
    *   3.5.4 Digitale Übergabeprotokolle (optional mit Fotodokumentation).
*   **3.6 Kommunikationssystem:**
    *   3.6.1 Integriertes, sicheres Nachrichtensystem für die direkte Kommunikation zwischen Geschäftskunden und zugewiesenen Fahrern bezüglich spezifischer Aufträge.
    *   3.6.2 Systembenachrichtigungen für neue Aufträge, Gebote, Auftragsänderungen, Zahlungen etc.
*   **3.7 Bewertungs- und Feedbacksystem:**
    *   3.7.1 Möglichkeit für Geschäftskunden, Fahrer nach Auftragsabschluss zu bewerten (z.B. Pünktlichkeit, Professionalität, Zustand des Fahrzeugs).
    *   3.7.2 Möglichkeit für Fahrer, Feedback zum Auftraggeber oder zum Prozess zu geben.
    *   3.7.3 Transparente Kommunikation (Pain Point 10): Wo angemessen und datenschutzkonform, allgemeine Gründe oder Kriterien erläutern, warum Fahrer bestimmte Aufträge nicht erhalten haben (z.B. "Andere Bewerber hatten höhere Bewertungen/passendere Qualifikationen für diesen spezifischen Auftrag").
*   **3.8 Support-System (Pain Point 5):**
    *   3.8.1 Reaktionsschneller Kundenservice mit klar definierten Servicezeiten.
    *   3.8.2 Verbesserte telefonische Erreichbarkeit und prominente Anzeige der Support-Kontaktmöglichkeiten.
    *   3.8.3 Umfassender FAQ-Bereich und eine durchsuchbare Wissensdatenbank.
    *   3.8.4 Implementierung eines Ticketsystems für Supportanfragen mit Nachverfolgung des Bearbeitungsstatus.
*   **3.9 Erweiterte Such- und Filterfunktionen (Pain Point 8):**
    *   3.9.1 Für Fahrer: Präzise Filteroptionen bei der Auftragssuche (z.B. nach Standort/Radius, Fahrzeugtyp, Distanz, Dringlichkeit, Vergütungsspanne).
    *   3.9.2 Für Geschäftskunden: Erweiterte Filter bei der Suche nach Fahrern (falls zutreffend und im Modell vorgesehen) oder bei der Analyse vergangener Aufträge.
*   **3.10 Transparenz des Auftragsvolumens für Fahrer (Pain Point 9):**
    *   3.10.1 Bereitstellung von klaren Informationen oder Indikatoren über das verfügbare Auftragsvolumen, z.B. nach Region, Fahrzeugtyp oder saisonalen Trends, um Fahrern eine bessere Planung zu ermöglichen.

## 4. Inhaltsstruktur (analog zu onlogist.com)

Die YoungMobility-Plattform wird die folgende Seitenstruktur und Inhalte umfassen, präsentiert im neuen UI/UX-Design:

*   **4.1 Haupt-Landingpage:**
    *   Hero-Sektion: Prägnante Darstellung des Wertversprechens von YoungMobility als führende, benutzerfreundliche Plattform für Fahrzeugüberführungen.
    *   Herausforderungen & Lösungen: Aufzeigen typischer Probleme im Fahrzeugtransfer und wie YoungMobility diese löst.
    *   Vorteile für Kunden und Fahrer.
    *   Call-to-Actions (z.B. "Auftrag erstellen", "Fahrer werden").
*   **4.2 Lösungsseiten (für Geschäftskunden):**
    *   Branchenspezifische Lösungen (z.B. für Autovermietungen, Leasinggesellschaften, Auto-Abo-Anbieter, Autohäuser, Werkstattketten, Carsharing-Anbieter).
    *   Detaillierte Beschreibung der Servicemodelle (z.B. Self-Managed vs. Full-Service, falls adaptiert).
*   **4.3 Informationsseiten:**
    *   Kundenreferenzen und Fallstudien (Testimonials).
    *   Blog/News-Bereich: Artikel, Tipps und Neuigkeiten für Fahrer und Kunden.
    *   Berichte/Whitepapers: Downloadbare Inhalte zu Markttrends, Effizienzsteigerung etc. (falls relevant).
    *   Karriere: Offene Stellen und Informationen zum Arbeiten bei YoungMobility.
    *   Über Uns: Unternehmensinformationen, Mission, Vision.
*   **4.4 Fahrerportal ("YoungMovers"):**
    *   Informationen und Anreize für die Registrierung als unabhängiger Fahrer.
    *   Detaillierte Beschreibung der Vorteile, des Verdienstpotenzials und des vereinfachten Onboarding-Prozesses.
    *   Direkter Link zur Fahrerregistrierung.
*   **4.5 API-Dokumentation:**
    *   Umfassende Dokumentation für Geschäftskunden zur Integration von YoungMobility in ihre bestehenden Systeme.
*   **4.6 Kunden- und Fahrerportal (z.B. `portal.youngmobility.com` oder integriert):**
    *   **Für Geschäftskunden:** Dashboard zur Auftragsverwaltung (erstellen, bearbeiten, stornieren, verfolgen), Rechnungsübersicht, Verwaltung von Stammdaten, Kommunikation mit Fahrern, Reporting-Funktionen.
    *   **Für Fahrer:** Dashboard zur Auftragsübersicht (verfügbare Aufträge, angenommene Aufträge, abgeschlossene Aufträge), Gebotsabgabe, Profilverwaltung, Verwaltung von Dokumenten, Einsicht in Verdienste und Abrechnungen, Kommunikation mit Kunden.

## 5. Spezifische Anforderungen zur Adressierung von Pain Points (Zusammenfassung der Integration)

*   **FR-P1 (Preistransparenz):** Siehe 3.4.1, 3.4.2.
*   **FR-P2 (Fahrer-Matching):** Siehe 3.2.4.
*   **FR-P3 (Buchungsvereinfachung):** Siehe 3.3.
*   **FR-P4 (Interface-Verbesserungen):** Siehe 2.7, genereller UI/UX-Ansatz unter 2.
*   **FR-P5 (Verbesserter Support):** Siehe 3.8.
*   **FR-P6 (Optimierte Fahrerregistrierung):** Siehe 3.1.2, 4.4.
*   **FR-P7 (Verbesserte Auftragsverfolgung):** Siehe 3.5.
*   **FR-P8 (Erweiterte Suchfilter):** Siehe 3.9.
*   **FR-P9 (Transparenz Auftragsvolumen):** Siehe 3.10.
*   **FR-P10 (Klareres Feedback Fahrer):** Siehe 3.7.3.

## 6. Nicht-funktionale Anforderungen (Auswahl)

*   **6.1 Performance:** Schnelle Ladezeiten der Seiten (z.B. First Contentful Paint < 1.8s, Largest Contentful Paint < 2.5s). Optimierte Datenbankabfragen.
*   **6.2 Skalierbarkeit:** Die Architektur muss so ausgelegt sein, dass sie das erwartete Wachstum (400.000+ Überführungen/Jahr, 10.000+ Fahrer) problemlos bewältigen kann.
*   **6.3 Sicherheit:** Implementierung von Best Practices für Webanwendungssicherheit (OWASP Top 10), Schutz sensibler Benutzer- und Zahlungsdaten, DSGVO-Konformität. Regelmäßige Sicherheitsaudits.
*   **6.4 Zuverlässigkeit:** Hohe Verfügbarkeit der Plattform (Ziel > 99.9% Uptime). Robuste Fehlerbehandlung und Logging.
*   **6.5 Wartbarkeit:** Sauberer, gut dokumentierter Code. Modulare Architektur zur Erleichterung von Updates und Erweiterungen.