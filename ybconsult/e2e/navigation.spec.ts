/**
 * E2E Tests für Navigation
 * 
 * Diese Datei enthält E2E-Tests für die Navigation der Anwendung.
 * Teil der Phase 8: Comprehensive Automated Testing (YM-805)
 */

import { test, expect } from "@playwright/test";

test.describe("Navigation", () => {
  test("should navigate to the home page", async ({ page }) => {
    await page.goto("/");
    
    // Überprüfe, ob die Startseite geladen wurde
    await expect(page).toHaveTitle(/YoungMobility Consulting/);
    
    // Überprüfe, ob das Logo vorhanden ist
    const logo = page.locator("img[alt='YoungMobility Logo']");
    await expect(logo).toBeVisible();
  });
  
  test("should navigate to the about page", async ({ page }) => {
    await page.goto("/");
    
    // Klicke auf den "Über uns"-Link
    await page.click("text=Über uns");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/about/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Über uns/);
  });
  
  test("should navigate to the services page", async ({ page }) => {
    await page.goto("/");
    
    // Klicke auf den "Leistungen"-Link
    await page.click("text=Leistungen");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/services/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Leistungen/);
  });
  
  test("should navigate to the contact page", async ({ page }) => {
    await page.goto("/");
    
    // Klicke auf den "Kontakt"-Link
    await page.click("text=Kontakt");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/contact/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Kontakt/);
    
    // Überprüfe, ob das Kontaktformular vorhanden ist
    const contactForm = page.locator("form");
    await expect(contactForm).toBeVisible();
  });
  
  test("should navigate to the privacy policy page", async ({ page }) => {
    await page.goto("/");
    
    // Klicke auf den "Datenschutz"-Link im Footer
    await page.click("text=Datenschutz");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/privacy-policy/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Datenschutzerklärung/);
  });
  
  test("should navigate to the terms of service page", async ({ page }) => {
    await page.goto("/");
    
    // Klicke auf den "AGB"-Link im Footer
    await page.click("text=AGB");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/terms-of-service/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Allgemeine Geschäftsbedingungen/);
  });
  
  test("should navigate to the quechua design page", async ({ page }) => {
    await page.goto("/quechua-design");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/quechua-design/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Quechua Design System/);
    
    // Überprüfe, ob die Farbpalette angezeigt wird
    const colorPalette = page.locator("text=Quechua-inspirierte Farbpalette");
    await expect(colorPalette).toBeVisible();
  });
  
  test("should navigate to the performance page", async ({ page }) => {
    await page.goto("/performance");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/performance/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Performance Optimization/);
    
    // Überprüfe, ob die Web Vitals angezeigt werden
    const webVitals = page.locator("text=Core Web Vitals");
    await expect(webVitals).toBeVisible();
  });
  
  test("should navigate to the security page", async ({ page }) => {
    await page.goto("/security");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/security/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Sicherheit & DSGVO-Konformität/);
    
    // Überprüfe, ob das Kontaktformular angezeigt wird
    const contactForm = page.locator("text=Kontaktformular mit Zod-Validierung");
    await expect(contactForm).toBeVisible();
  });
  
  test("should navigate to the scalability page", async ({ page }) => {
    await page.goto("/scalability");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/scalability/);
    
    // Überprüfe, ob der Seitentitel korrekt ist
    const heading = page.locator("h1");
    await expect(heading).toContainText(/Skalierbarkeit & Zuverlässigkeit/);
    
    // Überprüfe, ob die Systemgesundheit angezeigt wird
    const systemHealth = page.locator("text=Systemgesundheit");
    await expect(systemHealth).toBeVisible();
  });
  
  test("should have a responsive navigation menu", async ({ page }) => {
    // Setze die Viewport-Größe auf Mobilgeräte
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto("/");
    
    // Überprüfe, ob das Hamburger-Menü angezeigt wird
    const hamburgerMenu = page.locator("button[aria-label='Toggle Menu']");
    await expect(hamburgerMenu).toBeVisible();
    
    // Klicke auf das Hamburger-Menü
    await hamburgerMenu.click();
    
    // Überprüfe, ob das Menü geöffnet wird
    const mobileMenu = page.locator("nav");
    await expect(mobileMenu).toBeVisible();
    
    // Klicke auf einen Link im Menü
    await page.click("text=Über uns");
    
    // Überprüfe, ob die URL korrekt ist
    await expect(page).toHaveURL(/.*\/about/);
  });
});
