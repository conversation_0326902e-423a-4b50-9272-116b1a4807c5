import type { Config } from "tailwindcss"

const config: Config = {
  darkMode: "class",
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        inter: ["var(--font-inter)", "sans-serif"],
        playfair: ["var(--font-playfair)", "serif"],
      },
      colors: {
        // Bestehende Farben hier...
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        // Quechua-inspirierte Farbpalette
        // Primäre Farbpalette
        'quechua-cream': "var(--quechua-cream)",
        'quechua-beige': "var(--quechua-beige)",
        'quechua-light-green': "var(--quechua-light-green)",
        'quechua-medium-green': "var(--quechua-medium-green)",
        'quechua-dark-green': "var(--quechua-dark-green)",
        'quechua-earth-brown': "var(--quechua-earth-brown)",
        'quechua-terracotta': "var(--quechua-terracotta)",
        'quechua-sky-blue': "var(--quechua-sky-blue)",

        // Erweiterte Farbpalette
        'quechua-deep-brown': "var(--quechua-deep-brown)",
        'quechua-wheat': "var(--quechua-wheat)",
        'quechua-sienna': "var(--quechua-sienna)",
        'quechua-very-dark-green': "var(--quechua-very-dark-green)",
        'quechua-mountain-blue': "var(--quechua-mountain-blue)",
        'quechua-sunset-orange': "var(--quechua-sunset-orange)",
        'quechua-gold': "var(--quechua-gold)",

        // Legacy-Namen für Abwärtskompatibilität
        'ym-cream': "var(--quechua-cream)",
        'ym-beige': "var(--quechua-beige)",
        'ym-light-green': "var(--quechua-light-green)",
        'ym-medium-green': "var(--quechua-medium-green)",
        'ym-dark-green': "var(--quechua-dark-green)",
        'ym-earth-brown': "var(--quechua-earth-brown)",
        'ym-terracotta': "var(--quechua-terracotta)",
        'ym-sky-blue': "var(--quechua-sky-blue)"
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}

export default config