/* Ultra Modern Design System
 * Professional, sleek, and contemporary design for YoungMobility
 */

:root {
  /* Modern Dark Theme Colors */
  --ultra-bg-primary: #0a0a0a;
  --ultra-bg-secondary: #111111;
  --ultra-bg-tertiary: #1a1a1a;
  --ultra-bg-card: #161616;
  --ultra-bg-elevated: #1f1f1f;
  
  /* Text Colors */
  --ultra-text-primary: #ffffff;
  --ultra-text-secondary: #a1a1aa;
  --ultra-text-muted: #71717a;
  
  /* Brand Colors */
  --ultra-primary: #3b82f6;
  --ultra-primary-hover: #2563eb;
  --ultra-primary-light: #60a5fa;
  
  /* Accent Colors */
  --ultra-accent-green: #10b981;
  --ultra-accent-purple: #8b5cf6;
  --ultra-accent-orange: #f59e0b;
  --ultra-accent-red: #ef4444;
  
  /* Border Colors */
  --ultra-border: #27272a;
  --ultra-border-light: #3f3f46;
  --ultra-border-focus: #3b82f6;
  
  /* Shadows */
  --ultra-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ultra-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ultra-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ultra-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --ultra-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  
  /* Gradients */
  --ultra-gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --ultra-gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --ultra-gradient-accent: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  --ultra-gradient-bg: linear-gradient(135deg, #0a0a0a 0%, #111111 100%);
  
  /* Transitions */
  --ultra-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --ultra-transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --ultra-transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --ultra-transition-bounce: 400ms cubic-bezier(0.34, 1.56, 0.64, 1);
  
  /* Spacing */
  --ultra-space-xs: 0.25rem;
  --ultra-space-sm: 0.5rem;
  --ultra-space-md: 1rem;
  --ultra-space-lg: 1.5rem;
  --ultra-space-xl: 2rem;
  --ultra-space-2xl: 3rem;
  
  /* Border Radius */
  --ultra-radius-sm: 0.375rem;
  --ultra-radius-md: 0.5rem;
  --ultra-radius-lg: 0.75rem;
  --ultra-radius-xl: 1rem;
  --ultra-radius-2xl: 1.5rem;
  --ultra-radius-full: 9999px;
}

/* Ultra Modern Components */

/* Glass Morphism Card */
.ultra-card {
  background: rgba(22, 22, 22, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid var(--ultra-border);
  border-radius: var(--ultra-radius-lg);
  box-shadow: var(--ultra-shadow-lg);
  transition: all var(--ultra-transition-normal);
  position: relative;
  overflow: hidden;
}

.ultra-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
  opacity: 0;
  transition: opacity var(--ultra-transition-normal);
}

.ultra-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ultra-shadow-xl), var(--ultra-shadow-glow);
  border-color: var(--ultra-border-light);
}

.ultra-card:hover::before {
  opacity: 1;
}

/* Ultra Modern Button */
.ultra-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  border-radius: var(--ultra-radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--ultra-transition-normal);
  overflow: hidden;
  text-decoration: none;
  outline: none;
}

.ultra-button-primary {
  background: var(--ultra-gradient-primary);
  color: var(--ultra-text-primary);
  box-shadow: var(--ultra-shadow-md);
}

.ultra-button-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--ultra-transition-normal);
}

.ultra-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--ultra-shadow-lg), 0 0 20px rgba(59, 130, 246, 0.4);
}

.ultra-button-primary:hover::before {
  left: 100%;
}

.ultra-button-primary:active {
  transform: translateY(0);
}

/* Ultra Modern Input */
.ultra-input {
  width: 100%;
  padding: 0.875rem 1rem;
  background: var(--ultra-bg-card);
  border: 1px solid var(--ultra-border);
  border-radius: var(--ultra-radius-md);
  color: var(--ultra-text-primary);
  font-size: 0.875rem;
  transition: all var(--ultra-transition-fast);
  outline: none;
}

.ultra-input:focus {
  border-color: var(--ultra-border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--ultra-bg-elevated);
}

.ultra-input::placeholder {
  color: var(--ultra-text-muted);
}

/* Navigation */
.ultra-nav {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ultra-border);
  position: sticky;
  top: 0;
  z-index: 50;
}

.ultra-nav-item {
  color: var(--ultra-text-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--ultra-radius-sm);
  transition: all var(--ultra-transition-fast);
  position: relative;
}

.ultra-nav-item:hover {
  color: var(--ultra-text-primary);
  background: rgba(59, 130, 246, 0.1);
}

.ultra-nav-item::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--ultra-primary);
  transition: all var(--ultra-transition-fast);
  transform: translateX(-50%);
}

.ultra-nav-item:hover::after {
  width: 80%;
}

/* Hero Section */
.ultra-hero {
  background: var(--ultra-gradient-bg);
  position: relative;
  overflow: hidden;
}

.ultra-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Animated Background Grid */
.ultra-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
  opacity: 0.3;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Floating Elements */
.ultra-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Glow Effects */
.ultra-glow {
  position: relative;
}

.ultra-glow::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity var(--ultra-transition-normal);
  pointer-events: none;
  z-index: -1;
}

.ultra-glow:hover::after {
  opacity: 1;
}

/* Status Indicators */
.ultra-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--ultra-radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.ultra-status-success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--ultra-accent-green);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.ultra-status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--ultra-accent-orange);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.ultra-status-error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--ultra-accent-red);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Loading Animations */
.ultra-loading {
  position: relative;
  overflow: hidden;
}

.ultra-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
