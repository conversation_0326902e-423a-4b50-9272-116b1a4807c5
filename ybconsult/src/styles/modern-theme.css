/* Modern Design System for YoungMobility
 * A fresh, contemporary design system with enhanced interactive elements
 */

/* ===== Color Variables ===== */
:root {
  /* Primary Colors */
  --modern-primary: #3B82F6;       /* Vibrant blue - brand color */
  --modern-primary-dark: #2563EB;  /* Darker blue for hover states */
  --modern-primary-light: #93C5FD; /* Lighter blue for backgrounds */
  --modern-primary-gradient: linear-gradient(135deg, #3B82F6, #2563EB);
  --modern-primary-gradient-hover: linear-gradient(135deg, #2563EB, #1D4ED8);

  /* Secondary Colors */
  --modern-secondary: #10B981;     /* Green for success states */
  --modern-secondary-dark: #059669; /* Darker green for hover states */
  --modern-secondary-light: #A7F3D0; /* Lighter green for backgrounds */
  --modern-secondary-gradient: linear-gradient(135deg, #10B981, #059669);
  --modern-secondary-gradient-hover: linear-gradient(135deg, #059669, #047857);

  /* Background Colors - New darker background */
  --modern-bg-dark: #111827;       /* Dark background */
  --modern-bg-light: #F3F4F6;      /* Light background */
  --modern-bg-gradient: linear-gradient(135deg, #F3F4F6, #E5E7EB);
  --modern-bg-card: #FFFFFF;       /* Card background */
  --modern-bg-card-hover: #F9FAFB; /* Card hover background */

  /* Neutral Colors */
  --modern-white: #FFFFFF;
  --modern-cream: #F9FAFB;         /* Off-white for backgrounds */
  --modern-gray-100: #F3F4F6;      /* Lightest gray */
  --modern-gray-200: #E5E7EB;      /* Light gray for borders */
  --modern-gray-300: #D1D5DB;      /* Gray for disabled states */
  --modern-gray-400: #9CA3AF;      /* Medium gray for secondary text */
  --modern-gray-500: #6B7280;      /* Gray for placeholder text */
  --modern-gray-600: #4B5563;      /* Dark gray for body text */
  --modern-gray-700: #374151;      /* Darker gray for headings */
  --modern-gray-800: #1F2937;      /* Very dark gray for emphasis */
  --modern-gray-900: #111827;      /* Almost black */

  /* Accent Colors */
  --modern-accent-red: #EF4444;    /* Red for errors and alerts */
  --modern-accent-yellow: #F59E0B; /* Yellow for warnings */
  --modern-accent-purple: #8B5CF6; /* Purple for special elements */
  --modern-accent-teal: #14B8A6;   /* Teal for alternative accent */

  /* Shadows with enhanced depth */
  --modern-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --modern-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --modern-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --modern-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --modern-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --modern-shadow-glow: 0 0 15px rgba(59, 130, 246, 0.5);

  /* Transitions */
  --modern-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --modern-transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --modern-transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --modern-transition-bounce: 300ms cubic-bezier(0.34, 1.56, 0.64, 1);

  /* Border Radius */
  --modern-radius-sm: 0.25rem;  /* 4px */
  --modern-radius-md: 0.375rem; /* 6px */
  --modern-radius-lg: 0.5rem;   /* 8px */
  --modern-radius-xl: 0.75rem;  /* 12px */
  --modern-radius-2xl: 1rem;    /* 16px */
  --modern-radius-full: 9999px; /* For pills and circles */
}

/* ===== Typography ===== */
:root {
  --modern-font-sans: 'Montserrat', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --modern-font-serif: 'Playfair Display', Georgia, Cambria, 'Times New Roman', Times, serif;
  --modern-font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Font Sizes */
  --modern-text-xs: 0.75rem;    /* 12px */
  --modern-text-sm: 0.875rem;   /* 14px */
  --modern-text-base: 1rem;     /* 16px */
  --modern-text-lg: 1.125rem;   /* 18px */
  --modern-text-xl: 1.25rem;    /* 20px */
  --modern-text-2xl: 1.5rem;    /* 24px */
  --modern-text-3xl: 1.875rem;  /* 30px */
  --modern-text-4xl: 2.25rem;   /* 36px */
  --modern-text-5xl: 3rem;      /* 48px */

  /* Line Heights */
  --modern-leading-none: 1;
  --modern-leading-tight: 1.25;
  --modern-leading-snug: 1.375;
  --modern-leading-normal: 1.5;
  --modern-leading-relaxed: 1.625;
  --modern-leading-loose: 2;
}

/* ===== Component Styles ===== */

/* Modern Gradient Button */
.modern-gradient-button {
  background: var(--modern-primary-gradient);
  color: var(--modern-white);
  position: relative;
  z-index: 1;
  overflow: hidden;
  border: 2px solid transparent;
  transition: transform var(--modern-transition-bounce),
              box-shadow var(--modern-transition-normal),
              border-color var(--modern-transition-normal);
}

.modern-gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modern-primary-gradient-hover);
  z-index: -1;
  transition: opacity var(--modern-transition-normal);
  opacity: 0;
}

.modern-gradient-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--modern-shadow-lg);
  border-color: rgba(59, 130, 246, 0.3);
}

.modern-gradient-button:hover::before {
  opacity: 1;
}

.modern-gradient-button:active {
  transform: translateY(0);
  box-shadow: var(--modern-shadow-md);
}

.modern-gradient-button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  outline: none;
}

/* Secondary Gradient Button */
.modern-gradient-button-secondary {
  background: var(--modern-secondary-gradient);
  color: var(--modern-white);
  position: relative;
  z-index: 1;
  overflow: hidden;
  border: 2px solid transparent;
  transition: transform var(--modern-transition-bounce),
              box-shadow var(--modern-transition-normal),
              border-color var(--modern-transition-normal);
}

.modern-gradient-button-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modern-secondary-gradient-hover);
  z-index: -1;
  transition: opacity var(--modern-transition-normal);
  opacity: 0;
}

.modern-gradient-button-secondary:hover {
  transform: translateY(-3px);
  box-shadow: var(--modern-shadow-lg);
  border-color: rgba(16, 185, 129, 0.3);
}

.modern-gradient-button-secondary:hover::before {
  opacity: 1;
}

/* Modern Card */
.modern-card {
  background-color: var(--modern-bg-card);
  border-radius: var(--modern-radius-lg);
  box-shadow: var(--modern-shadow-md);
  transition: transform var(--modern-transition-normal), 
              box-shadow var(--modern-transition-normal),
              border-color var(--modern-transition-normal);
  overflow: hidden;
  border: 1px solid transparent;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--modern-shadow-lg);
  border-color: var(--modern-gray-200);
}

/* Modern Input */
.modern-input {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 2px solid var(--modern-gray-300);
  border-radius: var(--modern-radius-md);
  background-color: var(--modern-white);
  color: var(--modern-gray-800);
  transition: border-color var(--modern-transition-fast), 
              box-shadow var(--modern-transition-fast);
}

.modern-input:focus {
  border-color: var(--modern-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  outline: none;
}
