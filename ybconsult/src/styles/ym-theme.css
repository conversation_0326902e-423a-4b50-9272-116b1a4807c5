/* YoungMobility Design System
 * A modern, professional design system for the YoungMobility platform
 */

/* ===== Color Variables ===== */
:root {
  /* Primary Colors */
  --ym-primary: #2563EB;       /* Primary blue - brand color */
  --ym-primary-dark: #1D4ED8;  /* Darker blue for hover states */
  --ym-primary-light: #93C5FD; /* Lighter blue for backgrounds */

  /* Secondary Colors */
  --ym-secondary: #10B981;     /* Green for success states */
  --ym-secondary-dark: #059669; /* Darker green for hover states */
  --ym-secondary-light: #A7F3D0; /* Lighter green for backgrounds */

  /* Neutral Colors */
  --ym-white: #FFFFFF;
  --ym-cream: #F9FAFB;         /* Off-white for backgrounds */
  --ym-gray-100: #F3F4F6;      /* Lightest gray */
  --ym-gray-200: #E5E7EB;      /* Light gray for borders */
  --ym-gray-300: #D1D5DB;      /* Gray for disabled states */
  --ym-gray-400: #9CA3AF;      /* Medium gray for secondary text */
  --ym-gray-500: #6B7280;      /* Gray for placeholder text */
  --ym-gray-600: #4B5563;      /* Dark gray for body text */
  --ym-gray-700: #374151;      /* Darker gray for headings */
  --ym-gray-800: #1F2937;      /* Very dark gray for emphasis */
  --ym-gray-900: #111827;      /* Almost black */

  /* Accent Colors */
  --ym-accent-red: #EF4444;    /* Red for errors and alerts */
  --ym-accent-yellow: #F59E0B; /* Yellow for warnings */
  --ym-accent-purple: #8B5CF6; /* Purple for special elements */
  --ym-accent-teal: #14B8A6;   /* Teal for alternative accent */

  /* Shadows */
  --ym-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --ym-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ym-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ym-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --ym-transition-fast: 150ms ease;
  --ym-transition-normal: 250ms ease;
  --ym-transition-slow: 350ms ease;

  /* Border Radius */
  --ym-radius-sm: 0.25rem;  /* 4px */
  --ym-radius-md: 0.375rem; /* 6px */
  --ym-radius-lg: 0.5rem;   /* 8px */
  --ym-radius-xl: 0.75rem;  /* 12px */
  --ym-radius-2xl: 1rem;    /* 16px */
  --ym-radius-full: 9999px; /* For pills and circles */
}

/* ===== Typography ===== */
:root {
  --ym-font-sans: 'Montserrat', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --ym-font-serif: 'Playfair Display', Georgia, Cambria, 'Times New Roman', Times, serif;
  --ym-font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Font Sizes */
  --ym-text-xs: 0.75rem;    /* 12px */
  --ym-text-sm: 0.875rem;   /* 14px */
  --ym-text-base: 1rem;     /* 16px */
  --ym-text-lg: 1.125rem;   /* 18px */
  --ym-text-xl: 1.25rem;    /* 20px */
  --ym-text-2xl: 1.5rem;    /* 24px */
  --ym-text-3xl: 1.875rem;  /* 30px */
  --ym-text-4xl: 2.25rem;   /* 36px */
  --ym-text-5xl: 3rem;      /* 48px */

  /* Line Heights */
  --ym-leading-none: 1;
  --ym-leading-tight: 1.25;
  --ym-leading-snug: 1.375;
  --ym-leading-normal: 1.5;
  --ym-leading-relaxed: 1.625;
  --ym-leading-loose: 2;

  /* Font Weights */
  --ym-font-thin: 100;
  --ym-font-extralight: 200;
  --ym-font-light: 300;
  --ym-font-normal: 400;
  --ym-font-medium: 500;
  --ym-font-semibold: 600;
  --ym-font-bold: 700;
  --ym-font-extrabold: 800;
  --ym-font-black: 900;
}

/* ===== Component Styles ===== */

/* Buttons */
.ym-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--ym-radius-md);
  font-weight: var(--ym-font-medium);
  padding: 0.5rem 1rem;
  transition: all var(--ym-transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.ym-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.ym-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Primary Button */
.ym-ui-button {
  background-color: var(--ym-primary);
  color: var(--ym-white);
  border: 1px solid transparent;
  border-radius: var(--ym-radius-md);
  padding: 0.5rem 1rem;
  font-weight: var(--ym-font-medium);
  transition: all var(--ym-transition-normal);
  box-shadow: var(--ym-shadow-sm);
  position: relative;
  overflow: hidden;
}

.ym-ui-button:hover {
  background-color: var(--ym-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--ym-shadow-md);
}

.ym-ui-button:active {
  transform: translateY(0);
}

.ym-ui-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

/* Secondary Button */
.ym-ui-button-secondary {
  background-color: var(--ym-secondary);
  color: var(--ym-white);
  border: 1px solid transparent;
  border-radius: var(--ym-radius-md);
  padding: 0.5rem 1rem;
  font-weight: var(--ym-font-medium);
  transition: all var(--ym-transition-normal);
  box-shadow: var(--ym-shadow-sm);
  position: relative;
  overflow: hidden;
}

.ym-ui-button-secondary:hover {
  background-color: var(--ym-secondary-dark);
  transform: translateY(-2px);
  box-shadow: var(--ym-shadow-md);
}

.ym-ui-button-secondary:active {
  transform: translateY(0);
}

/* Outline Button */
.ym-ui-button-outline {
  background-color: transparent;
  color: var(--ym-primary);
  border: 1px solid var(--ym-primary);
  border-radius: var(--ym-radius-md);
  padding: 0.5rem 1rem;
  font-weight: var(--ym-font-medium);
  transition: all var(--ym-transition-normal);
  position: relative;
  overflow: hidden;
}

.ym-ui-button-outline:hover {
  background-color: rgba(37, 99, 235, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--ym-shadow-sm);
}

.ym-ui-button-outline:active {
  transform: translateY(0);
}

/* Ghost Button */
.ym-ui-button-ghost {
  background-color: transparent;
  color: var(--ym-gray-700);
  border: 1px solid transparent;
  border-radius: var(--ym-radius-md);
  padding: 0.5rem 1rem;
  font-weight: var(--ym-font-medium);
  transition: all var(--ym-transition-normal);
}

.ym-ui-button-ghost:hover {
  background-color: var(--ym-gray-100);
  border-color: var(--ym-gray-200);
}

/* Gradient Button Styles */
.ym-gradient-button {
  background: linear-gradient(to right, var(--ym-primary), var(--ym-primary-dark));
  color: white;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.ym-gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, var(--ym-primary-dark), var(--ym-primary));
  z-index: -1;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.ym-gradient-button:hover::before {
  opacity: 1;
}

/* Cards */
.ym-card {
  background-color: var(--ym-white);
  border-radius: var(--ym-radius-lg);
  box-shadow: var(--ym-shadow-md);
  transition: transform var(--ym-transition-normal), box-shadow var(--ym-transition-normal);
  overflow: hidden;
}

.ym-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--ym-shadow-lg);
}

/* Inputs */
.ym-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--ym-gray-300);
  border-radius: var(--ym-radius-md);
  background-color: var(--ym-white);
  color: var(--ym-gray-800);
  transition: border-color var(--ym-transition-fast), box-shadow var(--ym-transition-fast);
}

.ym-input:focus {
  border-color: var(--ym-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
  outline: none;
}

.ym-input::placeholder {
  color: var(--ym-gray-400);
}

.ym-input:disabled {
  background-color: var(--ym-gray-100);
  cursor: not-allowed;
}

/* Badges */
.ym-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: var(--ym-radius-full);
  font-size: var(--ym-text-xs);
  font-weight: var(--ym-font-medium);
}

.ym-badge-primary {
  background-color: var(--ym-primary-light);
  color: var(--ym-primary-dark);
}

.ym-badge-secondary {
  background-color: var(--ym-secondary-light);
  color: var(--ym-secondary-dark);
}

.ym-badge-error {
  background-color: rgba(239, 68, 68, 0.2);
  color: var(--ym-accent-red);
}

.ym-badge-warning {
  background-color: rgba(245, 158, 11, 0.2);
  color: var(--ym-accent-yellow);
}

/* Animations */
@keyframes ym-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes ym-slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes ym-slide-down {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.ym-animate-fade-in {
  animation: ym-fade-in var(--ym-transition-normal) forwards;
}

.ym-animate-slide-up {
  animation: ym-slide-up var(--ym-transition-normal) forwards;
}

.ym-animate-slide-down {
  animation: ym-slide-down var(--ym-transition-normal) forwards;
}
