/* Quechua-inspirierte Farbpalette und Design-System */

/* ===== Farbvariablen ===== */
:root {
  /* Primäre Farbpalette */
  --quechua-cream: #FFFDF6;
  --quechua-beige: #FAF6E9;
  --quechua-light-green: #DDEB9D;
  --quechua-medium-green: #A0C878;
  --quechua-dark-green: #5A7D2A;
  --quechua-earth-brown: #8B4513;
  --quechua-terracotta: #CD5C5C;
  --quechua-sky-blue: #87CEEB;

  /* Erweiterte Farbpalette */
  --quechua-deep-brown: #6B2513;
  --quechua-wheat: #F5DEB3;
  --quechua-sienna: #A0522D;
  --quechua-very-dark-green: #3A5D0A;
  --quechua-mountain-blue: #5D8AA8;
  --quechua-sunset-orange: #FA8072;
  --quechua-gold: #DAA520;

  /* <PERSON><PERSON><PERSON> und Transparenzen */
  --quechua-shadow-light: rgba(0, 0, 0, 0.05);
  --quechua-shadow-medium: rgba(0, 0, 0, 0.1);
  --quechua-shadow-dark: rgba(0, 0, 0, 0.2);

  /* Animation Timing */
  --quechua-transition-fast: 0.2s ease;
  --quechua-transition-medium: 0.3s ease;
  --quechua-transition-slow: 0.5s ease;
}

/* ===== Gradient-Buttons ===== */
.quechua-gradient-button {
  /* Basis-Farben für den Standard-Button */
  --color-1: var(--quechua-dark-green);
  --color-2: var(--quechua-earth-brown);
  --color-3: var(--quechua-medium-green);
  --color-4: var(--quechua-light-green);
  --color-5: var(--quechua-dark-green);

  /* Rahmenfarben */
  --border-angle: 140deg;
  --border-color-1: hsla(80, 75%, 60%, 0.3);
  --border-color-2: hsla(80, 75%, 40%, 0.8);

  /* Transition für sanfte Übergänge */
  transition: transform var(--quechua-transition-medium),
              box-shadow var(--quechua-transition-medium);
}

.quechua-gradient-button:hover {
  --pos-x: 0%;
  --pos-y: 95.51%;
  --spread-x: 110.24%;
  --spread-y: 110.2%;
  --color-1: var(--quechua-light-green);
  --color-2: var(--quechua-medium-green);
  --color-3: var(--quechua-earth-brown);
  --color-4: var(--quechua-dark-green);
  --color-5: var(--quechua-very-dark-green);

  /* Angepasste Stops für den Farbverlauf */
  --stop-1: 0%;
  --stop-2: 10%;
  --stop-3: 35.44%;
  --stop-4: 71.34%;
  --stop-5: 90.76%;

  /* Rahmenfarben beim Hover */
  --border-angle: 160deg;
  --border-color-1: hsla(80, 75%, 90%, 0.2);
  --border-color-2: hsla(80, 75%, 60%, 0.75);

  /* Visuelles Feedback */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--quechua-shadow-medium);
}

.quechua-gradient-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px var(--quechua-shadow-light);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.quechua-gradient-button-variant {
  /* Basis-Farben für den Varianten-Button */
  --color-1: var(--quechua-terracotta);
  --color-2: var(--quechua-earth-brown);
  --color-3: var(--quechua-sienna);
  --color-4: var(--quechua-wheat);
  --color-5: var(--quechua-terracotta);

  /* Rahmenfarben */
  --border-angle: 200deg;
  --border-color-1: hsla(0, 75%, 70%, 0.3);
  --border-color-2: hsla(0, 75%, 50%, 0.8);
}

.quechua-gradient-button-variant:hover {
  --pos-x: 0%;
  --pos-y: 95.51%;
  --spread-x: 110.24%;
  --spread-y: 110.2%;
  --color-1: var(--quechua-wheat);
  --color-2: var(--quechua-terracotta);
  --color-3: var(--quechua-sienna);
  --color-4: var(--quechua-earth-brown);
  --color-5: var(--quechua-deep-brown);

  /* Angepasste Stops für den Farbverlauf */
  --stop-1: 0%;
  --stop-2: 10%;
  --stop-3: 35.44%;
  --stop-4: 71.34%;
  --stop-5: 90.76%;

  /* Rahmenfarben beim Hover */
  --border-angle: 220deg;
  --border-color-1: hsla(0, 75%, 90%, 0.2);
  --border-color-2: hsla(0, 75%, 70%, 0.75);
}

/* ===== Quechua Card Styles ===== */
.quechua-card {
  background-color: var(--quechua-cream);
  border: 1px solid var(--quechua-light-green);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--quechua-shadow-light);
  transition: transform var(--quechua-transition-medium),
              box-shadow var(--quechua-transition-medium);
}

.quechua-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px var(--quechua-shadow-medium);
}

/* ===== Quechua Input Styles ===== */
.quechua-input {
  background-color: var(--quechua-beige);
  border: 1px solid var(--quechua-medium-green);
  border-radius: 8px;
  padding: 10px 16px;
  transition: border-color var(--quechua-transition-fast),
              box-shadow var(--quechua-transition-fast);
}

.quechua-input:focus {
  border-color: var(--quechua-dark-green);
  box-shadow: 0 0 0 2px rgba(90, 125, 42, 0.2);
  outline: none;
}

/* ===== Quechua Patterns ===== */
.quechua-pattern-diagonal {
  background-image: linear-gradient(45deg, var(--quechua-light-green) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--quechua-light-green) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--quechua-light-green) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--quechua-light-green) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.quechua-pattern-stripes {
  background: repeating-linear-gradient(
    45deg,
    var(--quechua-beige),
    var(--quechua-beige) 10px,
    var(--quechua-light-green) 10px,
    var(--quechua-light-green) 20px
  );
}

.quechua-pattern-dots {
  background-image: radial-gradient(
    var(--quechua-medium-green) 2px,
    transparent 2px
  );
  background-size: 20px 20px;
}
