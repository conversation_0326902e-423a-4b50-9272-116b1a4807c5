@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%; /* Entspricht #FFFFFF / ym-white */
    --foreground: 222.2 84% 4.9%; /* Entspricht etwa #0A2540 / ym-gray-900 für Text auf hellem Grund */

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%; /* Entspricht #2563EB / ym-primary (Beispiel HSL) */
    --primary-foreground: 210 40% 98%; /* Helle Schrift für primäre Buttons */

    --secondary: 210 40% 96.1%; /* Entspricht etwa #F3F4F6 / ym-gray-100 */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%; /* Entspricht etwa #6B7280 / ym-gray-500 */

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%; /* Entspricht #EF4444 / ym-accent-red */
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%; /* Entspricht etwa #E5E7EB / ym-gray-200 */
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem; /* Entspricht --ym-radius-lg */
  }

  .dark {
    --background: 222.2 84% 4.9%; /* Entspricht #111827 / ym-gray-900 */
    --foreground: 210 40% 98%; /* Entspricht #F9FAFB / ym-cream für Text auf dunklem Grund */

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%; /* Entspricht etwa #1F2937 / ym-gray-800 */
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%; /* Entspricht etwa #9CA3AF / ym-gray-400 */

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%; /* Dunklere Variante von Rot */
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 20.2% 65.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Die font-montserrat Klasse wird im RootLayout auf <body> angewendet */
  }
}

/* Custom utilities and base layer CSS variables can be added here if needed */

/* Example of scrollbar-hide utility if it was previously used */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}