/**
 * @file queryKeys.ts
 * @description Definiert Query Key Factories für React Query zur Sicherstellung von Konsistenz
 *              und zur Vermeidung von Redundanz beim <PERSON>ng und Refetching von Daten.
 *              Query Keys sollten immer Arrays sein und spezifische, serialisierbare Elemente enthalten.
 */

/**
 * Basis-Query-Key für alle Benutzerdaten.
 * @type {['users']}
 */
const USERS_BASE_KEY = ['users'] as const;

/**
 * Basis-Query-Key für alle Auftragsdaten.
 * @type {['orders']}
 */
const ORDERS_BASE_KEY = ['orders'] as const;

/**
 * Basis-Query-Key für alle Fahrerdaten.
 * @type {['drivers']}
 */
const DRIVERS_BASE_KEY = ['drivers'] as const;

/**
 * Basis-Query-Key für alle Fahrzeugdaten.
 * @type {['vehicles']}
 */
const VEHICLES_BASE_KEY = ['vehicles'] as const;

/**
 * Basis-Query-Key für alle Client-bezogenen Daten.
 * @type {['client']}
 */
const CLIENT_BASE_KEY = ['client'] as const;


/**
 * @namespace queryKeys
 * @description Enthält Factory-Funktionen zur Erzeugung von Query Keys für verschiedene Entitäten und Szenarien.
 */
export const queryKeys = {
  /**
   * Query Keys für Benutzerdaten.
   */
  users: {
    /**
     * Key für eine Liste aller Benutzer.
     * @example queryKeys.users.all() // ['users', 'list']
     * @returns {readonly ['users', 'list']}
     */
    all: () => [...USERS_BASE_KEY, 'list'] as const,
    /**
     * Key für eine Liste von Benutzern mit bestimmten Filtern.
     * @param {Record<string, any>} filters - Die Filterobjekte.
     * @example queryKeys.users.list({ role: 'admin', status: 'active' }) // ['users', 'list', { role: 'admin', status: 'active' }]
     * @returns {readonly ['users', 'list', Record<string, any>]}
     */
    list: (filters: Record<string, any>) => [...USERS_BASE_KEY, 'list', filters] as const,
    /**
     * Key für einen einzelnen Benutzer anhand seiner ID.
     * @param {string | number} id - Die ID des Benutzers.
     * @example queryKeys.users.detail(1) // ['users', 'detail', 1]
     * @returns {readonly ['users', 'detail', string | number]}
     */
    detail: (id: string | number) => [...USERS_BASE_KEY, 'detail', id] as const,
  },

  /**
   * Query Keys für Auftragsdaten.
   */
  orders: {
    /**
     * Key für eine Liste aller Aufträge.
     * @returns {readonly ['orders', 'list']}
     */
    all: () => [...ORDERS_BASE_KEY, 'list'] as const,
    /**
     * Key für eine Liste von Aufträgen mit Filtern.
     * @param {Record<string, any>} filters - Die Filterobjekte.
     * @returns {readonly ['orders', 'list', Record<string, any>]}
     */
    list: (filters: Record<string, any>) => [...ORDERS_BASE_KEY, 'list', filters] as const,
    /**
     * Key für einen einzelnen Auftrag anhand seiner ID.
     * @param {string | number} id - Die ID des Auftrags.
     * @returns {readonly ['orders', 'detail', string | number]}
     */
    detail: (id: string | number) => [...ORDERS_BASE_KEY, 'detail', id] as const,
    /**
     * Key für Aufträge, die einem bestimmten Benutzer zugeordnet sind.
     * @param {string | number} userId - Die ID des Benutzers.
     * @returns {readonly ['orders', 'list', 'byUser', string | number]}
     */
    byUser: (userId: string | number) => [...ORDERS_BASE_KEY, 'list', 'byUser', userId] as const,
  },

  /**
   * Query Keys für Fahrerdaten.
   */
  drivers: {
    /**
     * Key für eine Liste aller Fahrer.
     * @returns {readonly ['drivers', 'list']}
     */
    all: () => [...DRIVERS_BASE_KEY, 'list'] as const,
    /**
     * Key für eine Liste von Fahrern mit Filtern (z.B. Verfügbarkeit, Standort).
     * @param {Record<string, any>} filters - Die Filterobjekte.
     * @returns {readonly ['drivers', 'list', Record<string, any>]}
     */
    list: (filters: Record<string, any>) => [...DRIVERS_BASE_KEY, 'list', filters] as const,
    /**
     * Key für einen einzelnen Fahrer anhand seiner ID.
     * @param {string | number} id - Die ID des Fahrers.
     * @returns {readonly ['drivers', 'detail', string | number]}
     */
    detail: (id: string | number) => [...DRIVERS_BASE_KEY, 'detail', id] as const,
    /**
     * Key für die Dokumente eines bestimmten Fahrers.
     * @param {string | number} driverId - Die ID des Fahrers.
     * @returns {readonly ['drivers', 'detail', string | number, 'documents']}
     */
    documents: (driverId: string | number) => [...DRIVERS_BASE_KEY, 'detail', driverId, 'documents'] as const,
  },

  /**
   * Query Keys für Fahrzeugdaten.
   */
  vehicles: {
    /**
     * Key für eine Liste aller Fahrzeuge.
     * @returns {readonly ['vehicles', 'list']}
     */
    all: () => [...VEHICLES_BASE_KEY, 'list'] as const,
    /**
     * Key für eine Liste von Fahrzeugen mit Filtern (z.B. Typ, Verfügbarkeit).
     * @param {Record<string, any>} filters - Die Filterobjekte.
     * @returns {readonly ['vehicles', 'list', Record<string, any>]}
     */
    list: (filters: Record<string, any>) => [...VEHICLES_BASE_KEY, 'list', filters] as const,
    /**
     * Key für ein einzelnes Fahrzeug anhand seiner ID oder VIN.
     * @param {string | number} id - Die ID oder VIN des Fahrzeugs.
     * @returns {readonly ['vehicles', 'detail', string | number]}
     */
    detail: (id: string | number) => [...VEHICLES_BASE_KEY, 'detail', id] as const,
  },

  /**
   * Query Keys für Client-bezogene Daten.
   */
  client: {
    /**
     * Key für Dashboard-Daten des Clients.
     * @returns {readonly ['client', 'dashboard']}
     */
    dashboard: () => [...CLIENT_BASE_KEY, 'dashboard'] as const,
    /**
     * Key für Aufträge des Clients mit Filtern.
     * @param {Record<string, any>} filters - Die Filterobjekte.
     * @returns {readonly ['client', 'orders', Record<string, any>]}
     */
    orders: (filters: Record<string, any>) => [...CLIENT_BASE_KEY, 'orders', filters] as const,
    /**
     * Key für Rechnungsdaten des Clients.
     * @returns {readonly ['client', 'billing']}
     */
    billing: () => [...CLIENT_BASE_KEY, 'billing'] as const,
  },

  // Weitere Entitäten und spezifische Keys können hier hinzugefügt werden.
  // Beispiel:
  // invoices: {
  //   all: () => ['invoices', 'list'] as const,
  //   detail: (id: string) => ['invoices', 'detail', id] as const,
  //   byCustomer: (customerId: string) => ['invoices', 'byCustomer', customerId] as const,
  // }
};

/**
 * Beispiele für die Verwendung der Query Keys:
 *
 * import { useQuery } from '@tanstack/react-query';
 * import { queryKeys } from '@/lib/queryKeys';
 * import { fetchUsers, fetchUserById } from '@/api/userService'; // Beispiel API-Funktionen
 *
 * // Abrufen aller Benutzer
 * const { data: users } = useQuery({
 *   queryKey: queryKeys.users.all(),
 *   queryFn: fetchUsers,
 * });
 *
 * // Abrufen eines bestimmten Benutzers
 * const userId = 123;
 * const { data: user } = useQuery({
 *   queryKey: queryKeys.users.detail(userId),
 *   queryFn: () => fetchUserById(userId),
 * });
 *
 * // Abrufen einer gefilterten Liste von Aufträgen
 * const filters = { status: 'pending', priority: 'high' };
 * const { data: pendingHighPriorityOrders } = useQuery({
 *   queryKey: queryKeys.orders.list(filters),
 *   queryFn: () => fetchOrders(filters), // Annahme: fetchOrders akzeptiert ein Filterobjekt
 * });
 */