/**
 * Benachrichtigungssystem für YoungMobility
 * 
 * Diese Datei enthält Hilfsfunktionen zum Erstellen und Verwalten von Benachrichtigungen.
 */

import { PrismaClient } from "@prisma/client";
import { sendEmail, sendAssignmentNotification } from "./mailer";

const prisma = new PrismaClient();

/**
 * Schnittstelle für Benachrichtigungsoptionen
 */
interface NotificationOptions {
  userId: string;
  type: string;
  title: string;
  message: string;
  relatedEntityType?: string;
  relatedEntityId?: string;
  sendEmail?: boolean;
}

/**
 * Erstellt eine neue Benachrichtigung für einen Benutzer.
 * 
 * @param options Optionen für die Benachrichtigung
 * @returns Promise mit der erstellten Benachrichtigung
 */
export async function createNotification(options: NotificationOptions) {
  try {
    const notification = await prisma.notification.create({
      data: {
        userId: options.userId,
        type: options.type,
        title: options.title,
        message: options.message,
        relatedEntityType: options.relatedEntityType,
        relatedEntityId: options.relatedEntityId,
      },
    });

    // Wenn E-Mail-Benachrichtigung aktiviert ist, Benutzer abrufen und E-Mail senden
    if (options.sendEmail) {
      const user = await prisma.user.findUnique({
        where: { id: options.userId },
        select: { email: true, firstName: true, lastName: true },
      });

      if (user && user.email) {
        // Einfache E-Mail mit dem Benachrichtigungsinhalt senden
        await sendEmail({
          to: user.email,
          subject: options.title,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h1 style="color: #4F46E5;">${options.title}</h1>
              <p>Hallo ${user.firstName || 'Benutzer'},</p>
              <p>${options.message}</p>
              <div style="margin: 30px 0;">
                <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3000'}" 
                  style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                  Zur Plattform
                </a>
              </div>
              <p>Mit freundlichen Grüßen,<br>Ihr YoungMobility-Team</p>
            </div>
          `,
          text: `
            ${options.title}
            
            Hallo ${user.firstName || 'Benutzer'},
            
            ${options.message}
            
            Zur Plattform: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}
            
            Mit freundlichen Grüßen,
            Ihr YoungMobility-Team
          `,
        });
      }
    }

    return notification;
  } catch (error) {
    console.error("Fehler beim Erstellen der Benachrichtigung:", error);
    throw error;
  }
}

/**
 * Erstellt eine Benachrichtigung für eine Auftragszuweisung an einen Fahrer.
 * 
 * @param driverId ID des Fahrers
 * @param orderId ID des Auftrags
 * @param orderReference Referenznummer des Auftrags
 * @param sendEmail Ob eine E-Mail gesendet werden soll
 * @returns Promise mit der erstellten Benachrichtigung
 */
export async function notifyDriverAssignment(
  driverId: string,
  orderId: string,
  orderReference: string,
  sendEmail: boolean = true
) {
  try {
    // Benachrichtigung erstellen
    const notification = await createNotification({
      userId: driverId,
      type: "ASSIGNMENT_CREATED",
      title: "Neuer Auftrag zugewiesen",
      message: `Sie wurden dem Auftrag #${orderReference} zugewiesen. Bitte bestätigen oder lehnen Sie ab.`,
      relatedEntityType: "Order",
      relatedEntityId: orderId,
      sendEmail: false, // Wir senden eine spezifischere E-Mail unten
    });

    // Wenn E-Mail-Benachrichtigung aktiviert ist, Fahrer und Auftragsdaten abrufen
    if (sendEmail) {
      const driver = await prisma.user.findUnique({
        where: { id: driverId },
        select: { email: true, firstName: true, lastName: true },
      });

      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: {
          title: true,
          pickupCity: true,
          deliveryCity: true,
          pickupDate: true,
          clientProfile: {
            select: { companyName: true },
          },
        },
      });

      if (driver && driver.email && order) {
        // Spezifische E-Mail für Auftragszuweisung senden
        await sendAssignmentNotification(
          driver.email,
          `${driver.firstName} ${driver.lastName}`,
          orderReference,
          {
            title: order.title,
            pickupCity: order.pickupCity,
            deliveryCity: order.deliveryCity,
            pickupDate: order.pickupDate.toISOString(),
            clientCompany: order.clientProfile.companyName,
          }
        );
      }
    }

    return notification;
  } catch (error) {
    console.error("Fehler beim Benachrichtigen des Fahrers über die Zuweisung:", error);
    throw error;
  }
}

/**
 * Erstellt eine Benachrichtigung für einen Kunden, wenn ein Fahrer einen Auftrag angenommen hat.
 * 
 * @param clientId ID des Kunden
 * @param orderId ID des Auftrags
 * @param orderReference Referenznummer des Auftrags
 * @param driverName Name des Fahrers
 * @param sendEmail Ob eine E-Mail gesendet werden soll
 * @returns Promise mit der erstellten Benachrichtigung
 */
export async function notifyClientDriverAccepted(
  clientId: string,
  orderId: string,
  orderReference: string,
  driverName: string,
  sendEmail: boolean = true
) {
  return createNotification({
    userId: clientId,
    type: "DRIVER_ACCEPTED",
    title: "Fahrer hat Auftrag angenommen",
    message: `Der Fahrer ${driverName} hat Ihren Auftrag #${orderReference} angenommen und wird ihn ausführen.`,
    relatedEntityType: "Order",
    relatedEntityId: orderId,
    sendEmail,
  });
}

/**
 * Erstellt eine Benachrichtigung für einen Kunden, wenn ein Fahrer einen Auftrag abgelehnt hat.
 * 
 * @param clientId ID des Kunden
 * @param orderId ID des Auftrags
 * @param orderReference Referenznummer des Auftrags
 * @param sendEmail Ob eine E-Mail gesendet werden soll
 * @returns Promise mit der erstellten Benachrichtigung
 */
export async function notifyClientDriverDeclined(
  clientId: string,
  orderId: string,
  orderReference: string,
  sendEmail: boolean = true
) {
  return createNotification({
    userId: clientId,
    type: "DRIVER_DECLINED",
    title: "Fahrer hat Auftrag abgelehnt",
    message: `Ein Fahrer hat Ihren Auftrag #${orderReference} abgelehnt. Der Auftrag wurde wieder für andere Fahrer freigegeben.`,
    relatedEntityType: "Order",
    relatedEntityId: orderId,
    sendEmail,
  });
}

/**
 * Erstellt eine Benachrichtigung für einen Kunden, wenn der Status eines Auftrags aktualisiert wurde.
 * 
 * @param clientId ID des Kunden
 * @param orderId ID des Auftrags
 * @param orderReference Referenznummer des Auftrags
 * @param newStatus Neuer Status des Auftrags
 * @param sendEmail Ob eine E-Mail gesendet werden soll
 * @returns Promise mit der erstellten Benachrichtigung
 */
export async function notifyOrderStatusUpdate(
  clientId: string,
  orderId: string,
  orderReference: string,
  newStatus: string,
  sendEmail: boolean = true
) {
  // Benutzerfreundlichen Statustext erstellen
  let statusText = newStatus;
  switch (newStatus) {
    case "POSTED":
      statusText = "Veröffentlicht";
      break;
    case "DRIVER_ASSIGNED":
      statusText = "Fahrer zugewiesen";
      break;
    case "IN_PROGRESS":
      statusText = "In Bearbeitung";
      break;
    case "PICKED_UP":
      statusText = "Fahrzeug abgeholt";
      break;
    case "IN_TRANSIT":
      statusText = "In Transit";
      break;
    case "DELIVERED":
      statusText = "Fahrzeug geliefert";
      break;
    case "COMPLETED":
      statusText = "Abgeschlossen";
      break;
    case "CANCELLED":
      statusText = "Storniert";
      break;
    default:
      statusText = newStatus;
  }

  return createNotification({
    userId: clientId,
    type: "ORDER_STATUS_UPDATE",
    title: `Auftragsstatus aktualisiert: ${statusText}`,
    message: `Der Status Ihres Auftrags #${orderReference} wurde auf "${statusText}" aktualisiert.`,
    relatedEntityType: "Order",
    relatedEntityId: orderId,
    sendEmail,
  });
}

// Weitere Benachrichtigungsfunktionen können hier hinzugefügt werden
