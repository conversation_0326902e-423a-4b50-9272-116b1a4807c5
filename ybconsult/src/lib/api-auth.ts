import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * Verifies the API key from the request headers
 * @param request The Next.js request object
 * @returns The client ID if the API key is valid, null otherwise
 */
export async function verifyApi<PERSON>ey(request: NextRequest): Promise<string | null> {
  try {
    // Get API key from Authorization header
    const authHeader = request.headers.get("Authorization");
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null;
    }
    
    const apiKey = authHeader.substring(7); // Remove "Bearer " prefix
    
    if (!apiKey) {
      return null;
    }
    
    // Find API key in database
    const apiKeyRecord = await prisma.apiKey.findUnique({
      where: {
        key: apiKey,
        isActive: true,
      },
      select: {
        userId: true,
        expiresAt: true,
      },
    });
    
    if (!apiKeyRecord) {
      return null;
    }
    
    // Check if API key is expired
    if (apiKeyRecord.expiresAt && new Date() > apiKeyRecord.expiresAt) {
      return null;
    }
    
    // Check if user exists and is active
    const user = await prisma.user.findUnique({
      where: {
        id: apiKeyRecord.userId,
        status: "ACTIVE",
        role: "BUSINESS_CLIENT",
      },
      select: {
        id: true,
      },
    });
    
    if (!user) {
      return null;
    }
    
    // Update last used timestamp
    await prisma.apiKey.update({
      where: {
        key: apiKey,
      },
      data: {
        lastUsedAt: new Date(),
      },
    });
    
    return user.id;
    
  } catch (error) {
    console.error("Error verifying API key:", error);
    return null;
  }
}
