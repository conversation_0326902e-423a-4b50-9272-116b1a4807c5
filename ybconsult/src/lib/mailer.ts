/**
 * E-Mail-Service für YoungMobility
 * 
 * Diese Datei enthält Funktionen zum Versenden von E-Mails über verschiedene E-Mail-Dienste.
 * Aktuell ist eine Implementierung mit Resend vorbereitet, kann aber leicht auf andere Dienste
 * wie SendGrid, Mailgun, etc. erweitert werden.
 */

import { Resend } from 'resend';

// Resend API-Schlüssel aus den Umgebungsvariablen laden
const resendApiKey = process.env.RESEND_API_KEY;
const fromEmail = process.env.EMAIL_FROM || '<EMAIL>';

// Resend-Client initialisieren, wenn ein API-Schlüssel vorhanden ist
const resend = resendApiKey ? new Resend(resendApiKey) : null;

/**
 * Schnittstelle für E-Mail-Optionen
 */
interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html: string;
  from?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Sendet eine E-Mail über den konfigurierten E-Mail-Dienst.
 * 
 * @param options E-Mail-Optionen (Empfänger, Betreff, Inhalt, etc.)
 * @returns Promise mit dem Ergebnis des E-Mail-Versands
 */
export async function sendEmail(options: EmailOptions): Promise<any> {
  try {
    // Überprüfen, ob ein E-Mail-Dienst konfiguriert ist
    if (!resend) {
      console.warn('Kein E-Mail-Dienst konfiguriert. E-Mail wird nicht gesendet.');
      console.log('E-Mail-Inhalt:', {
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
      });
      return { id: 'dev-mode', success: true };
    }

    // E-Mail über Resend senden
    const result = await resend.emails.send({
      from: options.from || fromEmail,
      to: Array.isArray(options.to) ? options.to : [options.to],
      subject: options.subject,
      text: options.text,
      html: options.html,
      reply_to: options.replyTo,
      cc: options.cc ? (Array.isArray(options.cc) ? options.cc : [options.cc]) : undefined,
      bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc : [options.bcc]) : undefined,
      attachments: options.attachments,
    });

    return result;
  } catch (error) {
    console.error('Fehler beim Senden der E-Mail:', error);
    throw error;
  }
}

/**
 * Sendet eine Willkommens-E-Mail an einen neuen Benutzer.
 * 
 * @param to E-Mail-Adresse des Empfängers
 * @param name Name des Empfängers
 * @param role Rolle des Benutzers (DRIVER, BUSINESS_CLIENT, ADMIN)
 * @returns Promise mit dem Ergebnis des E-Mail-Versands
 */
export async function sendWelcomeEmail(to: string, name: string, role: string): Promise<any> {
  const subject = 'Willkommen bei YoungMobility!';
  
  // HTML-Inhalt der E-Mail
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4F46E5;">Willkommen bei YoungMobility!</h1>
      <p>Hallo ${name},</p>
      <p>vielen Dank für Ihre Registrierung bei YoungMobility. Wir freuen uns, Sie als ${
        role === 'DRIVER' ? 'Fahrer' : 
        role === 'BUSINESS_CLIENT' ? 'Geschäftskunden' : 'Benutzer'
      } begrüßen zu dürfen.</p>
      <p>Sie können sich jetzt mit Ihrer E-Mail-Adresse und Ihrem Passwort anmelden und die Plattform nutzen.</p>
      <div style="margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/login" 
           style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Jetzt anmelden
        </a>
      </div>
      <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>
      <p>Mit freundlichen Grüßen,<br>Ihr YoungMobility-Team</p>
    </div>
  `;
  
  // Text-Version der E-Mail für E-Mail-Clients, die kein HTML unterstützen
  const text = `
    Willkommen bei YoungMobility!
    
    Hallo ${name},
    
    vielen Dank für Ihre Registrierung bei YoungMobility. Wir freuen uns, Sie als ${
      role === 'DRIVER' ? 'Fahrer' : 
      role === 'BUSINESS_CLIENT' ? 'Geschäftskunden' : 'Benutzer'
    } begrüßen zu dürfen.
    
    Sie können sich jetzt mit Ihrer E-Mail-Adresse und Ihrem Passwort anmelden und die Plattform nutzen.
    
    Anmelden: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/login
    
    Bei Fragen stehen wir Ihnen gerne zur Verfügung.
    
    Mit freundlichen Grüßen,
    Ihr YoungMobility-Team
  `;
  
  return sendEmail({ to, subject, html, text });
}

/**
 * Sendet eine Benachrichtigung über eine neue Auftragszuweisung an einen Fahrer.
 * 
 * @param to E-Mail-Adresse des Fahrers
 * @param driverName Name des Fahrers
 * @param orderReference Referenznummer des Auftrags
 * @param orderDetails Details zum Auftrag (Abholung, Lieferung, etc.)
 * @returns Promise mit dem Ergebnis des E-Mail-Versands
 */
export async function sendAssignmentNotification(
  to: string, 
  driverName: string, 
  orderReference: string, 
  orderDetails: {
    title: string;
    pickupCity: string;
    deliveryCity: string;
    pickupDate: string;
    clientCompany: string;
  }
): Promise<any> {
  const subject = `Neuer Auftrag zugewiesen: #${orderReference}`;
  
  // HTML-Inhalt der E-Mail
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #4F46E5;">Neuer Auftrag für Sie</h1>
      <p>Hallo ${driverName},</p>
      <p>Ihnen wurde ein neuer Auftrag zugewiesen. Bitte bestätigen oder lehnen Sie diesen Auftrag in Ihrem Fahrer-Portal ab.</p>
      
      <div style="background-color: #f9fafb; border-radius: 8px; padding: 16px; margin: 20px 0;">
        <h2 style="margin-top: 0; color: #111827;">${orderDetails.title}</h2>
        <p><strong>Auftragsnummer:</strong> #${orderReference}</p>
        <p><strong>Auftraggeber:</strong> ${orderDetails.clientCompany}</p>
        <p><strong>Abholung:</strong> ${orderDetails.pickupCity}</p>
        <p><strong>Lieferung:</strong> ${orderDetails.deliveryCity}</p>
        <p><strong>Abholtermin:</strong> ${new Date(orderDetails.pickupDate).toLocaleDateString('de-DE')}</p>
      </div>
      
      <div style="margin: 30px 0;">
        <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/driver/my-jobs/pending-acceptance" 
           style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Zum Auftrag
        </a>
      </div>
      
      <p>Mit freundlichen Grüßen,<br>Ihr YoungMobility-Team</p>
    </div>
  `;
  
  // Text-Version der E-Mail
  const text = `
    Neuer Auftrag für Sie
    
    Hallo ${driverName},
    
    Ihnen wurde ein neuer Auftrag zugewiesen. Bitte bestätigen oder lehnen Sie diesen Auftrag in Ihrem Fahrer-Portal ab.
    
    Auftrag: ${orderDetails.title}
    Auftragsnummer: #${orderReference}
    Auftraggeber: ${orderDetails.clientCompany}
    Abholung: ${orderDetails.pickupCity}
    Lieferung: ${orderDetails.deliveryCity}
    Abholtermin: ${new Date(orderDetails.pickupDate).toLocaleDateString('de-DE')}
    
    Zum Auftrag: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/driver/my-jobs/pending-acceptance
    
    Mit freundlichen Grüßen,
    Ihr YoungMobility-Team
  `;
  
  return sendEmail({ to, subject, html, text });
}

// Weitere E-Mail-Funktionen können hier hinzugefügt werden, z.B.:
// - sendOrderStatusUpdate
// - sendPaymentConfirmation
// - sendPasswordReset
// usw.
