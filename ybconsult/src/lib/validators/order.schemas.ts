import { z } from "zod";

/**
 * Enum für Fahrzeugtypen, entspricht dem Prisma-Schema
 */
export enum VehicleType {
  CAR = "CAR",
  VAN = "VAN",
  SUV = "SUV",
  TRUCK = "TRUCK",
  MOTORCYCLE = "MOTORCYCLE",
  LUXURY = "LUXURY",
  VINTAGE = "VINTAGE",
  OTHER = "OTHER",
}

/**
 * Schema für die Fahrzeugdaten bei der Auftragserstellung
 */
export const vehicleSchema = z.object({
  vin: z.string().min(1, "VIN ist erforderlich").max(17, "VIN darf maximal 17 Zeichen lang sein"),
  make: z.string().min(1, "Marke ist erforderlich"),
  model: z.string().min(1, "Modell ist erforderlich"),
  year: z.number().int().min(1900, "Jahr muss mindestens 1900 sein").max(new Date().getFullYear() + 1, `Jahr darf maximal ${new Date().getFullYear() + 1} sein`).optional(),
  color: z.string().optional(),
  vehicleType: z.nativeEnum(VehicleType, {
    errorMap: () => ({ message: "Bitte wählen Sie einen gültigen Fahrzeugtyp" }),
  }),
  licensePlate: z.string().optional(),
  notes: z.string().optional(),
});

/**
 * Schema für die Adressdaten (Abholung und Lieferung)
 */
export const addressSchema = z.object({
  addressLine1: z.string().min(1, "Straße und Hausnummer sind erforderlich"),
  addressLine2: z.string().optional(),
  city: z.string().min(1, "Stadt ist erforderlich"),
  postalCode: z.string().min(1, "Postleitzahl ist erforderlich"),
  country: z.string().min(1, "Land ist erforderlich"),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  contactName: z.string().optional(),
  contactPhone: z.string().optional(),
});

/**
 * Schema für die Datumsangaben (Abholung und Lieferung)
 */
export const dateRangeSchema = z.object({
  from: z.date({
    required_error: "Startdatum ist erforderlich",
    invalid_type_error: "Ungültiges Datumsformat",
  }),
  to: z.date({
    required_error: "Enddatum ist erforderlich",
    invalid_type_error: "Ungültiges Datumsformat",
  }),
}).refine(data => data.from < data.to, {
  message: "Enddatum muss nach dem Startdatum liegen",
  path: ["to"],
});

/**
 * Hauptschema für die Auftragserstellung
 */
export const createOrderSchema = z.object({
  // Fahrzeugdaten
  vehicle: vehicleSchema,
  
  // Abholungsdaten
  pickup: addressSchema,
  pickupDate: dateRangeSchema,
  
  // Lieferungsdaten
  delivery: addressSchema,
  deliveryDate: dateRangeSchema,
  
  // Zusätzliche Informationen
  specialInstructions: z.string().optional(),
});

/**
 * Typ für die Auftragserstellung, abgeleitet vom Schema
 */
export type CreateOrderInput = z.infer<typeof createOrderSchema>;
