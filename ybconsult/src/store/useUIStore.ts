import { create } from 'zustand';

/**
 * @interface UIState
 * Definiert die Struktur des UI-Zustands.
 * @property {boolean} isMobileMenuOpen - Gibt an, ob das mobile Menü geöffnet ist.
 * @property {() => void} toggleMobileMenu - Funktion zum Umschalten des mobilen Menüs.
 */
interface UIState {
  isMobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
}

/**
 * `useUIStore` ist ein benutzerdefinierter Hook, der von Zustand erstellt wurde, um den globalen UI-Zustand zu verwalten.
 * Er enthält den Zustand `isMobileMenuOpen` und eine Aktion `toggleMobileMenu`, um diesen Zustand zu ändern.
 *
 * @returns {UIState} Das UI-Zustandsobjekt mit dem aktuellen Zustand und den Aktionen.
 */
export const useUIStore = create<UIState>((set) => ({
  // Initialer Zustand
  isMobileMenuOpen: false,
  
  /**
   * Schaltet den Zustand von `isMobileMenuOpen` um.
   * Wenn es offen ist, wird es geschlossen und umgekehrt.
   */
  toggleMobileMenu: () => set((state) => ({ isMobileMenuOpen: !state.isMobileMenuOpen })),
}));

// Beispiel für die Verwendung in einer Komponente:
//
// import { useUIStore } from '@/store/useUIStore'; // Pfad anpassen, falls nötig
//
// function MyComponent() {
//   const isMobileMenuOpen = useUIStore((state) => state.isMobileMenuOpen);
//   const toggleMobileMenu = useUIStore((state) => state.toggleMobileMenu);
//
//   return (
//     <div>
//       <button onClick={toggleMobileMenu}>
//         {isMobileMenuOpen ? 'Close Menu' : 'Open Menu'}
//       </button>
//       {isMobileMenuOpen && <div>Mobile Menu Content</div>}
//     </div>
//   );
// }