"use client";

import { GradientButton } from "@/components/ui/gradient-button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export default function TestPage() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-6">Test Seite für UI-Komponenten</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Beispiel Karte</CardTitle>
            <CardDescription>Dies ist eine Beispiel-Karte mit Shadcn UI</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">Hier ist ein Beispieltext in der Karte.</p>
            <div className="flex gap-2">
              <Badge>Badge 1</Badge>
              <Badge variant="secondary">Badge 2</Badge>
              <Badge variant="destructive">Badge 3</Badge>
            </div>
          </CardContent>
          <CardFooter>
            <GradientButton>Primärer Button</GradientButton>
            <GradientButton variant="variant" className="ml-2">Sekundärer Button</GradientButton>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Weitere UI-Elemente</CardTitle>
            <CardDescription>Verschiedene UI-Komponenten zum Testen</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Buttons</h3>
              <div className="flex flex-wrap gap-2">
                <GradientButton variant="default">Default</GradientButton>
                <GradientButton variant="variant">Variant</GradientButton>
                <GradientButton variant="destructive">Destructive</GradientButton>
                <GradientButton variant="outline">Outline</GradientButton>
                <GradientButton variant="ghost">Ghost</GradientButton>
                <GradientButton variant="link">Link</GradientButton>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Gradient Button Showcase</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-600">Primary Actions</h4>
                  <div className="space-y-2">
                    <GradientButton variant="default" className="w-full">Save Changes</GradientButton>
                    <GradientButton variant="variant" className="w-full">Continue</GradientButton>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-600">Secondary Actions</h4>
                  <div className="space-y-2">
                    <GradientButton variant="outline" className="w-full">Cancel</GradientButton>
                    <GradientButton variant="ghost" className="w-full">Skip</GradientButton>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-600">Special Actions</h4>
                  <div className="space-y-2">
                    <GradientButton variant="destructive" className="w-full">Delete Account</GradientButton>
                    <GradientButton variant="link" className="w-full">Learn More</GradientButton>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">Badges</h3>
              <div className="flex flex-wrap gap-2">
                <Badge>Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="destructive">Destructive</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
