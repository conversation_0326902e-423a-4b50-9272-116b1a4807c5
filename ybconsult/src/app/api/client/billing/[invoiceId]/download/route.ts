import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../../auth/[...nextauth]/route";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";

const prisma = new PrismaClient();

/**
 * API-Route für das Herunterladen einer Rechnung als PDF.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { invoiceId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const invoiceId = params.invoiceId;
    
    // Rechnung abrufen
    const invoice = await prisma.invoice.findUnique({
      where: {
        id: invoiceId,
      },
      include: {
        order: {
          include: {
            client: true,
            vehicle: true,
          },
        },
      },
    });
    
    if (!invoice) {
      return NextResponse.json(
        { message: "Rechnung nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer berechtigt ist, diese Rechnung zu sehen
    const clientProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!clientProfile || invoice.order.clientId !== clientProfile.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Rechnung einzusehen." },
        { status: 403 }
      );
    }
    
    // PDF-Dokument erstellen
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4-Format
    
    // Schriftarten laden
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    
    // Seitenränder
    const margin = 50;
    const width = page.getWidth() - 2 * margin;
    
    // Firmenlogo und Kopfzeile
    page.drawText("YoungMobility GmbH", {
      x: margin,
      y: page.getHeight() - margin - 30,
      size: 24,
      font: helveticaBold,
      color: rgb(0.1, 0.1, 0.1),
    });
    
    page.drawText("Fahrzeugtransport-Rechnung", {
      x: margin,
      y: page.getHeight() - margin - 60,
      size: 14,
      font: helveticaFont,
      color: rgb(0.3, 0.3, 0.3),
    });
    
    // Trennlinie
    page.drawLine({
      start: { x: margin, y: page.getHeight() - margin - 80 },
      end: { x: page.getWidth() - margin, y: page.getHeight() - margin - 80 },
      thickness: 1,
      color: rgb(0.8, 0.8, 0.8),
    });
    
    // Rechnungsinformationen
    let y = page.getHeight() - margin - 120;
    
    page.drawText(`Rechnungsnummer: ${invoice.invoiceNumber}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 20;
    
    page.drawText(`Datum: ${new Date(invoice.createdAt).toLocaleDateString("de-DE")}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`Fälligkeitsdatum: ${new Date(invoice.dueDate).toLocaleDateString("de-DE")}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 40;
    
    // Kundeninformationen
    page.drawText("Rechnungsempfänger:", {
      x: margin,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 20;
    
    page.drawText(`${invoice.order.client.companyName}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`${invoice.order.client.contactPersonName}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`${invoice.order.client.addressLine1}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    if (invoice.order.client.addressLine2) {
      page.drawText(`${invoice.order.client.addressLine2}`, {
        x: margin,
        y,
        size: 12,
        font: helveticaFont,
      });
      y -= 20;
    }
    
    page.drawText(`${invoice.order.client.postalCode} ${invoice.order.client.city}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`${invoice.order.client.country}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 40;
    
    // Auftragsinformationen
    page.drawText("Auftragsdetails:", {
      x: margin,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 20;
    
    page.drawText(`Auftragsnummer: ${invoice.order.orderReference}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    if (invoice.order.vehicle) {
      page.drawText(`Fahrzeug: ${invoice.order.vehicle.make} ${invoice.order.vehicle.model}`, {
        x: margin,
        y,
        size: 12,
        font: helveticaFont,
      });
      y -= 20;
      
      page.drawText(`VIN: ${invoice.order.vehicle.vin}`, {
        x: margin,
        y,
        size: 12,
        font: helveticaFont,
      });
      y -= 20;
    }
    
    page.drawText(`Transport von: ${invoice.order.pickupCity}, ${invoice.order.pickupCountry}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`Transport nach: ${invoice.order.deliveryCity}, ${invoice.order.deliveryCountry}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 40;
    
    // Rechnungsposten
    page.drawText("Rechnungsposten:", {
      x: margin,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 30;
    
    // Tabellenkopf
    const col1 = margin;
    const col2 = margin + width * 0.6;
    const col3 = margin + width * 0.8;
    
    page.drawText("Beschreibung", {
      x: col1,
      y,
      size: 12,
      font: helveticaBold,
    });
    
    page.drawText("Menge", {
      x: col2,
      y,
      size: 12,
      font: helveticaBold,
    });
    
    page.drawText("Betrag", {
      x: col3,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 20;
    
    // Trennlinie
    page.drawLine({
      start: { x: margin, y: y + 10 },
      end: { x: page.getWidth() - margin, y: y + 10 },
      thickness: 1,
      color: rgb(0.8, 0.8, 0.8),
    });
    y -= 20;
    
    // Rechnungsposten
    page.drawText("Fahrzeugtransport", {
      x: col1,
      y,
      size: 12,
      font: helveticaFont,
    });
    
    page.drawText("1", {
      x: col2,
      y,
      size: 12,
      font: helveticaFont,
    });
    
    page.drawText(`${invoice.amount.toFixed(2)} €`, {
      x: col3,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 30;
    
    // Trennlinie
    page.drawLine({
      start: { x: margin, y: y + 10 },
      end: { x: page.getWidth() - margin, y: y + 10 },
      thickness: 1,
      color: rgb(0.8, 0.8, 0.8),
    });
    y -= 20;
    
    // Gesamtbetrag
    page.drawText("Gesamtbetrag:", {
      x: col1,
      y,
      size: 12,
      font: helveticaBold,
    });
    
    page.drawText(`${invoice.amount.toFixed(2)} €`, {
      x: col3,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 40;
    
    // Zahlungsinformationen
    page.drawText("Zahlungsinformationen:", {
      x: margin,
      y,
      size: 12,
      font: helveticaBold,
    });
    y -= 20;
    
    page.drawText("Bitte überweisen Sie den Rechnungsbetrag unter Angabe der Rechnungsnummer auf folgendes Konto:", {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText("Bank: Beispielbank", {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText("IBAN: DE12 3456 7890 1234 5678 90", {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText("BIC: EXAMPLEXXX", {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 20;
    
    page.drawText(`Zahlungsfrist: ${new Date(invoice.dueDate).toLocaleDateString("de-DE")}`, {
      x: margin,
      y,
      size: 12,
      font: helveticaFont,
    });
    y -= 40;
    
    // Fußzeile
    const footerY = margin + 30;
    
    page.drawText("YoungMobility GmbH | Musterstraße 123 | 10115 Berlin", {
      x: margin,
      y: footerY,
      size: 10,
      font: helveticaFont,
      color: rgb(0.5, 0.5, 0.5),
    });
    
    page.drawText("Steuernummer: 123/456/78901 | USt-IdNr.: DE123456789", {
      x: margin,
      y: footerY - 15,
      size: 10,
      font: helveticaFont,
      color: rgb(0.5, 0.5, 0.5),
    });
    
    // PDF als Byte-Array speichern
    const pdfBytes = await pdfDoc.save();
    
    // PDF als Antwort senden
    return new NextResponse(pdfBytes, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="Rechnung_${invoice.invoiceNumber}.pdf"`,
      },
    });
    
  } catch (error) {
    console.error("Fehler beim Erstellen der PDF-Rechnung:", error);
    return NextResponse.json(
      { message: "Beim Erstellen der PDF-Rechnung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
