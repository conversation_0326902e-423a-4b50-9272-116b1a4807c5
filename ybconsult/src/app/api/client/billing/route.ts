import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, InvoiceStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Rechnungen des angemeldeten Clients.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Client-Profil abrufen
    const clientProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!clientProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // URL-Parameter für Filterung und Paginierung
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const search = searchParams.get("search");
    const dateFrom = searchParams.get("dateFrom");
    const dateTo = searchParams.get("dateTo");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "10"), 50); // Max. 50 Einträge pro Seite
    const skip = (page - 1) * limit;
    
    // Filter für die Datenbankabfrage erstellen
    const filter: any = {
      order: {
        clientId: clientProfile.id,
        deletedAt: null,
      },
    };
    
    // Status-Filter hinzufügen, wenn angegeben
    if (status && Object.values(InvoiceStatus).includes(status as InvoiceStatus)) {
      filter.status = status;
    }
    
    // Datumsfilter hinzufügen, wenn angegeben
    if (dateFrom) {
      filter.createdAt = {
        ...(filter.createdAt || {}),
        gte: new Date(dateFrom),
      };
    }
    
    if (dateTo) {
      filter.createdAt = {
        ...(filter.createdAt || {}),
        lte: new Date(dateTo),
      };
    }
    
    // Suchfilter hinzufügen, wenn angegeben
    if (search) {
      filter.OR = [
        { invoiceNumber: { contains: search, mode: "insensitive" } },
        { order: { orderReference: { contains: search, mode: "insensitive" } } },
      ];
    }
    
    // Rechnungen abrufen
    const invoices = await prisma.invoice.findMany({
      where: filter,
      include: {
        order: {
          select: {
            id: true,
            orderReference: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });
    
    // Gesamtanzahl der Rechnungen für Paginierung
    const total = await prisma.invoice.count({
      where: filter,
    });
    
    return NextResponse.json({
      invoices,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Rechnungen:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Rechnungen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
