import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import crypto from "crypto";
import { addMonths } from "date-fns";

// Define the schema for API key creation
const createApiKeySchema = z.object({
  name: z.string().min(1, "Name is required"),
  expiryMonths: z.number().int().min(0),
});

/**
 * GET /api/client/api-keys
 * Retrieves all API keys for the authenticated client
 */
export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is a business client
    if (!session || session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get API keys
    const apiKeys = await prisma.apiKey.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        name: true,
        isActive: true,
        expiresAt: true,
        lastUsedAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    
    return NextResponse.json(apiKeys);
    
  } catch (error) {
    console.error("Error retrieving API keys:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/client/api-keys
 * Creates a new API key for the authenticated client
 */
export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is a business client
    if (!session || session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = createApiKeySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Invalid input data", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { name, expiryMonths } = validationResult.data;
    
    // Generate API key
    const key = crypto.randomBytes(32).toString("hex");
    
    // Calculate expiry date
    const expiresAt = expiryMonths > 0 ? addMonths(new Date(), expiryMonths) : null;
    
    // Create API key
    const apiKey = await prisma.apiKey.create({
      data: {
        key,
        name,
        isActive: true,
        expiresAt,
        userId: session.user.id,
      },
    });
    
    // Return the API key (only returned once for security)
    return NextResponse.json(
      {
        id: apiKey.id,
        key,
        name: apiKey.name,
        isActive: apiKey.isActive,
        expiresAt: apiKey.expiresAt,
        createdAt: apiKey.createdAt,
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error("Error creating API key:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}
