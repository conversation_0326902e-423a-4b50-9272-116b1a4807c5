import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Define the schema for API key update
const updateApiKeySchema = z.object({
  isActive: z.boolean().optional(),
  name: z.string().min(1, "Name is required").optional(),
});

/**
 * PATCH /api/client/api-keys/[keyId]
 * Updates an API key
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { keyId: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is a business client
    if (!session || session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const keyId = params.keyId;
    
    // Check if API key exists and belongs to the user
    const apiKey = await prisma.apiKey.findUnique({
      where: {
        id: keyId,
        userId: session.user.id,
      },
    });
    
    if (!apiKey) {
      return NextResponse.json(
        { message: "API key not found" },
        { status: 404 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = updateApiKeySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Invalid input data", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { isActive, name } = validationResult.data;
    
    // Update API key
    const updatedApiKey = await prisma.apiKey.update({
      where: {
        id: keyId,
      },
      data: {
        ...(isActive !== undefined && { isActive }),
        ...(name && { name }),
      },
    });
    
    return NextResponse.json({
      id: updatedApiKey.id,
      name: updatedApiKey.name,
      isActive: updatedApiKey.isActive,
      expiresAt: updatedApiKey.expiresAt,
      lastUsedAt: updatedApiKey.lastUsedAt,
      createdAt: updatedApiKey.createdAt,
      updatedAt: updatedApiKey.updatedAt,
    });
    
  } catch (error) {
    console.error("Error updating API key:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/client/api-keys/[keyId]
 * Deletes an API key
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { keyId: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is a business client
    if (!session || session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const keyId = params.keyId;
    
    // Check if API key exists and belongs to the user
    const apiKey = await prisma.apiKey.findUnique({
      where: {
        id: keyId,
        userId: session.user.id,
      },
    });
    
    if (!apiKey) {
      return NextResponse.json(
        { message: "API key not found" },
        { status: 404 }
      );
    }
    
    // Delete API key
    await prisma.apiKey.delete({
      where: {
        id: keyId,
      },
    });
    
    return NextResponse.json(
      { message: "API key deleted successfully" },
      { status: 200 }
    );
    
  } catch (error) {
    console.error("Error deleting API key:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}
