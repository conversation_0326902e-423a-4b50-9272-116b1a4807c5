import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

// Validierungsschema für die Profilaktualisierung
const updateProfileSchema = z.object({
  companyName: z.string().min(2, "Firmenname muss mindestens 2 Zeichen lang sein"),
  contactPersonName: z.string().min(2, "Name der Kontaktperson muss mindestens 2 Zeichen lang sein"),
  phoneNumber: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
  vatId: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Benutzerprofil aus der Datenbank abrufen
    const userProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!userProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Profildaten zurückgeben
    return NextResponse.json(userProfile, { status: 200 });
  } catch (error) {
    console.error("Fehler beim Abrufen des Profils:", error);
    return NextResponse.json(
      { message: "Beim Abrufen des Profils ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = updateProfileSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Überprüfen, ob das Profil existiert
    const existingProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!existingProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Profil aktualisieren
    const updatedProfile = await prisma.userProfileClient.update({
      where: { id: existingProfile.id },
      data: {
        companyName: data.companyName,
        contactPersonName: data.contactPersonName,
        phoneNumber: data.phoneNumber,
        addressLine1: data.addressLine1,
        addressLine2: data.addressLine2,
        city: data.city,
        postalCode: data.postalCode,
        country: data.country,
        vatId: data.vatId,
      },
    });
    
    return NextResponse.json(updatedProfile, { status: 200 });
  } catch (error) {
    console.error("Fehler beim Aktualisieren des Profils:", error);
    return NextResponse.json(
      { message: "Beim Aktualisieren des Profils ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
