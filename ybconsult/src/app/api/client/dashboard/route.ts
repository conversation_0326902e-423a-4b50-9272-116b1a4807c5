import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, OrderStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für Dashboard-Daten des Geschäftskunden.
 * Liefert Zusammenfassungen zu aktiven Aufträgen, anstehenden Aktionen und letzten Aktivitäten.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Client-Profil abrufen
    const clientProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!clientProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }

    // Aktive Aufträge zählen (Status: POSTED, DRIVER_ASSIGNED, IN_TRANSIT)
    const activeOrdersCount = await prisma.order.count({
      where: {
        clientId: clientProfile.id,
        status: {
          in: [
            OrderStatus.POSTED,
            OrderStatus.DRIVER_ASSIGNED,
            OrderStatus.IN_TRANSIT
          ]
        },
        deletedAt: null
      }
    });

    // Aufträge zählen, die Aufmerksamkeit benötigen
    // z.B. Aufträge mit Problemen oder Aufträge, die bestätigt werden müssen
    const pendingActionsCount = await prisma.order.count({
      where: {
        clientId: clientProfile.id,
        OR: [
          { status: OrderStatus.ISSUE_REPORTED },
          { status: OrderStatus.PENDING_CLIENT_CONFIRMATION }
        ],
        deletedAt: null
      }
    });

    // Letzte Aktivitäten abrufen (z.B. Statusänderungen, neue Nachrichten)
    const recentEvents = await prisma.orderEvent.findMany({
      where: {
        order: {
          clientId: clientProfile.id,
          deletedAt: null
        }
      },
      orderBy: {
        eventTimestamp: 'desc'
      },
      take: 5,
      include: {
        order: {
          select: {
            orderReference: true
          }
        }
      }
    });

    // Formatierte Aktivitäten für die Anzeige
    const recentActivity = recentEvents.map(event => {
      let description = '';
      
      switch (event.eventType) {
        case 'STATUS_CHANGED':
          description = `Status von Auftrag ${event.order.orderReference} geändert: ${event.description}`;
          break;
        case 'DRIVER_ASSIGNED':
          description = `Fahrer für Auftrag ${event.order.orderReference} zugewiesen`;
          break;
        case 'NOTE_ADDED':
          description = `Neue Notiz zu Auftrag ${event.order.orderReference}`;
          break;
        default:
          description = `${event.description} (Auftrag ${event.order.orderReference})`;
      }

      return {
        description,
        timestamp: event.eventTimestamp.toLocaleString('de-DE', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      };
    });

    // Dashboard-Daten zurückgeben
    return NextResponse.json({
      activeOrders: activeOrdersCount,
      pendingActions: pendingActionsCount,
      recentActivity
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Dashboard-Daten:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Dashboard-Daten ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
