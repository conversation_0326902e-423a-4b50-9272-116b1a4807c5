import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Markieren einer Benachrichtigung als gelesen.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { notificationId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const notificationId = params.notificationId;
    
    // Benachrichtigung abrufen
    const notification = await prisma.notification.findUnique({
      where: {
        id: notificationId,
      },
    });
    
    if (!notification) {
      return NextResponse.json(
        { message: "Benachrichtigung nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob die Benachrichtigung dem angemeldeten Benutzer gehört
    if (notification.userId !== session.user.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Benachrichtigung zu aktualisieren." },
        { status: 403 }
      );
    }
    
    // Benachrichtigung als gelesen markieren
    const updatedNotification = await prisma.notification.update({
      where: {
        id: notificationId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    
    return NextResponse.json(updatedNotification, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Markieren der Benachrichtigung als gelesen:", error);
    return NextResponse.json(
      { message: "Beim Markieren der Benachrichtigung als gelesen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
