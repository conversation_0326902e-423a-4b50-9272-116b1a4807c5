import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Markieren aller Benachrichtigungen eines Benutzers als gelesen.
 */
export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Alle ungelesenen Benachrichtigungen des Benutzers als gelesen markieren
    const result = await prisma.notification.updateMany({
      where: {
        userId: session.user.id,
        isRead: false,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    
    return NextResponse.json({
      message: `${result.count} Benachrichtigungen wurden als gelesen markiert.`,
      count: result.count,
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Markieren aller Benachrichtigungen als gelesen:", error);
    return NextResponse.json(
      { message: "Beim Markieren aller Benachrichtigungen als gelesen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
