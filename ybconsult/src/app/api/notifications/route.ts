import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Benachrichtigungen des angemeldeten Benutzers.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // URL-Parameter für Paginierung und Filter
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "50");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;
    const unreadOnly = searchParams.get("unreadOnly") === "true";
    
    // Filter erstellen
    const filter: any = {
      userId: session.user.id,
    };
    
    if (unreadOnly) {
      filter.isRead = false;
    }
    
    // Benachrichtigungen abrufen
    const notifications = await prisma.notification.findMany({
      where: filter,
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });
    
    // Gesamtanzahl der Benachrichtigungen für Paginierung
    const total = await prisma.notification.count({
      where: filter,
    });
    
    // Anzahl der ungelesenen Benachrichtigungen
    const unreadCount = await prisma.notification.count({
      where: {
        userId: session.user.id,
        isRead: false,
      },
    });
    
    return NextResponse.json({
      notifications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        unreadCount,
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Benachrichtigungen:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Benachrichtigungen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
