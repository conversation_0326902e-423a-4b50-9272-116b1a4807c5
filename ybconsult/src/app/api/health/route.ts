/**
 * Health API Route
 * 
 * Diese Datei enthält die Health API Route.
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */

import { NextResponse } from "next/server";
import { checkHealth } from "@/utils/monitoring";
import { withErrorHandling } from "@/utils/error-handling";

/**
 * GET-Handler für die Health API Route
 * @returns NextResponse mit Gesundheitsstatus
 */
export const GET = withErrorHandling(async () => {
  try {
    const healthStatus = await checkHealth();
    
    // Setze HTTP-Statuscode basierend auf Gesundheitsstatus
    let status = 200;
    
    if (healthStatus.status === "unhealthy") {
      status = 503; // Service Unavailable
    }
    
    return NextResponse.json(healthStatus, { status });
  } catch (error) {
    console.error("Failed to check health status:", error);
    
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: {
          message: "Failed to check health status",
          details: process.env.NODE_ENV === "development" ? String(error) : undefined,
        },
      },
      { status: 500 }
    );
  }
});
