/**
 * Tests für die Health API Route
 * 
 * Diese Datei enthält Tests für die Health API Route.
 * Teil der Phase 8: Comprehensive Automated Testing (YM-805)
 */

import { GET } from "./route";
import { checkHealth } from "@/utils/monitoring";

// Mock für monitoring.ts
vi.mock("@/utils/monitoring", () => ({
  checkHealth: vi.fn(),
}));

describe("Health API Route", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });
  
  it("returns 200 OK when all services are healthy", async () => {
    // Mock für checkHealth
    (checkHealth as jest.Mock).mockResolvedValue({
      status: "healthy",
      timestamp: new Date().toISOString(),
      services: {
        app: {
          status: "healthy",
        },
        database: {
          status: "healthy",
          responseTime: 5,
        },
        cache: {
          status: "healthy",
        },
      },
    });
    
    const response = await GET();
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.status).toBe("healthy");
    expect(data.services.app.status).toBe("healthy");
    expect(data.services.database.status).toBe("healthy");
    expect(data.services.cache.status).toBe("healthy");
  });
  
  it("returns 200 OK with degraded status when some services are degraded", async () => {
    // Mock für checkHealth
    (checkHealth as jest.Mock).mockResolvedValue({
      status: "degraded",
      timestamp: new Date().toISOString(),
      services: {
        app: {
          status: "healthy",
        },
        database: {
          status: "degraded",
          responseTime: 500,
          message: "High response time",
        },
        cache: {
          status: "healthy",
        },
      },
    });
    
    const response = await GET();
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.status).toBe("degraded");
    expect(data.services.app.status).toBe("healthy");
    expect(data.services.database.status).toBe("degraded");
    expect(data.services.database.message).toBe("High response time");
    expect(data.services.cache.status).toBe("healthy");
  });
  
  it("returns 503 Service Unavailable when services are unhealthy", async () => {
    // Mock für checkHealth
    (checkHealth as jest.Mock).mockResolvedValue({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      services: {
        app: {
          status: "healthy",
        },
        database: {
          status: "unhealthy",
          message: "Database connection failed",
        },
        cache: {
          status: "healthy",
        },
      },
    });
    
    const response = await GET();
    const data = await response.json();
    
    expect(response.status).toBe(503);
    expect(data.status).toBe("unhealthy");
    expect(data.services.database.status).toBe("unhealthy");
    expect(data.services.database.message).toBe("Database connection failed");
  });
  
  it("returns 500 Internal Server Error when checkHealth throws an error", async () => {
    // Mock für checkHealth
    (checkHealth as jest.Mock).mockRejectedValue(new Error("Something went wrong"));
    
    const response = await GET();
    const data = await response.json();
    
    expect(response.status).toBe(500);
    expect(data.error).toBeDefined();
    expect(data.error.message).toBe("Failed to check health status");
  });
});
