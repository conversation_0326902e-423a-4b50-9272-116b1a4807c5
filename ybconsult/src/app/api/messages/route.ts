import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createNotification } from "@/lib/notifications";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen von Nachrichten für einen Auftrag.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // URL-Parameter für den Auftrag und Paginierung
    const searchParams = request.nextUrl.searchParams;
    const orderId = searchParams.get("orderId");
    const limit = parseInt(searchParams.get("limit") || "50");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;
    
    if (!orderId) {
      return NextResponse.json(
        { message: "Auftrags-ID ist erforderlich." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Benutzer Zugriff auf den Auftrag hat
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        assignment: true,
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer der Kunde oder der zugewiesene Fahrer ist
    const isClient = order.clientId === session.user.id;
    const isAssignedDriver = order.assignment?.driverId === session.user.id;
    
    if (!isClient && !isAssignedDriver && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, auf diese Nachrichten zuzugreifen." },
        { status: 403 }
      );
    }
    
    // Nachrichten abrufen
    const messages = await prisma.message.findMany({
      where: {
        orderId,
        OR: [
          { senderId: session.user.id },
          { receiverId: session.user.id },
        ],
      },
      include: {
        sender: {
          select: {
            id: true,
            email: true,
            role: true,
            clientProfile: {
              select: {
                companyName: true,
                contactPersonName: true,
              },
            },
            driverProfile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        receiver: {
          select: {
            id: true,
            email: true,
            role: true,
            clientProfile: {
              select: {
                companyName: true,
                contactPersonName: true,
              },
            },
            driverProfile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
      skip,
      take: limit,
    });
    
    // Ungelesene Nachrichten als gelesen markieren, wenn der Benutzer der Empfänger ist
    const unreadMessages = messages.filter(
      (message) => !message.isRead && message.receiverId === session.user.id
    );
    
    if (unreadMessages.length > 0) {
      await prisma.message.updateMany({
        where: {
          id: {
            in: unreadMessages.map((message) => message.id),
          },
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });
    }
    
    // Gesamtanzahl der Nachrichten für Paginierung
    const total = await prisma.message.count({
      where: {
        orderId,
        OR: [
          { senderId: session.user.id },
          { receiverId: session.user.id },
        ],
      },
    });
    
    return NextResponse.json({
      messages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Nachrichten:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Nachrichten ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

/**
 * API-Route für das Senden einer neuen Nachricht.
 */
export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Request-Body parsen
    const body = await request.json();
    const { orderId, content, receiverId } = body;
    
    if (!orderId || !content || !receiverId) {
      return NextResponse.json(
        { message: "Auftrags-ID, Inhalt und Empfänger-ID sind erforderlich." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Auftrag existiert
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        assignment: true,
        clientProfile: true,
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer der Kunde oder der zugewiesene Fahrer ist
    const isClient = order.clientId === session.user.id;
    const isAssignedDriver = order.assignment?.driverId === session.user.id;
    
    if (!isClient && !isAssignedDriver && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, Nachrichten für diesen Auftrag zu senden." },
        { status: 403 }
      );
    }
    
    // Überprüfen, ob der Empfänger der Kunde oder der zugewiesene Fahrer ist
    const isReceiverClient = order.clientId === receiverId;
    const isReceiverDriver = order.assignment?.driverId === receiverId;
    
    if (!isReceiverClient && !isReceiverDriver) {
      return NextResponse.json(
        { message: "Der Empfänger muss der Kunde oder der zugewiesene Fahrer sein." },
        { status: 400 }
      );
    }
    
    // Nachricht erstellen
    const message = await prisma.message.create({
      data: {
        content,
        orderId,
        senderId: session.user.id,
        receiverId,
      },
    });
    
    // Benachrichtigung für den Empfänger erstellen
    const senderName = isClient 
      ? order.clientProfile?.companyName || "Kunde"
      : "Fahrer";
    
    await createNotification({
      userId: receiverId,
      type: "NEW_MESSAGE",
      title: "Neue Nachricht",
      message: `Sie haben eine neue Nachricht von ${senderName} zum Auftrag #${order.id.substring(0, 8)} erhalten.`,
      relatedEntityType: "Message",
      relatedEntityId: message.id,
      sendEmail: true,
    });
    
    return NextResponse.json(message, { status: 201 });
    
  } catch (error) {
    console.error("Fehler beim Senden der Nachricht:", error);
    return NextResponse.json(
      { message: "Beim Senden der Nachricht ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
