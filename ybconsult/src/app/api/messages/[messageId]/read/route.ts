import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Markieren einer Nachricht als gelesen.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const messageId = params.messageId;
    
    // Nachricht abrufen
    const message = await prisma.message.findUnique({
      where: {
        id: messageId,
      },
    });
    
    if (!message) {
      return NextResponse.json(
        { message: "Nachricht nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer der Empfänger der Nachricht ist
    if (message.receiverId !== session.user.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Nachricht als gelesen zu markieren." },
        { status: 403 }
      );
    }
    
    // Nachricht als gelesen markieren
    const updatedMessage = await prisma.message.update({
      where: {
        id: messageId,
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });
    
    return NextResponse.json(updatedMessage, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Markieren der Nachricht als gelesen:", error);
    return NextResponse.json(
      { message: "Beim Markieren der Nachricht als gelesen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
