import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Fotos zu einem Auftrag.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag abrufen, um Berechtigungen zu prüfen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      select: {
        id: true,
        clientId: true,
        assignment: {
          select: {
            driverId: true,
          },
        },
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Berechtigungsprüfung: Nur der Auftraggeber, der zugewiesene Fahrer oder ein Admin darf die Fotos sehen
    const isClient = session.user.role === "BUSINESS_CLIENT" && order.clientId === session.user.id;
    const isAssignedDriver = session.user.role === "DRIVER" && order.assignment?.driverId === session.user.id;
    const isAdmin = session.user.role === "ADMIN";
    
    if (!isClient && !isAssignedDriver && !isAdmin) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Fotos einzusehen." },
        { status: 403 }
      );
    }
    
    // Fotos abrufen
    const pickupPhotos = await prisma.handoverPhoto.findMany({
      where: {
        pickupOrderId: orderId,
      },
      orderBy: {
        uploadedAt: "desc",
      },
    });
    
    const deliveryPhotos = await prisma.handoverPhoto.findMany({
      where: {
        deliveryOrderId: orderId,
      },
      orderBy: {
        uploadedAt: "desc",
      },
    });
    
    return NextResponse.json({
      pickupPhotos,
      deliveryPhotos,
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Fotos:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Fotos ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
