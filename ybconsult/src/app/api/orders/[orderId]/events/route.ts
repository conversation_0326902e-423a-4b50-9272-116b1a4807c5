import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Ereignisse zu einem Auftrag.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag abrufen, um Berechtigungen zu prüfen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      select: {
        id: true,
        clientId: true,
        assignment: {
          select: {
            driverId: true,
          },
        },
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer berechtigt ist, die Ereignisse zu diesem Auftrag zu sehen
    if (session.user.role === "BUSINESS_CLIENT") {
      const clientProfile = await prisma.userProfileClient.findFirst({
        where: { userId: session.user.id },
      });
      
      if (!clientProfile || order.clientId !== clientProfile.id) {
        return NextResponse.json(
          { message: "Sie haben keine Berechtigung, diese Ereignisse einzusehen." },
          { status: 403 }
        );
      }
    } else if (session.user.role === "DRIVER") {
      const driverProfile = await prisma.userProfileDriver.findFirst({
        where: { userId: session.user.id },
      });
      
      if (!driverProfile || !order.assignment || order.assignment.driverId !== driverProfile.id) {
        return NextResponse.json(
          { message: "Sie haben keine Berechtigung, diese Ereignisse einzusehen." },
          { status: 403 }
        );
      }
    } else if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Ereignisse einzusehen." },
        { status: 403 }
      );
    }
    
    // Ereignisse abrufen
    const events = await prisma.orderEvent.findMany({
      where: {
        orderId: orderId,
      },
      orderBy: {
        eventTimestamp: "desc",
      },
    });
    
    return NextResponse.json(events, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Ereignisse:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Ereignisse ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
