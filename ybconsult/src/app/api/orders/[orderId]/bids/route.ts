import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Gebote für einen Auftrag.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag abrufen, um Berechtigungen zu prüfen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      select: {
        id: true,
        clientId: true,
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Berechtigungsprüfung: Nur der Auftraggeber oder ein Admin darf die Gebote sehen
    if (session.user.role === "BUSINESS_CLIENT" && order.clientId !== session.user.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Gebote einzusehen." },
        { status: 403 }
      );
    } else if (session.user.role === "DRIVER") {
      return NextResponse.json(
        { message: "Fahrer können keine Gebote anderer Fahrer einsehen." },
        { status: 403 }
      );
    }
    
    // Gebote abrufen
    const bids = await prisma.bid.findMany({
      where: {
        orderId: orderId,
      },
      include: {
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            averageRating: true,
          },
        },
        driverProfile: {
          select: {
            id: true,
            completedTransfers: true,
            transportationTypes: true,
            qualifications: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    
    // Daten für die Antwort formatieren
    const formattedBids = bids.map(bid => ({
      id: bid.id,
      amount: bid.amount,
      currency: bid.currency,
      message: bid.message,
      status: bid.status,
      createdAt: bid.createdAt,
      driver: {
        id: bid.driver.id,
        firstName: bid.driver.firstName,
        lastName: bid.driver.lastName,
        averageRating: bid.driver.averageRating || 0,
        completedTransfers: bid.driverProfile.completedTransfers,
        transportationTypes: bid.driverProfile.transportationTypes,
        qualifications: bid.driverProfile.qualifications,
      },
    }));
    
    return NextResponse.json(formattedBids, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Gebote:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Gebote ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
