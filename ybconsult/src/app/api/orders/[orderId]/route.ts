import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen eines einzelnen Auftrags anhand seiner ID.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag abrufen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      include: {
        vehicle: true,
        assignment: {
          include: {
            driver: {
              select: {
                firstName: true,
                lastName: true,
                averageRating: true,
              },
            },
          },
        },
        invoice: {
          select: {
            id: true,
            invoiceNumber: true,
            amount: true,
            status: true,
            dueDate: true,
            paidAt: true,
          },
        },
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer berechtigt ist, diesen Auftrag zu sehen
    if (session.user.role === "BUSINESS_CLIENT") {
      const clientProfile = await prisma.userProfileClient.findFirst({
        where: { userId: session.user.id },
      });
      
      if (!clientProfile || order.clientId !== clientProfile.id) {
        return NextResponse.json(
          { message: "Sie haben keine Berechtigung, diesen Auftrag einzusehen." },
          { status: 403 }
        );
      }
    } else if (session.user.role === "DRIVER") {
      const driverProfile = await prisma.userProfileDriver.findFirst({
        where: { userId: session.user.id },
      });
      
      if (!driverProfile || !order.assignment || order.assignment.driverId !== driverProfile.id) {
        return NextResponse.json(
          { message: "Sie haben keine Berechtigung, diesen Auftrag einzusehen." },
          { status: 403 }
        );
      }
    } else if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diesen Auftrag einzusehen." },
        { status: 403 }
      );
    }
    
    return NextResponse.json(order, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen des Auftrags:", error);
    return NextResponse.json(
      { message: "Beim Abrufen des Auftrags ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
