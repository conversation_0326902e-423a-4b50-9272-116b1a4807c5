import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { createOrderSchema } from "@/lib/validators/order.schemas";

const prisma = new PrismaClient();

/**
 * API-Route zur Berechnung des geschätzten Preises für einen Auftrag.
 * Verwendet die gleichen Eingabedaten wie die Auftragserstellung.
 */
export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = createOrderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Grundpreis basierend auf Fahrzeugtyp
    let basePrice = 0;
    switch (data.vehicle.vehicleType) {
      case "CAR":
        basePrice = 150;
        break;
      case "VAN":
        basePrice = 200;
        break;
      case "SUV":
        basePrice = 180;
        break;
      case "TRUCK":
        basePrice = 300;
        break;
      case "MOTORCYCLE":
        basePrice = 120;
        break;
      case "LUXURY":
        basePrice = 250;
        break;
      case "VINTAGE":
        basePrice = 280;
        break;
      case "OTHER":
        basePrice = 180;
        break;
      default:
        basePrice = 150;
    }
    
    // Entfernungszuschlag (vereinfachte Berechnung)
    // In einer realen Anwendung würde hier ein Geodienst zur Entfernungsberechnung verwendet werden
    const distanceFactor = 1.5; // Beispielwert
    
    // Zeitfaktor (basierend auf der Dringlichkeit)
    const pickupDays = Math.ceil((data.pickupDate.to.getTime() - data.pickupDate.from.getTime()) / (1000 * 60 * 60 * 24));
    const deliveryDays = Math.ceil((data.deliveryDate.to.getTime() - data.deliveryDate.from.getTime()) / (1000 * 60 * 60 * 24));
    
    let urgencyFactor = 1.0;
    if (pickupDays <= 1 || deliveryDays <= 1) {
      urgencyFactor = 1.3; // Zuschlag für sehr kurzfristige Termine
    } else if (pickupDays <= 3 || deliveryDays <= 3) {
      urgencyFactor = 1.1; // Leichter Zuschlag für kurzfristige Termine
    }
    
    // Gesamtpreis berechnen
    const estimatedPrice = basePrice * distanceFactor * urgencyFactor;
    
    // Auf zwei Dezimalstellen runden
    const roundedPrice = Math.round(estimatedPrice * 100) / 100;
    
    return NextResponse.json({
      estimatedPrice: roundedPrice,
      breakdown: {
        basePrice,
        distanceFactor,
        urgencyFactor
      }
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler bei der Preisberechnung:", error);
    return NextResponse.json(
      { message: "Bei der Preisberechnung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
