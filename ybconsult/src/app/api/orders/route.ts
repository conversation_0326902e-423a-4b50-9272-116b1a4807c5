import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, OrderStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { createOrderSchema } from "@/lib/validators/order.schemas";

const prisma = new PrismaClient();

/**
 * API-Route für die Erstellung eines neuen Auftrags.
 */
export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Client-Profil abrufen
    const clientProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!clientProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = createOrderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Grundpreis basierend auf Fahrzeugtyp (vereinfachte Berechnung)
    let basePrice = 0;
    switch (data.vehicle.vehicleType) {
      case "CAR":
        basePrice = 150;
        break;
      case "VAN":
        basePrice = 200;
        break;
      case "SUV":
        basePrice = 180;
        break;
      case "TRUCK":
        basePrice = 300;
        break;
      case "MOTORCYCLE":
        basePrice = 120;
        break;
      case "LUXURY":
        basePrice = 250;
        break;
      case "VINTAGE":
        basePrice = 280;
        break;
      case "OTHER":
        basePrice = 180;
        break;
      default:
        basePrice = 150;
    }
    
    // Entfernungszuschlag (vereinfachte Berechnung)
    const distanceFactor = 1.5; // Beispielwert
    
    // Zeitfaktor (basierend auf der Dringlichkeit)
    const pickupDays = Math.ceil((data.pickupDate.to.getTime() - data.pickupDate.from.getTime()) / (1000 * 60 * 60 * 24));
    const deliveryDays = Math.ceil((data.deliveryDate.to.getTime() - data.deliveryDate.from.getTime()) / (1000 * 60 * 60 * 24));
    
    let urgencyFactor = 1.0;
    if (pickupDays <= 1 || deliveryDays <= 1) {
      urgencyFactor = 1.3; // Zuschlag für sehr kurzfristige Termine
    } else if (pickupDays <= 3 || deliveryDays <= 3) {
      urgencyFactor = 1.1; // Leichter Zuschlag für kurzfristige Termine
    }
    
    // Gesamtpreis berechnen
    const estimatedPrice = basePrice * distanceFactor * urgencyFactor;
    
    // Auf zwei Dezimalstellen runden
    const roundedPrice = Math.round(estimatedPrice * 100) / 100;
    
    // Transaktion starten, um Auftrag und Fahrzeug in einem Schritt zu erstellen
    const result = await prisma.$transaction(async (tx) => {
      // Auftrag erstellen
      const order = await tx.order.create({
        data: {
          clientId: clientProfile.id,
          status: OrderStatus.POSTED,
          
          // Abholungsdaten
          pickupAddressLine1: data.pickup.addressLine1,
          pickupAddressLine2: data.pickup.addressLine2,
          pickupCity: data.pickup.city,
          pickupPostalCode: data.pickup.postalCode,
          pickupCountry: data.pickup.country,
          pickupLatitude: data.pickup.latitude,
          pickupLongitude: data.pickup.longitude,
          pickupContactName: data.pickup.contactName,
          pickupContactPhone: data.pickup.contactPhone,
          pickupDateFrom: data.pickupDate.from,
          pickupDateTo: data.pickupDate.to,
          
          // Lieferungsdaten
          deliveryAddressLine1: data.delivery.addressLine1,
          deliveryAddressLine2: data.delivery.addressLine2,
          deliveryCity: data.delivery.city,
          deliveryPostalCode: data.delivery.postalCode,
          deliveryCountry: data.delivery.country,
          deliveryLatitude: data.delivery.latitude,
          deliveryLongitude: data.delivery.longitude,
          deliveryContactName: data.delivery.contactName,
          deliveryContactPhone: data.delivery.contactPhone,
          deliveryDateFrom: data.deliveryDate.from,
          deliveryDateTo: data.deliveryDate.to,
          
          // Zusätzliche Informationen
          specialInstructions: data.specialInstructions,
          
          // Preisberechnung
          estimatedPrice: roundedPrice,
          currency: "EUR",
        },
      });
      
      // Fahrzeug erstellen und mit dem Auftrag verknüpfen
      const vehicle = await tx.vehicle.create({
        data: {
          orderId: order.id,
          vin: data.vehicle.vin,
          make: data.vehicle.make,
          model: data.vehicle.model,
          year: data.vehicle.year,
          color: data.vehicle.color,
          vehicleType: data.vehicle.vehicleType,
          licensePlate: data.vehicle.licensePlate,
          notes: data.vehicle.notes,
        },
      });
      
      // Ereignis für die Auftragserstellung protokollieren
      await tx.orderEvent.create({
        data: {
          orderId: order.id,
          eventType: "STATUS_CHANGED",
          description: "Auftrag erstellt und veröffentlicht",
          actorType: "CLIENT",
          actorId: session.user.id,
          eventData: {
            old_status: null,
            new_status: OrderStatus.POSTED,
          },
        },
      });
      
      return { order, vehicle };
    });
    
    return NextResponse.json(result.order, { status: 201 });
    
  } catch (error) {
    console.error("Fehler beim Erstellen des Auftrags:", error);
    return NextResponse.json(
      { message: "Beim Erstellen des Auftrags ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

/**
 * API-Route für das Abrufen aller Aufträge des angemeldeten Clients.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Geschäftskunde ist
    if (session.user.role !== "BUSINESS_CLIENT") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Client-Profil abrufen
    const clientProfile = await prisma.userProfileClient.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!clientProfile) {
      return NextResponse.json(
        { message: "Profil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // URL-Parameter für Filterung und Paginierung
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(parseInt(searchParams.get("limit") || "10"), 50); // Max. 50 Einträge pro Seite
    const skip = (page - 1) * limit;
    
    // Filter für die Datenbankabfrage erstellen
    const filter: any = {
      clientId: clientProfile.id,
      deletedAt: null,
    };
    
    // Status-Filter hinzufügen, wenn angegeben
    if (status) {
      if (status === "active") {
        filter.status = {
          in: [
            OrderStatus.POSTED,
            OrderStatus.DRIVER_ASSIGNED,
            OrderStatus.IN_TRANSIT
          ]
        };
      } else if (status === "completed") {
        filter.status = OrderStatus.COMPLETED;
      } else if (status === "pending") {
        filter.status = {
          in: [
            OrderStatus.ISSUE_REPORTED,
            OrderStatus.PENDING_CLIENT_CONFIRMATION
          ]
        };
      } else if (Object.values(OrderStatus).includes(status as OrderStatus)) {
        filter.status = status;
      }
    }
    
    // Aufträge abrufen
    const orders = await prisma.order.findMany({
      where: filter,
      include: {
        vehicle: true,
        assignment: {
          include: {
            driver: {
              select: {
                firstName: true,
                lastName: true,
                averageRating: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });
    
    // Gesamtanzahl der Aufträge für Paginierung
    const total = await prisma.order.count({
      where: filter,
    });
    
    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Aufträge:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Aufträge ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
