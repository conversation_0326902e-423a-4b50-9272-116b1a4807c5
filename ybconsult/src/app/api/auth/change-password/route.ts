import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import bcrypt from "bcrypt";
import { getServerSession } from "next-auth";
import { authOptions } from "../[...nextauth]/route";

const prisma = new PrismaClient();

// Validierungsschema für die Passwortänderung
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Aktuelles Passwort ist erforderlich"),
  newPassword: z.string().min(8, "Neues Passwort muss mindestens 8 Zeichen lang sein"),
});

export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = changePasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { currentPassword, newPassword } = validationResult.data;
    
    // Benutzer aus der Datenbank abrufen
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });
    
    if (!user || !user.password) {
      return NextResponse.json(
        { message: "Benutzer nicht gefunden oder kein Passwort gesetzt." },
        { status: 404 }
      );
    }
    
    // Aktuelles Passwort überprüfen
    const isValidPassword = await bcrypt.compare(
      currentPassword,
      user.password
    );
    
    if (!isValidPassword) {
      return NextResponse.json(
        { message: "Das aktuelle Passwort ist nicht korrekt." },
        { status: 400 }
      );
    }
    
    // Neues Passwort hashen
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Passwort aktualisieren
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword },
    });
    
    return NextResponse.json(
      { message: "Passwort erfolgreich geändert." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Fehler beim Ändern des Passworts:", error);
    return NextResponse.json(
      { message: "Beim Ändern des Passworts ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
