import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import crypto from "crypto";

const prisma = new PrismaClient();

// Validierungsschema für die Passwort-Reset-Anfrage
const forgotPasswordSchema = z.object({
  email: z.string().email("Ungültige E-Mail-Adresse"),
});

export async function POST(request: NextRequest) {
  try {
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = forgotPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { email } = validationResult.data;
    
    // Überprüfen, ob ein <PERSON>er mit dieser E-Mail existiert
    const user = await prisma.user.findUnique({
      where: { email },
    });
    
    // Aus Sicherheitsgründen geben wir immer eine erfolgreiche Antwort zurück,
    // auch wenn kein Benutzer gefunden wurde, um keine Informationen über existierende E-Mails preiszugeben
    if (!user) {
      return NextResponse.json(
        { message: "Wenn ein Konto mit dieser E-Mail existiert, wurde ein Link zum Zurücksetzen des Passworts gesendet." },
        { status: 200 }
      );
    }
    
    // Token generieren
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 Stunden gültig
    
    // Token in der Datenbank speichern
    await prisma.user.update({
      where: { id: user.id },
      data: {
        resetToken,
        resetTokenExpiry,
      },
    });
    
    // Reset-URL erstellen
    const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
    const resetUrl = `${baseUrl}/reset-password/${resetToken}`;
    
    // In einer echten Anwendung würde hier der E-Mail-Versand erfolgen
    console.log(`[DEV] Passwort-Reset-Link für ${email}: ${resetUrl}`);
    
    // TODO: E-Mail mit Reset-Link senden
    // Beispiel:
    // await sendEmail({
    //   to: email,
    //   subject: "Passwort zurücksetzen",
    //   text: `Klicken Sie auf den folgenden Link, um Ihr Passwort zurückzusetzen: ${resetUrl}`,
    //   html: `<p>Klicken Sie auf den folgenden Link, um Ihr Passwort zurückzusetzen:</p><p><a href="${resetUrl}">${resetUrl}</a></p>`,
    // });
    
    return NextResponse.json(
      { message: "Wenn ein Konto mit dieser E-Mail existiert, wurde ein Link zum Zurücksetzen des Passworts gesendet." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Fehler bei der Passwort-Reset-Anfrage:", error);
    return NextResponse.json(
      { message: "Bei der Verarbeitung Ihrer Anfrage ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
