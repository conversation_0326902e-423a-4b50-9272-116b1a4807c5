import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, UserRole, UserStatus } from "@prisma/client";
import bcrypt from "bcrypt";
import { z } from "zod";

const prisma = new PrismaClient();

// Validierungsschema für die Registrierungsdaten
const registerClientSchema = z.object({
  email: z.string().email("Ungültige E-Mail-Adresse"),
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
  companyName: z.string().min(2, "Firmenname muss mindestens 2 Zeichen lang sein"),
  contactPersonName: z.string().min(2, "Name der Kontaktperson muss mindestens 2 Zeichen lang sein"),
  phoneNumber: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
  vatId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = registerClientSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Überprüfen, ob die E-Mail bereits existiert
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });
    
    if (existingUser) {
      return NextResponse.json(
        { message: "Ein Benutzer mit dieser E-Mail-Adresse existiert bereits." },
        { status: 409 }
      );
    }
    
    // Passwort hashen
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(data.password, saltRounds);
    
    // Benutzer und Profil in einer Transaktion erstellen
    const result = await prisma.$transaction(async (tx) => {
      // Benutzer erstellen
      const user = await tx.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          role: UserRole.BUSINESS_CLIENT,
          status: UserStatus.PENDING_EMAIL_VERIFICATION,
        },
      });
      
      // Geschäftskundenprofil erstellen
      const clientProfile = await tx.userProfileClient.create({
        data: {
          userId: user.id,
          companyName: data.companyName,
          contactPersonName: data.contactPersonName,
          phoneNumber: data.phoneNumber,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          postalCode: data.postalCode,
          country: data.country,
          vatId: data.vatId,
        },
      });
      
      // Hier würde normalerweise der E-Mail-Versand für die Verifizierung erfolgen
      // TODO: E-Mail-Verifizierung implementieren
      
      return { user, clientProfile };
    });
    
    // Erfolgreiche Antwort senden (ohne sensible Daten)
    return NextResponse.json(
      {
        message: "Registrierung erfolgreich. Bitte überprüfen Sie Ihre E-Mails für die Verifizierung.",
        userId: result.user.id,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Fehler bei der Registrierung:", error);
    return NextResponse.json(
      { message: "Bei der Registrierung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
