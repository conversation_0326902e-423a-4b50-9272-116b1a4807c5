// /Users/<USER>/Coding/ybconsulting/ybconsult/src/app/api/auth/[...nextauth]/route.ts
import NextAuth, { NextAuthOptions } from "next-auth";
import Cred<PERSON>sProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { PrismaClient, UserRole, UserStatus } from "@prisma/client";
import bcrypt from "bcrypt";

// Erweitere die NextAuth Session-Typen
declare module "next-auth" {
  interface User {
    id: string;
    email: string;
    role: UserRole;
    status: UserStatus;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      role: string;
      status: string;
    }
  }
}

// Erweitere die JWT-Typen
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: UserRole;
    status: UserStatus;
  }
}

const prisma = new PrismaClient();

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify whatever fields you expect to be submitted.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          throw new Error("Bitte geben Sie E-Mail und Passwort ein.");
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (!user || !user.password) {
          throw new Error("Benutzer nicht gefunden oder kein Passwort gesetzt.");
        }

        // Überprüfen, ob der Benutzer aktiv ist
        if (user.status === "PENDING_EMAIL_VERIFICATION") {
          throw new Error("Bitte bestätigen Sie zuerst Ihre E-Mail-Adresse.");
        }

        if (user.status === "PENDING_ADMIN_APPROVAL") {
          throw new Error("Ihr Konto wartet noch auf die Freigabe durch einen Administrator.");
        }

        if (user.status === "SUSPENDED") {
          throw new Error("Ihr Konto wurde vorübergehend gesperrt. Bitte kontaktieren Sie den Support.");
        }

        if (user.status === "DELETED") {
          throw new Error("Dieses Konto existiert nicht mehr.");
        }

        const isValidPassword = await bcrypt.compare(
          credentials.password,
          user.password
        );

        if (!isValidPassword) {
          throw new Error("Ungültiges Passwort.");
        }

        // Aktualisiere lastLoginAt
        await prisma.user.update({
          where: { id: user.id },
          data: {
            // @ts-ignore - lastLoginAt existiert im Schema, aber TS kennt es noch nicht
            lastLoginAt: new Date()
          },
        });

        // Return user object if credentials are valid
        return {
          id: user.id,
          email: user.email,
          role: user.role,
          status: user.status,
        };
      },
    }),
    // ...add more providers here if needed (e.g., Google, GitHub)
  ],
  session: {
    // Use JSON Web Tokens for session handling
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }: { token: any, user: any }) {
      // Persist the user's role, status and id to the token
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.status = user.status;
      }
      return token;
    },
    async session({ session, token }: { session: any, token: any }) {
      // Send properties to the client, like an access_token and user id from a provider.
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.status = token.status as string;
      }
      return session;
    },
  },
  pages: {
    signIn: "/login", // Custom login page
    error: '/auth/error', // Error code passed in query string as ?error=
    signOut: '/auth/signout',
    verifyRequest: '/auth/verify-request', // (used for check email message)
    // newUser: '/auth/new-user' // New users will be directed here on first sign in (leave the property out to disable)
  },
  // Enable debug messages in the console if you are having problems
  debug: process.env.NODE_ENV === "development",
  secret: process.env.NEXTAUTH_SECRET, // Secret for signing JWTs
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };