import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Token aus der URL extrahieren
    const token = request.nextUrl.searchParams.get("token");
    
    if (!token) {
      return NextResponse.json(
        { message: "Token ist erforderlich." },
        { status: 400 }
      );
    }
    
    // Benutzer mit diesem Token suchen
    const user = await prisma.user.findFirst({
      where: {
        resetToken: token,
        resetTokenExpiry: {
          gt: new Date(), // Token muss noch gültig sein
        },
      },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Ungültiger oder abgelaufener Token." },
        { status: 400 }
      );
    }
    
    // Token ist gültig
    return NextResponse.json(
      { message: "Token ist gültig." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Fehler bei der Token-Validierung:", error);
    return NextResponse.json(
      { message: "Bei der Überprüfung des Tokens ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
