import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, UserRole, UserStatus, DocumentType, DocumentVerificationStatus } from "@prisma/client";
import bcrypt from "bcrypt";
import { z } from "zod";

const prisma = new PrismaClient();

// Validierungsschema für die Registrierungsdaten
const registerDriverSchema = z.object({
  email: z.string().email("Ungültige E-Mail-Adresse"),
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
  firstName: z.string().min(2, "Vorname muss mindestens 2 Zeichen lang sein"),
  lastName: z.string().min(2, "Nachname muss mindestens 2 Zeichen lang sein"),
  phoneNumber: z.string().min(5, "Telefonnummer ist erforderlich"),
  dateOfBirth: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
});

export async function POST(request: NextRequest) {
  try {
    // Multipart-Formular-Daten verarbeiten
    const formData = await request.formData();
    
    // Benutzerdaten extrahieren
    const userData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
      firstName: formData.get("firstName") as string,
      lastName: formData.get("lastName") as string,
      phoneNumber: formData.get("phoneNumber") as string,
      dateOfBirth: formData.get("dateOfBirth") as string || undefined,
      addressLine1: formData.get("addressLine1") as string,
      addressLine2: formData.get("addressLine2") as string || undefined,
      city: formData.get("city") as string,
      postalCode: formData.get("postalCode") as string,
      country: formData.get("country") as string,
    };
    
    // Daten validieren
    const validationResult = registerDriverSchema.safeParse(userData);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Überprüfen, ob die E-Mail bereits existiert
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });
    
    if (existingUser) {
      return NextResponse.json(
        { message: "Ein Benutzer mit dieser E-Mail-Adresse existiert bereits." },
        { status: 409 }
      );
    }
    
    // Passwort hashen
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(data.password, saltRounds);
    
    // Dokumente verarbeiten
    const documentFiles: Record<string, File> = {};
    
    // Führerschein (erforderlich)
    const drivingLicenseFile = formData.get(`documents[DRIVING_LICENSE]`) as File | null;
    if (!drivingLicenseFile) {
      return NextResponse.json(
        { message: "Führerschein ist erforderlich." },
        { status: 400 }
      );
    }
    documentFiles.DRIVING_LICENSE = drivingLicenseFile;
    
    // Optionale Dokumente
    const businessRegistrationFile = formData.get(`documents[BUSINESS_REGISTRATION]`) as File | null;
    if (businessRegistrationFile) {
      documentFiles.BUSINESS_REGISTRATION = businessRegistrationFile;
    }
    
    const insuranceFile = formData.get(`documents[INSURANCE]`) as File | null;
    if (insuranceFile) {
      documentFiles.INSURANCE = insuranceFile;
    }
    
    // Dokumente speichern (in einer echten Anwendung würden diese in S3 oder einen anderen Speicherdienst hochgeladen)
    // Hier simulieren wir das Speichern und generieren URLs
    const documentUrls: Record<string, { url: string, fileName: string, mimeType: string, fileSize: number }> = {};
    
    for (const [type, file] of Object.entries(documentFiles)) {
      // In einer echten Anwendung würde hier der Upload zu S3 oder einem anderen Speicherdienst erfolgen
      // Hier generieren wir eine simulierte URL
      const simulatedUrl = `https://storage.example.com/documents/${Date.now()}-${file.name.replace(/\s+/g, '-')}`;
      
      documentUrls[type] = {
        url: simulatedUrl,
        fileName: file.name,
        mimeType: file.type,
        fileSize: file.size,
      };
    }
    
    // Benutzer, Profil und Dokumente in einer Transaktion erstellen
    const result = await prisma.$transaction(async (tx) => {
      // Benutzer erstellen
      const user = await tx.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          role: UserRole.DRIVER,
          status: UserStatus.PENDING_ADMIN_APPROVAL, // Fahrer benötigen Admin-Genehmigung
        },
      });
      
      // Fahrerprofil erstellen
      const driverProfile = await tx.userProfileDriver.create({
        data: {
          userId: user.id,
          firstName: data.firstName,
          lastName: data.lastName,
          phoneNumber: data.phoneNumber,
          dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : undefined,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          postalCode: data.postalCode,
          country: data.country,
        },
      });
      
      // Dokumente erstellen
      const documents = [];
      
      for (const [type, fileInfo] of Object.entries(documentUrls)) {
        const document = await tx.document.create({
          data: {
            documentType: type as DocumentType,
            fileName: fileInfo.fileName,
            fileUrl: fileInfo.url,
            mimeType: fileInfo.mimeType,
            fileSize: fileInfo.fileSize,
            verificationStatus: DocumentVerificationStatus.PENDING,
            driverProfileId: driverProfile.id,
          },
        });
        
        documents.push(document);
      }
      
      // Hier würde normalerweise der E-Mail-Versand für die Verifizierung erfolgen
      // TODO: E-Mail-Verifizierung implementieren
      
      return { user, driverProfile, documents };
    });
    
    // Erfolgreiche Antwort senden (ohne sensible Daten)
    return NextResponse.json(
      {
        message: "Registrierung erfolgreich. Ihre Dokumente werden geprüft und Ihr Konto wird nach erfolgreicher Prüfung freigeschaltet.",
        userId: result.user.id,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Fehler bei der Registrierung:", error);
    return NextResponse.json(
      { message: "Bei der Registrierung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
