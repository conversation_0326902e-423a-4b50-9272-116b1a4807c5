import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { z } from "zod";
import bcrypt from "bcrypt";

const prisma = new PrismaClient();

// Validierungsschema für die Passwort-Reset-Anfrage
const resetPasswordSchema = z.object({
  token: z.string().min(1, "Token ist erforderlich"),
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
});

export async function POST(request: NextRequest) {
  try {
    // Daten aus dem Request-Body extrahieren
    const body = await request.json();
    
    // Daten validieren
    const validationResult = resetPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Validierungsfehler", errors: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { token, password } = validationResult.data;
    
    // Benutzer mit diesem Token suchen
    const user = await prisma.user.findFirst({
      where: {
        resetToken: token,
        resetTokenExpiry: {
          gt: new Date(), // Token muss noch gültig sein
        },
      },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Ungültiger oder abgelaufener Token." },
        { status: 400 }
      );
    }
    
    // Passwort hashen
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // Passwort aktualisieren und Token zurücksetzen
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
      },
    });
    
    return NextResponse.json(
      { message: "Passwort erfolgreich zurückgesetzt." },
      { status: 200 }
    );
  } catch (error) {
    console.error("Fehler beim Zurücksetzen des Passworts:", error);
    return NextResponse.json(
      { message: "Beim Zurücksetzen des Passworts ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
