import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createNotification } from "@/lib/notifications";

const prisma = new PrismaClient();

/**
 * API-Route für das Ändern des Status eines Benutzers (nur für Admins).
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const userId = params.userId;
    
    // Request-Body parsen
    const body = await request.json();
    const { status, reason } = body;
    
    if (!status || !["ACTIVE", "SUSPENDED", "DELETED"].includes(status)) {
      return NextResponse.json(
        { message: "Ungültiger Status. Erlaubte Status sind 'ACTIVE', 'SUSPENDED' oder 'DELETED'." },
        { status: 400 }
      );
    }
    
    // Benutzer abrufen
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Benutzer nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Benutzer aktualisieren
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        status,
        deletedAt: status === "DELETED" ? new Date() : null,
      },
    });
    
    // Benachrichtigung erstellen
    let notificationType, notificationTitle, notificationMessage;
    
    if (status === "ACTIVE") {
      notificationType = "ACCOUNT_ACTIVATED";
      notificationTitle = "Konto aktiviert";
      notificationMessage = "Ihr Konto wurde von einem Administrator aktiviert. Sie können sich jetzt anmelden und die Plattform nutzen.";
    } else if (status === "SUSPENDED") {
      notificationType = "ACCOUNT_SUSPENDED";
      notificationTitle = "Konto gesperrt";
      notificationMessage = `Ihr Konto wurde von einem Administrator gesperrt. Grund: ${reason || "Keine Angabe"}. Bitte kontaktieren Sie den Support für weitere Informationen.`;
    } else if (status === "DELETED") {
      notificationType = "ACCOUNT_DELETED";
      notificationTitle = "Konto gelöscht";
      notificationMessage = `Ihr Konto wurde von einem Administrator gelöscht. Grund: ${reason || "Keine Angabe"}. Bitte kontaktieren Sie den Support für weitere Informationen.`;
    }
    
    if (notificationType) {
      await createNotification({
        userId,
        type: notificationType,
        title: notificationTitle,
        message: notificationMessage,
        sendEmail: true,
      });
    }
    
    return NextResponse.json({
      user: updatedUser,
      message: `Benutzerstatus erfolgreich auf '${status}' geändert.`,
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Ändern des Benutzerstatus:", error);
    return NextResponse.json(
      { message: "Beim Ändern des Benutzerstatus ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
