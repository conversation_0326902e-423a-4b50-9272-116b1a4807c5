import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen eines einzelnen Benutzers (nur für Admins).
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "<PERSON>ugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const userId = params.userId;
    
    // Benutzer abrufen
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        clientProfile: true,
        driverProfile: {
          include: {
            documents: true,
          },
        },
      },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Benutzer nicht gefunden." },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ user }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen des Benutzers:", error);
    return NextResponse.json(
      { message: "Beim Abrufen des Benutzers ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

/**
 * API-Route für das Aktualisieren eines Benutzers (nur für Admins).
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const userId = params.userId;
    
    // Request-Body parsen
    const body = await request.json();
    const { status, role } = body;
    
    // Benutzer abrufen
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Benutzer nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Benutzer aktualisieren
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        status: status || undefined,
        role: role || undefined,
      },
    });
    
    return NextResponse.json({ user: updatedUser }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Aktualisieren des Benutzers:", error);
    return NextResponse.json(
      { message: "Beim Aktualisieren des Benutzers ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
