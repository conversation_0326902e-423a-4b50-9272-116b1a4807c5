import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createNotification } from "@/lib/notifications";

const prisma = new PrismaClient();

/**
 * API-Route für die Verifizierung eines Benutzers (nur für Admins).
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const userId = params.userId;
    
    // Request-Body parsen
    const body = await request.json();
    const { action, reason } = body;
    
    if (!action || (action !== "approve" && action !== "reject")) {
      return NextResponse.json(
        { message: "Ungültige Aktion. Erlaubte Aktionen sind 'approve' oder 'reject'." },
        { status: 400 }
      );
    }
    
    // Benutzer abrufen
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });
    
    if (!user) {
      return NextResponse.json(
        { message: "Benutzer nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Benutzer auf Verifizierung wartet
    if (user.status !== "PENDING_ADMIN_APPROVAL") {
      return NextResponse.json(
        { message: "Der Benutzer wartet nicht auf Verifizierung." },
        { status: 400 }
      );
    }
    
    // Benutzer aktualisieren
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        status: action === "approve" ? "ACTIVE" : "SUSPENDED",
      },
    });
    
    // Benachrichtigung erstellen
    if (action === "approve") {
      await createNotification({
        userId,
        type: "ACCOUNT_APPROVED",
        title: "Konto freigegeben",
        message: "Ihr Konto wurde von einem Administrator freigegeben. Sie können sich jetzt anmelden und die Plattform nutzen.",
        sendEmail: true,
      });
    } else {
      await createNotification({
        userId,
        type: "ACCOUNT_REJECTED",
        title: "Konto abgelehnt",
        message: `Ihr Konto wurde von einem Administrator abgelehnt. Grund: ${reason || "Keine Angabe"}. Bitte kontaktieren Sie den Support für weitere Informationen.`,
        sendEmail: true,
      });
    }
    
    return NextResponse.json({
      user: updatedUser,
      message: action === "approve" ? "Benutzer erfolgreich freigegeben." : "Benutzer erfolgreich abgelehnt.",
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler bei der Benutzerverifizierung:", error);
    return NextResponse.json(
      { message: "Bei der Benutzerverifizierung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
