import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller Benutzer (nur für Admins).
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // URL-Parameter für Paginierung und Filter
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "20");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;
    const role = searchParams.get("role");
    const status = searchParams.get("status");
    const search = searchParams.get("search");
    
    // Filter erstellen
    const filter: any = {};
    
    if (role) {
      filter.role = role;
    }
    
    if (status) {
      filter.status = status;
    }
    
    if (search) {
      filter.OR = [
        {
          email: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          clientProfile: {
            OR: [
              {
                companyName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                contactPersonName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            ],
          },
        },
        {
          driverProfile: {
            OR: [
              {
                firstName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            ],
          },
        },
      ];
    }
    
    // Benutzer abrufen
    const users = await prisma.user.findMany({
      where: filter,
      include: {
        clientProfile: true,
        driverProfile: {
          include: {
            documents: {
              select: {
                id: true,
                documentType: true,
                verificationStatus: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });
    
    // Gesamtanzahl der Benutzer für Paginierung
    const total = await prisma.user.count({
      where: filter,
    });
    
    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Benutzer:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Benutzer ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
