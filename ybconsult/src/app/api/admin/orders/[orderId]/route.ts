import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen eines einzelnen Auftrags mit Geboten für Admins.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag mit allen relevanten Informationen abrufen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      include: {
        vehicle: true,
        client: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        clientProfile: {
          select: {
            id: true,
            companyName: true,
          },
        },
        assignment: {
          include: {
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                averageRating: true,
              },
            },
            driverProfile: {
              select: {
                id: true,
                completedTransfers: true,
                transportationTypes: true,
              },
            },
          },
        },
        bids: {
          include: {
            driver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                averageRating: true,
              },
            },
            driverProfile: {
              select: {
                id: true,
                completedTransfers: true,
                transportationTypes: true,
                qualifications: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        invoice: {
          select: {
            id: true,
            invoiceNumber: true,
            amount: true,
            status: true,
            dueDate: true,
            paidAt: true,
          },
        },
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Daten für die Antwort formatieren
    const formattedOrder = {
      ...order,
      bids: order.bids.map(bid => ({
        id: bid.id,
        amount: bid.amount,
        currency: bid.currency,
        message: bid.message,
        status: bid.status,
        createdAt: bid.createdAt,
        driver: {
          id: bid.driver.id,
          firstName: bid.driver.firstName,
          lastName: bid.driver.lastName,
          averageRating: bid.driver.averageRating || 0,
          completedTransfers: bid.driverProfile.completedTransfers,
          transportationTypes: bid.driverProfile.transportationTypes,
          qualifications: bid.driverProfile.qualifications,
          // Hier könnten weitere Berechnungen wie Entfernung zum Abholort hinzugefügt werden
          // distanceToPickup: calculateDistance(bid.driverProfile.lastKnownLocation, order.pickupLatitude, order.pickupLongitude),
        },
      })),
    };
    
    return NextResponse.json(formattedOrder, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen des Auftrags:", error);
    return NextResponse.json(
      { message: "Beim Abrufen des Auftrags ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
