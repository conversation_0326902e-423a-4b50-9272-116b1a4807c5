import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, OrderStatus, AssignmentStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { z } from "zod";

const prisma = new PrismaClient();

// Validierungsschema für die Anfrage
const assignDriverSchema = z.object({
  driverId: z.string().min(1, "Fahrer-ID ist erforderlich"),
});

/**
 * API-Route für die Zuweisung eines Fahrers zu einem Auftrag (nur für Admins).
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Admin ist
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const orderId = params.orderId;
    
    // Auftrag abrufen
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        deletedAt: null,
      },
      include: {
        assignment: true,
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Auftrag bereits einem Fahrer zugewiesen ist
    if (order.assignment) {
      return NextResponse.json(
        { message: "Dieser Auftrag ist bereits einem Fahrer zugewiesen." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Auftrag in einem Zustand ist, der eine Zuweisung erlaubt
    const allowedStatuses = ["POSTED", "BIDDING"];
    if (!allowedStatuses.includes(order.status)) {
      return NextResponse.json(
        { message: `Fahrerzuweisung nicht möglich im Status "${order.status}".` },
        { status: 400 }
      );
    }
    
    // Anfragedaten validieren
    const body = await request.json();
    const validationResult = assignDriverSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Anfragedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { driverId } = validationResult.data;
    
    // Überprüfen, ob der Fahrer existiert
    const driver = await prisma.user.findUnique({
      where: {
        id: driverId,
        role: "DRIVER",
      },
      include: {
        driverProfile: true,
      },
    });
    
    if (!driver || !driver.driverProfile) {
      return NextResponse.json(
        { message: "Fahrer nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Fahrer ein Gebot für diesen Auftrag abgegeben hat
    const bid = await prisma.bid.findFirst({
      where: {
        orderId: orderId,
        driverId: driverId,
      },
    });
    
    // Transaktion starten, um Auftragsstatus zu aktualisieren und Zuweisung zu erstellen
    const result = await prisma.$transaction(async (tx) => {
      // Auftragsstatus aktualisieren
      const updatedOrder = await tx.order.update({
        where: {
          id: orderId,
        },
        data: {
          status: OrderStatus.DRIVER_ASSIGNED,
        },
      });
      
      // Zuweisung erstellen
      const assignment = await tx.assignment.create({
        data: {
          orderId: orderId,
          driverId: driverId,
          driverProfileId: driver.driverProfile.id,
          status: AssignmentStatus.PENDING_DRIVER_ACCEPTANCE,
        },
      });
      
      // Auftragsereignis erstellen
      const orderEvent = await tx.orderEvent.create({
        data: {
          orderId: orderId,
          eventType: "DRIVER_ASSIGNED",
          description: `Fahrer ${driver.firstName} ${driver.lastName} wurde dem Auftrag zugewiesen`,
          actorType: "ADMIN",
          actorId: session.user.id,
          eventData: {
            driverId: driverId,
            driverName: `${driver.firstName} ${driver.lastName}`,
            assignmentId: assignment.id,
          },
        },
      });
      
      // Benachrichtigung für den Fahrer erstellen
      const driverNotification = await tx.notification.create({
        data: {
          userId: driverId,
          type: "ASSIGNMENT_CREATED",
          title: "Neuer Auftrag zugewiesen",
          message: `Sie wurden dem Auftrag #${order.orderReference} zugewiesen. Bitte bestätigen oder lehnen Sie ab.`,
          relatedEntityType: "Order",
          relatedEntityId: orderId,
        },
      });
      
      // Benachrichtigung für den Kunden erstellen
      const clientNotification = await tx.notification.create({
        data: {
          userId: order.clientId,
          type: "DRIVER_ASSIGNED",
          title: "Fahrer zugewiesen",
          message: `Ein Fahrer wurde Ihrem Auftrag #${order.orderReference} zugewiesen.`,
          relatedEntityType: "Order",
          relatedEntityId: orderId,
        },
      });
      
      return { updatedOrder, assignment, orderEvent, driverNotification, clientNotification };
    });
    
    // TODO: E-Mail-Benachrichtigungen senden (in YM-504 implementieren)
    
    return NextResponse.json(result, { status: 200 });
    
  } catch (error) {
    console.error("Fehler bei der Fahrerzuweisung:", error);
    return NextResponse.json(
      { message: "Bei der Fahrerzuweisung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
