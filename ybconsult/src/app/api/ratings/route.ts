import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { createNotification } from "@/lib/notifications";

const prisma = new PrismaClient();

/**
 * API-Route für das Erstellen einer Bewertung.
 */
export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Request-Body parsen
    const body = await request.json();
    const { orderId, ratedUserId, score, comment } = body;
    
    if (!orderId || !ratedUserId || !score) {
      return NextResponse.json(
        { message: "Auftrags-ID, <PERSON>utzer-ID und Bewertung sind erforderlich." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Score im gültigen Bereich liegt (1-5)
    if (score < 1 || score > 5) {
      return NextResponse.json(
        { message: "Die Bewertung muss zwischen 1 und 5 liegen." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Auftrag existiert und abgeschlossen ist
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        assignment: true,
        clientProfile: true,
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Auftrag abgeschlossen ist
    if (order.status !== "COMPLETED") {
      return NextResponse.json(
        { message: "Bewertungen können nur für abgeschlossene Aufträge abgegeben werden." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Benutzer der Kunde oder der zugewiesene Fahrer ist
    const isClient = order.clientId === session.user.id;
    const isAssignedDriver = order.assignment?.driverId === session.user.id;
    
    if (!isClient && !isAssignedDriver) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, eine Bewertung für diesen Auftrag abzugeben." },
        { status: 403 }
      );
    }
    
    // Überprüfen, ob der zu bewertende Benutzer der Kunde oder der zugewiesene Fahrer ist
    const isRatedUserClient = order.clientId === ratedUserId;
    const isRatedUserDriver = order.assignment?.driverId === ratedUserId;
    
    if (!isRatedUserClient && !isRatedUserDriver) {
      return NextResponse.json(
        { message: "Der zu bewertende Benutzer muss der Kunde oder der zugewiesene Fahrer sein." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der Benutzer sich nicht selbst bewertet
    if (session.user.id === ratedUserId) {
      return NextResponse.json(
        { message: "Sie können sich nicht selbst bewerten." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob bereits eine Bewertung existiert
    const existingRating = await prisma.rating.findFirst({
      where: {
        orderId,
        raterId: session.user.id,
        ratedUserId,
      },
    });
    
    if (existingRating) {
      return NextResponse.json(
        { message: "Sie haben bereits eine Bewertung für diesen Benutzer abgegeben." },
        { status: 400 }
      );
    }
    
    // Bewertung erstellen
    const rating = await prisma.rating.create({
      data: {
        score,
        comment,
        orderId,
        raterId: session.user.id,
        ratedUserId,
      },
    });
    
    // Benachrichtigung für den bewerteten Benutzer erstellen
    const raterName = isClient 
      ? order.clientProfile?.companyName || "Kunde"
      : "Fahrer";
    
    await createNotification({
      userId: ratedUserId,
      type: "NEW_RATING",
      title: "Neue Bewertung",
      message: `Sie haben eine neue Bewertung von ${raterName} für den Auftrag #${order.id.substring(0, 8)} erhalten.`,
      relatedEntityType: "Rating",
      relatedEntityId: rating.id,
      sendEmail: true,
    });
    
    return NextResponse.json(rating, { status: 201 });
    
  } catch (error) {
    console.error("Fehler beim Erstellen der Bewertung:", error);
    return NextResponse.json(
      { message: "Beim Erstellen der Bewertung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

/**
 * API-Route für das Abrufen von Bewertungen für einen Benutzer.
 */
export async function GET(request: NextRequest) {
  try {
    // URL-Parameter für den Benutzer und Paginierung
    const searchParams = request.nextUrl.searchParams;
    const userId = searchParams.get("userId");
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;
    
    if (!userId) {
      return NextResponse.json(
        { message: "Benutzer-ID ist erforderlich." },
        { status: 400 }
      );
    }
    
    // Bewertungen abrufen
    const ratings = await prisma.rating.findMany({
      where: {
        ratedUserId: userId,
      },
      include: {
        rater: {
          select: {
            id: true,
            email: true,
            role: true,
            clientProfile: {
              select: {
                companyName: true,
                contactPersonName: true,
              },
            },
            driverProfile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        order: {
          select: {
            id: true,
            title: true,
            status: true,
            createdAt: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });
    
    // Durchschnittliche Bewertung berechnen
    const averageRating = await prisma.rating.aggregate({
      where: {
        ratedUserId: userId,
      },
      _avg: {
        score: true,
      },
      _count: true,
    });
    
    // Gesamtanzahl der Bewertungen für Paginierung
    const total = await prisma.rating.count({
      where: {
        ratedUserId: userId,
      },
    });
    
    return NextResponse.json({
      ratings,
      averageRating: {
        score: averageRating._avg.score || 0,
        count: averageRating._count,
      },
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Bewertungen:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Bewertungen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
