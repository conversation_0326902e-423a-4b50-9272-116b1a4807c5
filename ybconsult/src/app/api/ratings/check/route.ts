import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für die Überprüfung, ob ein Benutzer bereits eine Bewertung abgegeben hat.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // URL-Parameter
    const searchParams = request.nextUrl.searchParams;
    const orderId = searchParams.get("orderId");
    const raterId = searchParams.get("raterId");
    const ratedUserId = searchParams.get("ratedUserId");
    
    if (!orderId || !raterId || !ratedUserId) {
      return NextResponse.json(
        { message: "Auftrags-ID, Bewerter-ID und Bewerteter-ID sind erforderlich." },
        { status: 400 }
      );
    }
    
    // Überprüfen, ob der angemeldete Benutzer der Bewerter ist
    if (session.user.id !== raterId) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Informationen abzurufen." },
        { status: 403 }
      );
    }
    
    // Bewertung suchen
    const rating = await prisma.rating.findFirst({
      where: {
        orderId,
        raterId,
        ratedUserId,
      },
    });
    
    return NextResponse.json({
      hasRated: !!rating,
      rating: rating ? {
        id: rating.id,
        score: rating.score,
        createdAt: rating.createdAt,
      } : null,
    }, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Überprüfen der Bewertung:", error);
    return NextResponse.json(
      { message: "Beim Überprüfen der Bewertung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
