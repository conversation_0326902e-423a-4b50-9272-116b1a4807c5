import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Define the schema for ticket update
const updateTicketSchema = z.object({
  status: z.enum(["OPEN", "IN_PROGRESS", "RESOLVED", "CLOSED"]).optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]).optional(),
});

// Define the schema for ticket response
const ticketResponseSchema = z.object({
  message: z.string().min(1, "Antwort darf nicht leer sein"),
});

/**
 * GET /api/support/tickets/[ticketId]
 * Retrieves a specific support ticket
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { ticketId: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 401 }
      );
    }
    
    const isAdmin = session.user.role === "ADMIN";
    const userId = session.user.id;
    
    // Get ticket
    const ticket = await prisma.supportTicket.findUnique({
      where: { id: params.ticketId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
            clientProfile: {
              select: {
                companyName: true,
                contactPersonName: true,
              },
            },
            driverProfile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        responses: {
          orderBy: { createdAt: "asc" },
          include: {
            responder: {
              select: {
                id: true,
                email: true,
                role: true,
              },
            },
          },
        },
      },
    });
    
    // Check if ticket exists
    if (!ticket) {
      return NextResponse.json(
        { message: "Support-Ticket nicht gefunden" },
        { status: 404 }
      );
    }
    
    // Check if user is authorized to view this ticket
    if (!isAdmin && ticket.userId !== userId) {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 403 }
      );
    }
    
    return NextResponse.json(ticket);
    
  } catch (error) {
    console.error("Fehler beim Abrufen des Support-Tickets:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/support/tickets/[ticketId]
 * Updates a support ticket (admin only)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { ticketId: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is admin
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = updateTicketSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Eingabedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { status, priority } = validationResult.data;
    
    // Check if ticket exists
    const ticket = await prisma.supportTicket.findUnique({
      where: { id: params.ticketId },
      select: { id: true, userId: true },
    });
    
    if (!ticket) {
      return NextResponse.json(
        { message: "Support-Ticket nicht gefunden" },
        { status: 404 }
      );
    }
    
    // Update data
    const updateData: any = {};
    
    if (status) {
      updateData.status = status;
      
      // If status is RESOLVED, set resolvedAt
      if (status === "RESOLVED") {
        updateData.resolvedAt = new Date();
      }
    }
    
    if (priority) {
      updateData.priority = priority;
    }
    
    // Update ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: params.ticketId },
      data: updateData,
    });
    
    // Create notification for ticket owner
    await prisma.notification.create({
      data: {
        userId: ticket.userId,
        type: "SUPPORT_TICKET_UPDATE",
        title: "Support-Ticket aktualisiert",
        message: `Ihr Support-Ticket wurde aktualisiert. Status: ${status || "unverändert"}, Priorität: ${priority || "unverändert"}`,
        relatedEntityType: "SupportTicket",
        relatedEntityId: ticket.id,
      },
    });
    
    return NextResponse.json(updatedTicket);
    
  } catch (error) {
    console.error("Fehler beim Aktualisieren des Support-Tickets:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/support/tickets/[ticketId]/responses
 * Adds a response to a support ticket
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { ticketId: string } }
) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 401 }
      );
    }
    
    const isAdmin = session.user.role === "ADMIN";
    const userId = session.user.id;
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = ticketResponseSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Eingabedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { message } = validationResult.data;
    
    // Check if ticket exists
    const ticket = await prisma.supportTicket.findUnique({
      where: { id: params.ticketId },
      select: { id: true, userId: true },
    });
    
    if (!ticket) {
      return NextResponse.json(
        { message: "Support-Ticket nicht gefunden" },
        { status: 404 }
      );
    }
    
    // Check if user is authorized to respond to this ticket
    if (!isAdmin && ticket.userId !== userId) {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 403 }
      );
    }
    
    // Create response
    const response = await prisma.supportTicketResponse.create({
      data: {
        message,
        isFromAdmin: isAdmin,
        ticketId: params.ticketId,
        responderId: userId,
      },
    });
    
    // If admin responded, update ticket status to IN_PROGRESS if it's OPEN
    if (isAdmin) {
      await prisma.supportTicket.updateMany({
        where: {
          id: params.ticketId,
          status: "OPEN",
        },
        data: {
          status: "IN_PROGRESS",
        },
      });
      
      // Create notification for ticket owner
      await prisma.notification.create({
        data: {
          userId: ticket.userId,
          type: "SUPPORT_TICKET_RESPONSE",
          title: "Neue Antwort auf Ihr Support-Ticket",
          message: "Ein Support-Mitarbeiter hat auf Ihr Ticket geantwortet.",
          relatedEntityType: "SupportTicket",
          relatedEntityId: ticket.id,
        },
      });
    } else {
      // Create notification for admins
      await prisma.notification.createMany({
        data: (await prisma.user.findMany({
          where: { role: "ADMIN" },
          select: { id: true },
        })).map(admin => ({
          userId: admin.id,
          type: "SUPPORT_TICKET_RESPONSE",
          title: "Neue Antwort auf Support-Ticket",
          message: `Ein Benutzer hat auf ein Support-Ticket geantwortet.`,
          relatedEntityType: "SupportTicket",
          relatedEntityId: ticket.id,
        })),
      });
    }
    
    return NextResponse.json(response, { status: 201 });
    
  } catch (error) {
    console.error("Fehler beim Hinzufügen einer Antwort:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}
