import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Define the schema for ticket creation
const createTicketSchema = z.object({
  subject: z.string().min(5, "Betreff muss mindestens 5 Zeichen lang sein"),
  message: z.string().min(20, "Nachricht muss mindestens 20 Zeichen lang sein"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]),
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein").optional(),
});

/**
 * POST /api/support/tickets
 * Creates a new support ticket
 */
export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = createTicketSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Eingabedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { subject, message, priority, email } = validationResult.data;
    
    // Check if user is authenticated
    if (!session && !email) {
      return NextResponse.json(
        { message: "E-Mail-Adresse ist erforderlich für nicht angemeldete Benutzer" },
        { status: 400 }
      );
    }
    
    // Create ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        subject,
        message,
        priority: priority as any,
        userId: session?.user?.id || "anonymous", // Use anonymous for non-authenticated users
        // For non-authenticated users, we'll store the email in the message
        ...(email && !session && { message: `Email: ${email}\n\n${message}` }),
      },
    });
    
    // Create notification for admins
    await prisma.notification.createMany({
      data: (await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      })).map(admin => ({
        userId: admin.id,
        type: "NEW_SUPPORT_TICKET",
        title: "Neues Support-Ticket",
        message: `Ein neues Support-Ticket wurde erstellt: ${subject}`,
        relatedEntityType: "SupportTicket",
        relatedEntityId: ticket.id,
      })),
    });
    
    return NextResponse.json(
      { message: "Support-Ticket erfolgreich erstellt", ticketId: ticket.id },
      { status: 201 }
    );
    
  } catch (error) {
    console.error("Fehler beim Erstellen des Support-Tickets:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/support/tickets
 * Retrieves support tickets (admin only or user's own tickets)
 */
export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { message: "Nicht autorisiert" },
        { status: 401 }
      );
    }
    
    const isAdmin = session.user.role === "ADMIN";
    const userId = session.user.id;
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const priority = searchParams.get("priority");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;
    
    // Build filter
    const filter: any = {};
    
    // If not admin, only show user's own tickets
    if (!isAdmin) {
      filter.userId = userId;
    }
    
    // Add status filter if provided
    if (status) {
      filter.status = status;
    }
    
    // Add priority filter if provided
    if (priority) {
      filter.priority = priority;
    }
    
    // Get tickets
    const [tickets, totalCount] = await Promise.all([
      prisma.supportTicket.findMany({
        where: filter,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              role: true,
              clientProfile: {
                select: {
                  companyName: true,
                  contactPersonName: true,
                },
              },
              driverProfile: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
          responses: {
            orderBy: { createdAt: "asc" },
            include: {
              responder: {
                select: {
                  id: true,
                  email: true,
                  role: true,
                },
              },
            },
          },
        },
      }),
      prisma.supportTicket.count({ where: filter }),
    ]);
    
    return NextResponse.json({
      tickets,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der Support-Tickets:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}
