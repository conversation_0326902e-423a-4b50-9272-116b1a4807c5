import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Define the schema for driver feedback
const driverFeedbackSchema = z.object({
  orderId: z.string().min(1, "Auftrags-ID ist erforderlich"),
  feedback: z.string().min(1, "Feedback darf nicht leer sein"),
  type: z.enum(["rejection", "assignment"]),
});

/**
 * POST /api/driver/feedback
 * Creates a new driver feedback entry
 */
export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is a driver
    if (!session || session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "<PERSON>cht autorisiert" },
        { status: 403 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = driverFeedbackSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Eingabedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { orderId, feedback, type } = validationResult.data;
    
    // Get driver profile ID
    const driverProfile = await prisma.userProfileDriver.findUnique({
      where: { userId: session.user.id },
      select: { id: true },
    });
    
    if (!driverProfile) {
      return NextResponse.json(
        { message: "Fahrerprofil nicht gefunden" },
        { status: 404 }
      );
    }
    
    // Check if order exists
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      select: { id: true, clientId: true, title: true },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden" },
        { status: 404 }
      );
    }
    
    // Create driver feedback entry in the database
    // Note: We're using a transaction to create both the feedback entry and notification
    await prisma.$transaction(async (tx) => {
      // Create feedback entry
      // For simplicity, we're storing feedback as a message with a special type
      await tx.message.create({
        data: {
          content: feedback,
          orderId,
          senderId: session.user.id,
          receiverId: order.clientId, // Send to the client who created the order
          isRead: false, // Admin will need to read it
        },
      });
      
      // Create notification for admins
      await tx.notification.createMany({
        data: (await tx.user.findMany({
          where: { role: "ADMIN" },
          select: { id: true },
        })).map(admin => ({
          userId: admin.id,
          type: type === "rejection" ? "DRIVER_REJECTION_FEEDBACK" : "DRIVER_ASSIGNMENT_FEEDBACK",
          title: type === "rejection" ? "Feedback zur Auftragsablehnung" : "Feedback zur Auftragszuweisung",
          message: `Ein Fahrer hat Feedback zu ${type === "rejection" ? "einer Ablehnung" : "einer Zuweisung"} für den Auftrag "${order.title}" gegeben.`,
          relatedEntityType: "Order",
          relatedEntityId: orderId,
        })),
      });
    });
    
    return NextResponse.json(
      { message: "Feedback erfolgreich gesendet" },
      { status: 201 }
    );
    
  } catch (error) {
    console.error("Fehler beim Erstellen des Fahrer-Feedbacks:", error);
    return NextResponse.json(
      { message: "Ein Fehler ist aufgetreten" },
      { status: 500 }
    );
  }
}
