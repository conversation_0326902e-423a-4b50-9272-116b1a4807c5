import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, AssignmentStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für das Abrufen aller ausstehenden Zuweisungen für den angemeldeten Fahrer.
 */
export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "<PERSON>ugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Fahrerprofil abrufen
    const driverProfile = await prisma.userProfileDriver.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!driverProfile) {
      return NextResponse.json(
        { message: "Fahrerprofil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Ausstehende Zuweisungen abrufen
    const pendingAssignments = await prisma.assignment.findMany({
      where: {
        driverId: session.user.id,
        status: AssignmentStatus.PENDING_DRIVER_ACCEPTANCE,
      },
      include: {
        order: {
          include: {
            clientProfile: {
              select: {
                companyName: true,
              },
            },
          },
        },
      },
      orderBy: {
        assignedAt: "desc",
      },
    });
    
    // Daten für die Antwort formatieren
    const formattedAssignments = pendingAssignments.map(assignment => ({
      id: assignment.id,
      orderId: assignment.orderId,
      status: assignment.status,
      assignedAt: assignment.assignedAt,
      order: {
        id: assignment.order.id,
        orderReference: assignment.order.orderReference,
        title: assignment.order.title,
        vehicleType: assignment.order.vehicleType,
        vehicleMake: assignment.order.vehicleMake,
        vehicleModel: assignment.order.vehicleModel,
        pickupAddress: assignment.order.pickupAddress,
        pickupCity: assignment.order.pickupCity,
        pickupDate: assignment.order.pickupDate,
        deliveryAddress: assignment.order.deliveryAddress,
        deliveryCity: assignment.order.deliveryCity,
        deliveryDate: assignment.order.deliveryDate,
        estimatedPrice: assignment.order.estimatedPrice,
        finalPrice: assignment.order.finalPrice,
        currency: assignment.order.currency,
        specialInstructions: assignment.order.specialInstructions,
        client: {
          companyName: assignment.order.clientProfile.companyName,
        },
      },
    }));
    
    return NextResponse.json(formattedAssignments, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Abrufen der ausstehenden Zuweisungen:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der ausstehenden Zuweisungen ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
