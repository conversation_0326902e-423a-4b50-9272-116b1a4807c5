import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, AssignmentStatus, OrderStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

const prisma = new PrismaClient();

/**
 * API-Route für die Annahme einer Auftragszuweisung durch einen Fahrer.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { assignmentId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const assignmentId = params.assignmentId;
    
    // Zuweisung abrufen
    const assignment = await prisma.assignment.findUnique({
      where: {
        id: assignmentId,
      },
      include: {
        order: {
          select: {
            id: true,
            orderReference: true,
            clientId: true,
          },
        },
      },
    });
    
    if (!assignment) {
      return NextResponse.json(
        { message: "Zuweisung nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob die Zuweisung dem angemeldeten Fahrer gehört
    if (assignment.driverId !== session.user.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Zuweisung anzunehmen." },
        { status: 403 }
      );
    }
    
    // Überprüfen, ob die Zuweisung im Status "PENDING_DRIVER_ACCEPTANCE" ist
    if (assignment.status !== AssignmentStatus.PENDING_DRIVER_ACCEPTANCE) {
      return NextResponse.json(
        { message: `Annahme nicht möglich im Status "${assignment.status}".` },
        { status: 400 }
      );
    }
    
    // Transaktion starten, um Zuweisung und Auftragsstatus zu aktualisieren
    const result = await prisma.$transaction(async (tx) => {
      // Zuweisung aktualisieren
      const updatedAssignment = await tx.assignment.update({
        where: {
          id: assignmentId,
        },
        data: {
          status: AssignmentStatus.ACCEPTED_BY_DRIVER,
          driverAcceptedAt: new Date(),
        },
      });
      
      // Auftragsstatus aktualisieren
      const updatedOrder = await tx.order.update({
        where: {
          id: assignment.orderId,
        },
        data: {
          status: OrderStatus.IN_PROGRESS,
        },
      });
      
      // Auftragsereignis erstellen
      const orderEvent = await tx.orderEvent.create({
        data: {
          orderId: assignment.orderId,
          eventType: "DRIVER_ACCEPTED",
          description: "Fahrer hat den Auftrag angenommen",
          actorType: "DRIVER",
          actorId: session.user.id,
          eventData: {
            assignmentId: assignmentId,
            driverId: session.user.id,
          },
        },
      });
      
      // Benachrichtigung für den Kunden erstellen
      const clientNotification = await tx.notification.create({
        data: {
          userId: assignment.order.clientId,
          type: "DRIVER_ACCEPTED",
          title: "Fahrer hat Auftrag angenommen",
          message: `Der Fahrer hat Ihren Auftrag #${assignment.order.orderReference} angenommen und wird ihn ausführen.`,
          relatedEntityType: "Order",
          relatedEntityId: assignment.orderId,
        },
      });
      
      return { updatedAssignment, updatedOrder, orderEvent, clientNotification };
    });
    
    // TODO: E-Mail-Benachrichtigungen senden (in YM-504 implementieren)
    
    return NextResponse.json(result, { status: 200 });
    
  } catch (error) {
    console.error("Fehler bei der Annahme der Zuweisung:", error);
    return NextResponse.json(
      { message: "Bei der Annahme der Zuweisung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
