import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, AssignmentStatus, OrderStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { z } from "zod";

const prisma = new PrismaClient();

// Validierungsschema für die Anfrage
const declineSchema = z.object({
  reason: z.string().optional(),
});

/**
 * API-Route für die Ablehnung einer Auftragszuweisung durch einen Fahrer.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { assignmentId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // <PERSON><PERSON>pr<PERSON><PERSON>, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const assignmentId = params.assignmentId;
    
    // Anfragedaten validieren
    const body = await request.json();
    const validationResult = declineSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Anfragedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { reason } = validationResult.data;
    
    // Zuweisung abrufen
    const assignment = await prisma.assignment.findUnique({
      where: {
        id: assignmentId,
      },
      include: {
        order: {
          select: {
            id: true,
            orderReference: true,
            clientId: true,
            status: true,
          },
        },
      },
    });
    
    if (!assignment) {
      return NextResponse.json(
        { message: "Zuweisung nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob die Zuweisung dem angemeldeten Fahrer gehört
    if (assignment.driverId !== session.user.id) {
      return NextResponse.json(
        { message: "Sie haben keine Berechtigung, diese Zuweisung abzulehnen." },
        { status: 403 }
      );
    }
    
    // Überprüfen, ob die Zuweisung im Status "PENDING_DRIVER_ACCEPTANCE" ist
    if (assignment.status !== AssignmentStatus.PENDING_DRIVER_ACCEPTANCE) {
      return NextResponse.json(
        { message: `Ablehnung nicht möglich im Status "${assignment.status}".` },
        { status: 400 }
      );
    }
    
    // Transaktion starten, um Zuweisung zu löschen und Auftragsstatus zurückzusetzen
    const result = await prisma.$transaction(async (tx) => {
      // Zuweisung aktualisieren (auf DECLINED_BY_DRIVER setzen)
      const updatedAssignment = await tx.assignment.update({
        where: {
          id: assignmentId,
        },
        data: {
          status: AssignmentStatus.DECLINED_BY_DRIVER,
        },
      });
      
      // Auftragsstatus zurücksetzen
      const updatedOrder = await tx.order.update({
        where: {
          id: assignment.orderId,
        },
        data: {
          status: OrderStatus.POSTED, // Zurück zu "POSTED", damit andere Fahrer sich bewerben können
        },
      });
      
      // Auftragsereignis erstellen
      const orderEvent = await tx.orderEvent.create({
        data: {
          orderId: assignment.orderId,
          eventType: "DRIVER_DECLINED",
          description: "Fahrer hat den Auftrag abgelehnt",
          actorType: "DRIVER",
          actorId: session.user.id,
          eventData: {
            assignmentId: assignmentId,
            driverId: session.user.id,
            reason: reason || null,
          },
        },
      });
      
      // Benachrichtigung für den Kunden erstellen
      const clientNotification = await tx.notification.create({
        data: {
          userId: assignment.order.clientId,
          type: "DRIVER_DECLINED",
          title: "Fahrer hat Auftrag abgelehnt",
          message: `Der Fahrer hat Ihren Auftrag #${assignment.order.orderReference} abgelehnt. Der Auftrag wurde wieder für andere Fahrer freigegeben.`,
          relatedEntityType: "Order",
          relatedEntityId: assignment.orderId,
        },
      });
      
      // Benachrichtigung für Admins erstellen
      const adminUsers = await tx.user.findMany({
        where: {
          role: "ADMIN",
        },
        select: {
          id: true,
        },
      });
      
      const adminNotifications = await Promise.all(
        adminUsers.map(admin => 
          tx.notification.create({
            data: {
              userId: admin.id,
              type: "DRIVER_DECLINED",
              title: "Fahrer hat Auftrag abgelehnt",
              message: `Ein Fahrer hat den Auftrag #${assignment.order.orderReference} abgelehnt. ${reason ? `Grund: ${reason}` : ''}`,
              relatedEntityType: "Order",
              relatedEntityId: assignment.orderId,
            },
          })
        )
      );
      
      return { updatedAssignment, updatedOrder, orderEvent, clientNotification, adminNotifications };
    });
    
    // TODO: E-Mail-Benachrichtigungen senden (in YM-504 implementieren)
    
    return NextResponse.json(result, { status: 200 });
    
  } catch (error) {
    console.error("Fehler bei der Ablehnung der Zuweisung:", error);
    return NextResponse.json(
      { message: "Bei der Ablehnung der Zuweisung ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
