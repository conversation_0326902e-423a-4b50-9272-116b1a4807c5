import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, DocumentType, DocumentVerificationStatus } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Fahrerprofil abrufen
    const driverProfile = await prisma.userProfileDriver.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!driverProfile) {
      return NextResponse.json(
        { message: "Fahrerprofil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Dokumente des Fahrers abrufen
    const documents = await prisma.document.findMany({
      where: { driverProfileId: driverProfile.id },
      orderBy: { uploadedAt: "desc" },
    });
    
    return NextResponse.json(documents, { status: 200 });
  } catch (error) {
    console.error("Fehler beim Abrufen der Dokumente:", error);
    return NextResponse.json(
      { message: "Beim Abrufen der Dokumente ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    // Fahrerprofil abrufen
    const driverProfile = await prisma.userProfileDriver.findFirst({
      where: { userId: session.user.id },
    });
    
    if (!driverProfile) {
      return NextResponse.json(
        { message: "Fahrerprofil nicht gefunden." },
        { status: 404 }
      );
    }
    
    // Formular-Daten verarbeiten
    const formData = await request.formData();
    const file = formData.get("file") as File | null;
    const documentType = formData.get("documentType") as string | null;
    
    if (!file) {
      return NextResponse.json(
        { message: "Keine Datei hochgeladen." },
        { status: 400 }
      );
    }
    
    if (!documentType || !Object.values(DocumentType).includes(documentType as DocumentType)) {
      return NextResponse.json(
        { message: "Ungültiger Dokumenttyp." },
        { status: 400 }
      );
    }
    
    // Datei-Informationen
    const fileName = file.name;
    const mimeType = file.type;
    const fileSize = file.size;
    
    // Überprüfen der Dateigröße (max. 5 MB)
    const maxSize = 5 * 1024 * 1024; // 5 MB in Bytes
    if (fileSize > maxSize) {
      return NextResponse.json(
        { message: "Die Datei ist zu groß. Maximale Größe: 5 MB." },
        { status: 400 }
      );
    }
    
    // Überprüfen des Dateityps
    const allowedTypes = ["application/pdf", "image/jpeg", "image/jpg", "image/png"];
    if (!allowedTypes.includes(mimeType)) {
      return NextResponse.json(
        { message: "Ungültiger Dateityp. Erlaubt sind: PDF, JPG, JPEG, PNG." },
        { status: 400 }
      );
    }
    
    // In einer echten Anwendung würde hier der Upload zu S3 oder einem anderen Speicherdienst erfolgen
    // Hier simulieren wir das Speichern und generieren eine URL
    const simulatedUrl = `https://storage.example.com/documents/${Date.now()}-${fileName.replace(/\s+/g, '-')}`;
    
    // Dokument in der Datenbank speichern
    const document = await prisma.document.create({
      data: {
        documentType: documentType as DocumentType,
        fileName: fileName,
        fileUrl: simulatedUrl,
        mimeType: mimeType,
        fileSize: fileSize,
        verificationStatus: DocumentVerificationStatus.PENDING,
        driverProfileId: driverProfile.id,
      },
    });
    
    return NextResponse.json(
      { message: "Dokument erfolgreich hochgeladen.", document },
      { status: 201 }
    );
  } catch (error) {
    console.error("Fehler beim Hochladen des Dokuments:", error);
    return NextResponse.json(
      { message: "Beim Hochladen des Dokuments ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
