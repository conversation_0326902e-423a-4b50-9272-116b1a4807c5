import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { z } from "zod";

const prisma = new PrismaClient();

// Validierungsschema für die Anfrage
const uploadPhotoSchema = z.object({
  photoType: z.enum(["PICKUP", "DELIVERY"]),
  photoUrl: z.string().url("Gültige Foto-URL erforderlich"),
  notes: z.string().optional(),
});

/**
 * API-Route für das Hochladen von Fotos bei der Abholung oder Lieferung.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Überprüfen, ob der Benutzer angemeldet ist
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { message: "Nicht autorisiert. Bitte melden Sie sich an." },
        { status: 401 }
      );
    }
    
    // Überprüfen, ob der Benutzer ein Fahrer ist
    if (session.user.role !== "DRIVER") {
      return NextResponse.json(
        { message: "Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion." },
        { status: 403 }
      );
    }
    
    const orderId = params.orderId;
    
    // Anfragedaten validieren
    const body = await request.json();
    const validationResult = uploadPhotoSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Ungültige Anfragedaten", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const { photoType, photoUrl, notes } = validationResult.data;
    
    // Überprüfen, ob der Auftrag existiert und der Fahrer zugewiesen ist
    const assignment = await prisma.assignment.findFirst({
      where: {
        orderId: orderId,
        driverId: session.user.id,
      },
      include: {
        order: {
          select: {
            id: true,
            orderReference: true,
            clientId: true,
          },
        },
      },
    });
    
    if (!assignment) {
      return NextResponse.json(
        { message: "Auftrag nicht gefunden oder Sie sind nicht der zugewiesene Fahrer." },
        { status: 404 }
      );
    }
    
    // Überprüfen, ob der Auftrag in einem Zustand ist, der das Hochladen von Fotos erlaubt
    const allowedStatuses = ["ACCEPTED_BY_DRIVER", "IN_PROGRESS", "PICKED_UP", "IN_TRANSIT", "DELIVERED"];
    if (!allowedStatuses.includes(assignment.status)) {
      return NextResponse.json(
        { message: `Fotos können in diesem Auftragsstatus nicht hochgeladen werden: "${assignment.status}".` },
        { status: 400 }
      );
    }
    
    // Transaktion starten, um Foto zu erstellen und Auftragsereignis zu protokollieren
    const result = await prisma.$transaction(async (tx) => {
      // Foto erstellen
      const photo = await tx.handoverPhoto.create({
        data: {
          photoUrl: photoUrl,
          photoType: photoType,
          notes: notes,
          uploadedById: session.user.id,
          ...(photoType === "PICKUP" ? { pickupOrderId: orderId } : { deliveryOrderId: orderId }),
        },
      });
      
      // Auftragsereignis erstellen
      const orderEvent = await tx.orderEvent.create({
        data: {
          orderId: orderId,
          eventType: photoType === "PICKUP" ? "PICKUP_PHOTO_UPLOADED" : "DELIVERY_PHOTO_UPLOADED",
          description: `Fahrer hat ein Foto für die ${photoType === "PICKUP" ? "Abholung" : "Lieferung"} hochgeladen`,
          actorType: "DRIVER",
          actorId: session.user.id,
          eventData: {
            photoId: photo.id,
            photoUrl: photoUrl,
            notes: notes,
          },
        },
      });
      
      // Benachrichtigung für den Kunden erstellen
      const clientNotification = await tx.notification.create({
        data: {
          userId: assignment.order.clientId,
          type: photoType === "PICKUP" ? "PICKUP_PHOTO_UPLOADED" : "DELIVERY_PHOTO_UPLOADED",
          title: `Foto für ${photoType === "PICKUP" ? "Abholung" : "Lieferung"} hochgeladen`,
          message: `Der Fahrer hat ein Foto für die ${photoType === "PICKUP" ? "Abholung" : "Lieferung"} bei Auftrag #${assignment.order.orderReference} hochgeladen.`,
          relatedEntityType: "Order",
          relatedEntityId: orderId,
        },
      });
      
      return { photo, orderEvent, clientNotification };
    });
    
    return NextResponse.json(result, { status: 200 });
    
  } catch (error) {
    console.error("Fehler beim Hochladen des Fotos:", error);
    return NextResponse.json(
      { message: "Beim Hochladen des Fotos ist ein Fehler aufgetreten." },
      { status: 500 }
    );
  }
}
