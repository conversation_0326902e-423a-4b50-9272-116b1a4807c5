import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { verifyApi<PERSON>ey } from "@/lib/api-auth";

// Define the schema for order creation
const createOrderSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters long"),
  description: z.string().optional(),
  vehicleType: z.enum(["SEDAN", "SUV", "VAN", "TRUCK", "LUXURY", "SPORTS", "OTHER"]),
  vehicleMake: z.string().optional(),
  vehicleModel: z.string().optional(),
  vehicleYear: z.number().int().positive().optional(),
  
  pickupAddress: z.string(),
  pickupCity: z.string(),
  pickupPostalCode: z.string(),
  pickupCountry: z.string(),
  pickupLat: z.number().optional(),
  pickupLng: z.number().optional(),
  pickupDate: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: "Invalid pickup date format" }
  ),
  pickupTimeWindow: z.string().optional(),
  
  deliveryAddress: z.string(),
  deliveryCity: z.string(),
  deliveryPostalCode: z.string(),
  deliveryCountry: z.string(),
  deliveryLat: z.number().optional(),
  deliveryLng: z.number().optional(),
  deliveryDate: z.string().refine(
    (date) => !isNaN(Date.parse(date)),
    { message: "Invalid delivery date format" }
  ),
  deliveryTimeWindow: z.string().optional(),
  
  specialInstructions: z.string().optional(),
  urgency: z.enum(["STANDARD", "URGENT", "EMERGENCY"]).optional(),
  
  // External reference ID for client's system
  externalReference: z.string().optional(),
});

/**
 * GET /api/v1/orders
 * Retrieves orders for the authenticated client
 */
export async function GET(request: NextRequest) {
  try {
    // Verify API key
    const clientId = await verifyApiKey(request);
    if (!clientId) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;
    
    // Build filter
    const filter: any = {
      clientId,
      deletedAt: null,
    };
    
    // Add status filter if provided
    if (status) {
      filter.status = status;
    }
    
    // Get orders
    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        where: filter,
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
        select: {
          id: true,
          title: true,
          description: true,
          vehicleType: true,
          vehicleMake: true,
          vehicleModel: true,
          vehicleYear: true,
          pickupAddress: true,
          pickupCity: true,
          pickupPostalCode: true,
          pickupCountry: true,
          pickupLat: true,
          pickupLng: true,
          pickupDate: true,
          pickupTimeWindow: true,
          deliveryAddress: true,
          deliveryCity: true,
          deliveryPostalCode: true,
          deliveryCountry: true,
          deliveryLat: true,
          deliveryLng: true,
          deliveryDate: true,
          deliveryTimeWindow: true,
          distance: true,
          estimatedDuration: true,
          price: true,
          currency: true,
          urgency: true,
          status: true,
          specialInstructions: true,
          externalReference: true,
          createdAt: true,
          updatedAt: true,
          assignment: {
            select: {
              id: true,
              status: true,
              assignedAt: true,
              acceptedAt: true,
              enRouteToPickupAt: true,
              pickedUpAt: true,
              enRouteToDeliveryAt: true,
              deliveredAt: true,
              completedAt: true,
              driverProfile: {
                select: {
                  firstName: true,
                  lastName: true,
                  phoneNumber: true,
                },
              },
            },
          },
        },
      }),
      prisma.order.count({ where: filter }),
    ]);
    
    return NextResponse.json({
      orders,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
    
  } catch (error) {
    console.error("Error retrieving orders:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/orders
 * Creates a new order
 */
export async function POST(request: NextRequest) {
  try {
    // Verify API key
    const clientId = await verifyApiKey(request);
    if (!clientId) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get client profile
    const clientProfile = await prisma.userProfileClient.findUnique({
      where: { userId: clientId },
      select: { id: true },
    });
    
    if (!clientProfile) {
      return NextResponse.json(
        { message: "Client profile not found" },
        { status: 404 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate request body
    const validationResult = createOrderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { message: "Invalid input data", errors: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    const data = validationResult.data;
    
    // Create order
    const order = await prisma.order.create({
      data: {
        title: data.title,
        description: data.description,
        vehicleType: data.vehicleType,
        vehicleMake: data.vehicleMake,
        vehicleModel: data.vehicleModel,
        vehicleYear: data.vehicleYear,
        
        pickupAddress: data.pickupAddress,
        pickupCity: data.pickupCity,
        pickupPostalCode: data.pickupPostalCode,
        pickupCountry: data.pickupCountry,
        pickupLat: data.pickupLat,
        pickupLng: data.pickupLng,
        pickupDate: new Date(data.pickupDate),
        pickupTimeWindow: data.pickupTimeWindow,
        
        deliveryAddress: data.deliveryAddress,
        deliveryCity: data.deliveryCity,
        deliveryPostalCode: data.deliveryPostalCode,
        deliveryCountry: data.deliveryCountry,
        deliveryLat: data.deliveryLat,
        deliveryLng: data.deliveryLng,
        deliveryDate: new Date(data.deliveryDate),
        deliveryTimeWindow: data.deliveryTimeWindow,
        
        specialInstructions: data.specialInstructions,
        urgency: data.urgency || "STANDARD",
        status: "POSTED", // Automatically post the order
        externalReference: data.externalReference,
        
        clientId,
        clientProfileId: clientProfile.id,
      },
    });
    
    // Create notification for admins
    await prisma.notification.createMany({
      data: (await prisma.user.findMany({
        where: { role: "ADMIN" },
        select: { id: true },
      })).map(admin => ({
        userId: admin.id,
        type: "NEW_ORDER_API",
        title: "New Order via API",
        message: `A new order "${data.title}" was created via the API.`,
        relatedEntityType: "Order",
        relatedEntityId: order.id,
      })),
    });
    
    return NextResponse.json(
      { 
        message: "Order created successfully", 
        orderId: order.id,
        status: order.status,
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error("Error creating order:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}
