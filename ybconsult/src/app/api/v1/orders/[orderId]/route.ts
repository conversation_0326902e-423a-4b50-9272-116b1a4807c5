import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyApiKey } from "@/lib/api-auth";

/**
 * GET /api/v1/orders/[orderId]
 * Retrieves a specific order
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    // Verify API key
    const clientId = await verifyApiKey(request);
    if (!clientId) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const orderId = params.orderId;
    
    // Get order
    const order = await prisma.order.findUnique({
      where: {
        id: orderId,
        clientId, // Ensure the order belongs to the client
        deletedAt: null,
      },
      select: {
        id: true,
        title: true,
        description: true,
        vehicleType: true,
        vehicleMake: true,
        vehicleModel: true,
        vehicleYear: true,
        pickupAddress: true,
        pickupCity: true,
        pickupPostalCode: true,
        pickupCountry: true,
        pickupLat: true,
        pickupLng: true,
        pickupDate: true,
        pickupTimeWindow: true,
        deliveryAddress: true,
        deliveryCity: true,
        deliveryPostalCode: true,
        deliveryCountry: true,
        deliveryLat: true,
        deliveryLng: true,
        deliveryDate: true,
        deliveryTimeWindow: true,
        distance: true,
        estimatedDuration: true,
        price: true,
        currency: true,
        urgency: true,
        status: true,
        specialInstructions: true,
        externalReference: true,
        createdAt: true,
        updatedAt: true,
        assignment: {
          select: {
            id: true,
            status: true,
            assignedAt: true,
            acceptedAt: true,
            enRouteToPickupAt: true,
            pickedUpAt: true,
            enRouteToDeliveryAt: true,
            deliveredAt: true,
            completedAt: true,
            driverProfile: {
              select: {
                firstName: true,
                lastName: true,
                phoneNumber: true,
              },
            },
          },
        },
        pickupPhotos: {
          select: {
            id: true,
            fileUrl: true,
            uploadedAt: true,
          },
        },
        deliveryPhotos: {
          select: {
            id: true,
            fileUrl: true,
            uploadedAt: true,
          },
        },
      },
    });
    
    if (!order) {
      return NextResponse.json(
        { message: "Order not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(order);
    
  } catch (error) {
    console.error("Error retrieving order:", error);
    return NextResponse.json(
      { message: "An error occurred" },
      { status: 500 }
    );
  }
}
