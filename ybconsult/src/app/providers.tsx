// <PERSON><PERSON><PERSON> diese Datei als Client-<PERSON>mpo<PERSON>e, da Provider typischerweise Client-seitig sind
'use client';

import React from 'react';
// Importiert QueryClient und QueryClientProvider von @tanstack/react-query
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Importiert ReactQueryDevtools für die Entwicklungsunterstützung (optional, aber empfohlen)
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import AuthProviders from '@/components/providers/AuthProviders'; // Importiert den AuthProviders
import { Session } from 'next-auth'; // Importiert den Session-Typ

// Annahme: AnimatePresence und andere Provider könnten hier bereits vorhanden sein (gemäß YM-004)
// import { AnimatePresence } from 'framer-motion';

/**
 * @interface ProvidersProps
 * @description Props für die Providers Komponente.
 * @property {React.ReactNode} children - Die Kind-Komponenten, die von den Providern umschlossen werden.
 * @property {Session | null} [session] - Die NextAuth-Session, optional.
 */
interface ProvidersProps {
  children: React.ReactNode;
  session?: Session | null; // Session kann null sein, wenn nicht angemeldet
}

/**
 * @function Providers
 * @description Eine Client-Komponente, die verschiedene Kontext-Provider für die Anwendung bündelt.
 *              Dies beinhaltet den QueryClientProvider für React Query und den AuthProviders für NextAuth.
 * @param {ProvidersProps} props - Die Eigenschaften der Komponente.
 * @returns {JSX.Element} Die Provider-Komponente.
 */
export function Providers({ children, session }: ProvidersProps): JSX.Element {
  // Erstellt eine neue Instanz von QueryClient.
  // React.useState wird verwendet, um sicherzustellen, dass sie nur einmal pro Mount erstellt wird.
  const [queryClient] = React.useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        // Konfiguriert Standardoptionen für Queries, z.B. staleTime
        // staleTime: 5 * 60 * 1000, // 5 Minuten
      },
    },
  }));

  console.log("Providers Component (client-side): Received session:", session);

  return (
    // Umschließt die Kind-Komponenten mit dem AuthProviders für NextAuth-Session-Management
    <AuthProviders session={session}>
      {/* Umschließt die Kind-Komponenten mit dem QueryClientProvider für React Query */}
      <QueryClientProvider client={queryClient}>
        {/* Hier könnten andere Provider wie AnimatePresence von Framer Motion sein */}
        {/* <AnimatePresence> */}
        {children}
        {/* </AnimatePresence> */}
        {/* Fügt die ReactQueryDevtools hinzu, die nur im Entwicklungsmodus gerendert werden */}
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </AuthProviders>
  );
}