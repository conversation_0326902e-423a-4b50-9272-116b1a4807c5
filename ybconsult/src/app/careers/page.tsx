import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Karriere bei YoungMobility | Stellenangebote",
  description: "Entdecken Sie Karrieremöglichkeiten bei YoungMobility. Werden Sie Teil unseres Teams und helfen Sie uns, die Fahrzeuglogistik zu revolutionieren.",
  keywords: "Karriere, Jobs, Stellenangebote, YoungMobility, Fahrzeuglogistik, Arbeiten bei YoungMobility",
};

// Offene Stellen
const jobOpenings = [
  {
    id: "senior-backend-developer",
    title: "Senior Backend Developer (m/w/d)",
    department: "Engineering",
    location: "Berlin oder Remote",
    type: "Vollzeit",
    description: "Wir suchen einen erfahrenen Backend-Entwickler, der unser Engineering-Team verstärkt und an der Weiterentwicklung unserer Plattform arbeitet.",
    responsibilities: [
      "Entwicklung und Wartung unserer Backend-Systeme",
      "Implementierung neuer Features und Optimierung bestehender Funktionalitäten",
      "Zusammenarbeit mit dem Frontend-Team zur Integration neuer Features",
      "Sicherstellung der Skalierbarkeit und Performance unserer Systeme",
    ],
    requirements: [
      "Mindestens 5 Jahre Erfahrung in der Backend-Entwicklung",
      "Fundierte Kenntnisse in Node.js, TypeScript und PostgreSQL",
      "Erfahrung mit RESTful APIs und GraphQL",
      "Verständnis von DevOps-Praktiken und CI/CD-Pipelines",
    ],
  },
  {
    id: "product-manager",
    title: "Product Manager (m/w/d)",
    department: "Product",
    location: "Berlin",
    type: "Vollzeit",
    description: "Als Product Manager gestalten Sie die Zukunft unserer Plattform und sorgen dafür, dass wir die Bedürfnisse unserer Kunden optimal erfüllen.",
    responsibilities: [
      "Entwicklung und Umsetzung der Produktstrategie",
      "Priorisierung von Features und Erstellung von Roadmaps",
      "Zusammenarbeit mit Engineering, Design und Business Teams",
      "Analyse von Markttrends und Wettbewerbern",
    ],
    requirements: [
      "Mindestens 3 Jahre Erfahrung als Product Manager",
      "Erfahrung in der Entwicklung von B2B-Produkten",
      "Starke analytische Fähigkeiten und datengetriebene Entscheidungsfindung",
      "Ausgezeichnete Kommunikations- und Präsentationsfähigkeiten",
    ],
  },
  {
    id: "customer-success-manager",
    title: "Customer Success Manager (m/w/d)",
    department: "Customer Success",
    location: "München",
    type: "Vollzeit",
    description: "Als Customer Success Manager sind Sie die Schnittstelle zwischen unseren Kunden und unserem Produkt. Sie stellen sicher, dass unsere Kunden den maximalen Nutzen aus unserer Plattform ziehen.",
    responsibilities: [
      "Betreuung und Beratung unserer Kunden",
      "Onboarding neuer Kunden und Schulung der Plattform",
      "Identifizierung von Upselling- und Cross-Selling-Möglichkeiten",
      "Sammlung von Kundenfeedback zur Produktverbesserung",
    ],
    requirements: [
      "Mindestens 2 Jahre Erfahrung im Customer Success oder Account Management",
      "Erfahrung in der Betreuung von B2B-Kunden",
      "Ausgezeichnete Kommunikations- und Präsentationsfähigkeiten",
      "Technisches Verständnis und Affinität zu digitalen Produkten",
    ],
  },
  {
    id: "operations-manager",
    title: "Operations Manager (m/w/d)",
    department: "Operations",
    location: "Hamburg",
    type: "Vollzeit",
    description: "Als Operations Manager sind Sie verantwortlich für die reibungslose Abwicklung unserer Fahrzeugüberführungen und die Optimierung unserer operativen Prozesse.",
    responsibilities: [
      "Überwachung und Optimierung der operativen Prozesse",
      "Management des Fahrernetzwerks und Sicherstellung der Servicequalität",
      "Lösung von operativen Herausforderungen und Eskalationen",
      "Zusammenarbeit mit dem Produkt-Team zur Verbesserung der Plattform",
    ],
    requirements: [
      "Mindestens 3 Jahre Erfahrung im Operations Management",
      "Erfahrung in der Logistik oder Mobilitätsbranche von Vorteil",
      "Starke analytische Fähigkeiten und Prozessdenken",
      "Ausgezeichnete Kommunikations- und Führungsfähigkeiten",
    ],
  },
];

// Vorteile
const benefits = [
  {
    title: "Flexible Arbeitszeiten",
    description: "Wir bieten flexible Arbeitszeiten und die Möglichkeit, remote zu arbeiten.",
    icon: "⏰",
  },
  {
    title: "Weiterbildung",
    description: "Wir unterstützen deine berufliche Weiterentwicklung mit Schulungen und Konferenzen.",
    icon: "📚",
  },
  {
    title: "Modernes Equipment",
    description: "Du erhältst die neueste Hardware und Software für deine Arbeit.",
    icon: "💻",
  },
  {
    title: "Teamevents",
    description: "Regelmäßige Teamevents und gemeinsame Aktivitäten stärken unseren Zusammenhalt.",
    icon: "🎉",
  },
  {
    title: "Gesundheitsförderung",
    description: "Wir bieten Zuschüsse für Fitnessstudios und Gesundheitsprogramme.",
    icon: "🏋️‍♂️",
  },
  {
    title: "Startup-Atmosphäre",
    description: "Flache Hierarchien, schnelle Entscheidungswege und die Möglichkeit, etwas zu bewegen.",
    icon: "🚀",
  },
];

export default function CareersPage() {
  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[40vh] min-h-[300px] flex items-center justify-center text-center">
        <Image
          src="/images/careers-hero.jpg"
          alt="Karriere bei YoungMobility"
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="relative z-10 p-8 md:p-12 bg-black/40 backdrop-blur-sm rounded-lg max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
            Karriere bei YoungMobility
          </h1>
          <p className="text-lg md:text-xl text-gray-100 mb-6 max-w-3xl mx-auto">
            Werden Sie Teil unseres Teams und helfen Sie uns, die Fahrzeuglogistik zu revolutionieren.
          </p>
        </div>
      </section>

      {/* Über uns als Arbeitgeber */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Über uns als Arbeitgeber</h2>
          <div className="flex flex-col md:flex-row gap-8 items-center">
            <div className="md:w-1/2">
              <p className="text-lg text-gray-700 mb-4">
                Bei YoungMobility arbeiten wir gemeinsam daran, die Fahrzeuglogistik zu revolutionieren. Wir sind ein dynamisches Team aus Experten verschiedener Bereiche, die ihre Leidenschaft und ihr Know-how einbringen, um innovative Lösungen zu entwickeln.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Als Arbeitgeber legen wir großen Wert auf eine offene Kommunikation, flache Hierarchien und die persönliche Weiterentwicklung unserer Mitarbeiter. Wir bieten ein modernes Arbeitsumfeld, flexible Arbeitszeiten und die Möglichkeit, remote zu arbeiten.
              </p>
              <p className="text-lg text-gray-700">
                Wenn Sie Teil eines innovativen Unternehmens sein möchten, das die Zukunft der Fahrzeuglogistik gestaltet, dann sind Sie bei uns genau richtig. Wir freuen uns auf Ihre Bewerbung!
              </p>
            </div>
            <div className="md:w-1/2 relative h-80 rounded-xl overflow-hidden">
              <Image
                src="/images/careers-team.jpg"
                alt="Das Team von YoungMobility"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Unsere Vorteile */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Unsere Vorteile</h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-md">
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-700">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Offene Stellen */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Offene Stellen</h2>
        
        <div className="max-w-4xl mx-auto space-y-8">
          {jobOpenings.map((job) => (
            <div key={job.id} className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-2 text-gray-900">{job.title}</h3>
                <div className="flex flex-wrap gap-3 mb-4">
                  <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm">{job.department}</span>
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">{job.location}</span>
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">{job.type}</span>
                </div>
                <p className="text-gray-700 mb-4">{job.description}</p>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Aufgaben:</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {job.responsibilities.map((item, index) => (
                      <li key={index} className="text-gray-700">{item}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Anforderungen:</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {job.requirements.map((item, index) => (
                      <li key={index} className="text-gray-700">{item}</li>
                    ))}
                  </ul>
                </div>
                
                <Button asChild>
                  <Link href={`/careers/${job.id}`}>Jetzt bewerben</Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Initiativbewerbung */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Keine passende Stelle gefunden?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Wir sind immer auf der Suche nach talentierten Menschen, die unser Team verstärken. Senden Sie uns Ihre Initiativbewerbung!
          </p>
          <Button asChild size="lg">
            <Link href="/careers/initiative">Initiativbewerbung</Link>
          </Button>
        </div>
      </section>

      {/* CTA für Fahrer */}
      <section className="container mx-auto px-4 text-center py-8">
        <div className="bg-accent text-white p-8 md:p-12 rounded-xl max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6">Werden Sie YoungMover</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Sie möchten als Fahrer bei uns arbeiten? Entdecken Sie die Vorteile und Möglichkeiten als YoungMover.
          </p>
          <Button asChild size="lg" className="bg-white text-accent hover:bg-gray-100">
            <Link href="/youngmovers">Mehr erfahren</Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
