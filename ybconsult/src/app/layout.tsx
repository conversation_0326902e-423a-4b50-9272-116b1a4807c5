import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Playfair_Display } from "next/font/google"; // Importiere die Schriftarten
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Toaster } from "@/components/ui/sonner"; // Für Benachrichtigungen (aus YM-003)
import { Providers } from './providers'; // Korrigierter Pfad zu providers.tsx
import { getServerSession } from "next-auth/next"; // Import für serverseitige Session
import { authOptions } from "./api/auth/[...nextauth]/route"; // Import der authOptions
import OrganizationSchema from "@/components/seo/OrganizationSchema";
import ServiceSchema from "@/components/seo/ServiceSchema";

// Inter für den Haupttext
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter", // Definiere eine CSS-Variable für die Schriftart
  display: "swap",
});

// Playfair Display für Überschriften
const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair", // Definiere eine CSS-Variable für die Schriftart
  display: "swap",
});

// Metadaten für die Seite
export const metadata: Metadata = {
  title: {
    default: "YoungMobility - Innovative Fahrzeugüberführungen",
    template: "%s | YoungMobility",
  },
  description: "YoungMobility revolutioniert die Fahrzeuglogistik mit einer modernen, benutzerfreundlichen Plattform für Geschäftskunden und Fahrer.",
  keywords: ["Fahrzeugüberführung", "Fahrzeuglogistik", "Autotransport", "Fahrzeugtransfer", "YoungMobility"],
  authors: [{ name: "YoungMobility GmbH" }],
  creator: "YoungMobility GmbH",
  publisher: "YoungMobility GmbH",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://youngmobility.com"),
  alternates: {
    canonical: "/",
    languages: {
      "de-DE": "/",
      "en-US": "/en",
    },
  },
  openGraph: {
    type: "website",
    locale: "de_DE",
    url: "https://youngmobility.com",
    title: "YoungMobility - Innovative Fahrzeugüberführungen",
    description: "YoungMobility revolutioniert die Fahrzeuglogistik mit einer modernen, benutzerfreundlichen Plattform für Geschäftskunden und Fahrer.",
    siteName: "YoungMobility",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "YoungMobility - Innovative Fahrzeugüberführungen",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "YoungMobility - Innovative Fahrzeugüberführungen",
    description: "YoungMobility revolutioniert die Fahrzeuglogistik mit einer modernen, benutzerfreundlichen Plattform für Geschäftskunden und Fahrer.",
    images: ["/images/og-image.jpg"],
    creator: "@youngmobility",
  },
  verification: {
    google: "google-site-verification-code",
  },
};

/**
 * Root-Layout Komponente für die gesamte Anwendung.
 * Ruft die NextAuth-Session serverseitig ab und übergibt sie an die Providers-Komponente.
 */
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Ruft die Session serverseitig ab, um sie an den clientseitigen SessionProvider weiterzugeben.
  // Dies vermeidet Flackern oder Layout-Verschiebungen durch clientseitiges Laden der Session.
  const session = await getServerSession(authOptions);
  console.log("RootLayout (server-side): Fetched session:", session);

  return (
    <html lang="de" className={`${inter.variable} ${playfair.variable}`}>
      <head>
        <OrganizationSchema />
        <ServiceSchema />
      </head>
      <body className="bg-background text-foreground flex flex-col min-h-screen font-inter">
        <Providers session={session}>
          <Header />
          <main className="flex-grow w-full">
            {children}
          </main>
          <Footer />
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
