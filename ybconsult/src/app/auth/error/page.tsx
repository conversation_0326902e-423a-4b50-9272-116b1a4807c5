"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { GradientButton } from "@/components/ui/gradient-button";
import { AlertCircle } from "lucide-react";

/**
 * Auth Error Seite
 * Zeigt Authentifizierungsfehler an und bietet Optionen zur Behebung.
 */
export default function AuthErrorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [errorDescription, setErrorDescription] = useState<string>("");

  useEffect(() => {
    // Fehler aus der URL auslesen
    const errorParam = searchParams.get("error");
    setError(errorParam);

    // Benutzerfreundliche Fehlerbeschreibung basierend auf dem Fehlercode
    switch (errorParam) {
      case "Configuration":
        setErrorDescription("Es liegt ein Problem mit der Serverkonfiguration vor. Bitte kontaktieren Sie den Support.");
        break;
      case "AccessDenied":
        setErrorDescription("Zugriff verweigert. Sie haben keine Berechtigung für diese Aktion.");
        break;
      case "Verification":
        setErrorDescription("Der Verifizierungslink ist ungültig oder abgelaufen. Bitte fordern Sie einen neuen an.");
        break;
      case "OAuthSignin":
      case "OAuthCallback":
      case "OAuthCreateAccount":
      case "EmailCreateAccount":
      case "Callback":
      case "OAuthAccountNotLinked":
      case "EmailSignin":
      case "CredentialsSignin":
        setErrorDescription("Bei der Anmeldung ist ein Fehler aufgetreten. Bitte überprüfen Sie Ihre Anmeldedaten und versuchen Sie es erneut.");
        break;
      case "SessionRequired":
        setErrorDescription("Für diese Seite ist eine Anmeldung erforderlich. Bitte melden Sie sich an, um fortzufahren.");
        break;
      default:
        setErrorDescription("Ein unbekannter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut oder kontaktieren Sie den Support.");
    }
  }, [searchParams]);

  /**
   * Zurück zur Login-Seite navigieren
   */
  const handleBackToLogin = () => {
    router.push("/login");
  };

  /**
   * Zurück zur Startseite navigieren
   */
  const handleBackToHome = () => {
    router.push("/");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-center mb-4">
            <AlertCircle className="h-12 w-12 text-destructive" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">Authentifizierungsfehler</CardTitle>
          <CardDescription className="text-center">
            {error && <span className="font-mono text-sm">{error}</span>}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-6">{errorDescription}</p>
          <div className="flex flex-col gap-3">
            <GradientButton onClick={handleBackToLogin}>
              Zur Anmeldeseite
            </GradientButton>
            <GradientButton onClick={handleBackToHome} variant="variant">
              Zur Startseite
            </GradientButton>
          </div>
        </CardContent>
        <CardFooter className="text-center text-sm text-muted-foreground">
          Bei anhaltenden Problemen kontaktieren Sie bitte den Support.
        </CardFooter>
      </Card>
    </div>
  );
}
