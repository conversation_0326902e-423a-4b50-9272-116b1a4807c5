"use client";

import { useEffect } from "react";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

/**
 * SignOut-Seite
 * Zeigt eine Bestätigungsseite für den Logout an und bietet Optionen zum Abmelden oder Zurückkehren.
 */
export default function SignOutPage() {
  const router = useRouter();

  /**
   * Behandelt den Logout-Prozess
   */
  const handleSignOut = async () => {
    await signOut({ redirect: false });
    router.push("/");
  };

  /**
   * Behandelt die Rückkehr zur vorherigen Seite
   */
  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Abmelden</CardTitle>
          <CardDescription className="text-center">
            Möchten Sie sich wirklich von Ihrem Konto abmelden?
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <Button onClick={handleSignOut} variant="destructive">
            Ja, abmelden
          </Button>
          <Button onClick={handleCancel} variant="outline">
            Nein, zurück
          </Button>
        </CardContent>
        <CardFooter className="text-center text-sm text-muted-foreground">
          Sie können sich jederzeit wieder anmelden.
        </CardFooter>
      </Card>
    </div>
  );
}
