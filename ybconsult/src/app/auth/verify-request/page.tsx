"use client";

import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";

/**
 * Verify Request Seite
 * Wird angezeigt, nachdem ein Benutzer eine E-Mail-Verifizierung angefordert hat.
 */
export default function VerifyRequestPage() {
  const router = useRouter();

  /**
   * Zurück zur Startseite navigieren
   */
  const handleBackToHome = () => {
    router.push("/");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-center mb-4">
            <Mail className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">E-Mail-Bestätigung</CardTitle>
          <CardDescription className="text-center">
            Bitte überprüfen Sie Ihren Posteingang
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-6">
            Wir haben Ihnen eine E-Mail mit einem Bestätigungslink gesendet. 
            Bitte klicken Sie auf diesen Link, um Ihre E-Mail-Adresse zu verifizieren und Ihre Registrierung abzuschließen.
          </p>
          <p className="mb-6 text-sm text-muted-foreground">
            Falls Sie keine E-Mail erhalten haben, überprüfen Sie bitte auch Ihren Spam-Ordner.
          </p>
          <Button onClick={handleBackToHome} className="w-full">
            Zurück zur Startseite
          </Button>
        </CardContent>
        <CardFooter className="text-center text-sm text-muted-foreground">
          Der Bestätigungslink ist 24 Stunden gültig.
        </CardFooter>
      </Card>
    </div>
  );
}
