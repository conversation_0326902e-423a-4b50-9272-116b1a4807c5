export default function ClientOrdersPage() {
  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>Aufträge</h1>
      
      {/* Filter */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        padding: '20px', 
        backgroundColor: 'white',
        marginBottom: '20px'
      }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '15px' }}>Filter</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button style={{ 
            padding: '8px 16px',
            backgroundColor: '#3b82f6',
            color: 'white',
            borderRadius: '6px',
            border: 'none',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            Alle
          </button>
          <button style={{ 
            padding: '8px 16px',
            backgroundColor: 'white',
            color: '#64748b',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            Aktiv
          </button>
          <button style={{ 
            padding: '8px 16px',
            backgroundColor: 'white',
            color: '#64748b',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            Abgeschlossen
          </button>
          <button style={{ 
            padding: '8px 16px',
            backgroundColor: 'white',
            color: '#64748b',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            Storniert
          </button>
        </div>
      </div>
      
      {/* Aufträge */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        backgroundColor: 'white',
        marginBottom: '30px',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '15px 20px', borderBottom: '1px solid #e2e8f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>Aufträge</h2>
          <a 
            href="/client-orders-create" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Neuer Auftrag
          </a>
        </div>
        
        <div style={{ padding: '30px 0', textAlign: 'center' }}>
          <p style={{ color: '#64748b', marginBottom: '15px' }}>Keine Aufträge vorhanden</p>
          <a 
            href="/client-orders-create" 
            style={{ 
              display: 'inline-block',
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Ersten Auftrag erstellen
          </a>
        </div>
      </div>
      
      {/* Navigation */}
      <div style={{ marginTop: '30px', borderTop: '1px solid #e2e8f0', paddingTop: '20px' }}>
        <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>Navigation</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <a 
            href="/client-dashboard" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Dashboard
          </a>
          <a 
            href="/client-orders" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Aufträge
          </a>
          <a 
            href="/client-orders-create" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Neuer Auftrag
          </a>
          <a 
            href="/client-billing" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Rechnungen
          </a>
        </div>
      </div>
    </div>
  );
}
