"use client";

import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ViewportLoader } from "@/utils/dynamic-loading";
import { getOptimizedImageProps } from "@/utils/image-optimization";
import { useDebounce, useThrottle } from "@/utils/component-optimization";
import { reportWebVitals, WebVitalsMetric } from "@/utils/performance-monitoring";
import Image from "next/image";

/**
 * Performance Optimization Showcase Page
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */
export default function PerformancePage() {
  const [webVitals, setWebVitals] = useState<WebVitalsMetric[]>([]);
  const [count, setCount] = useState(0);
  const [inputValue, setInputValue] = useState("");
  const debouncedValue = useDebounce(inputValue, 500);

  // Throttled function for button clicks
  const handleThrottledClick = useThrottle(() => {
    setCount(prev => prev + 1);
  }, 300);

  // Report Web Vitals
  useEffect(() => {
    reportWebVitals((metric) => {
      setWebVitals(prev => [...prev, metric]);
    });
  }, []);

  // Optimized image props
  const heroImageProps = getOptimizedImageProps(
    "/placeholder-fullscreen-1.jpg",
    "Hero Image",
    { width: 1200, height: 600, priority: true }
  );

  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-center mb-8">Performance Optimization</h1>
      <p className="text-center mb-12 max-w-3xl mx-auto">
        Diese Seite demonstriert verschiedene Performance-Optimierungen, die im Rahmen von Phase 8 implementiert wurden.
      </p>

      {/* Web Vitals */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Core Web Vitals</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {webVitals.length > 0 ? (
            webVitals.map((metric, index) => (
              <Card key={index} className={`quechua-card border-l-4 ${
                metric.rating === 'good'
                  ? 'border-l-green-500'
                  : metric.rating === 'needs-improvement'
                    ? 'border-l-yellow-500'
                    : 'border-l-red-500'
              }`}>
                <CardHeader>
                  <CardTitle>{metric.name}</CardTitle>
                  <CardDescription>Web Vital Metrik</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-2xl font-bold">
                    {metric.name === 'CLS'
                      ? metric.value.toFixed(3)
                      : Math.round(metric.value)}
                    {metric.name !== 'CLS' ? 'ms' : ''}
                  </p>
                  <p className={`mt-2 ${
                    metric.rating === 'good'
                      ? 'text-green-600'
                      : metric.rating === 'needs-improvement'
                        ? 'text-yellow-600'
                        : 'text-red-600'
                  }`}>
                    {metric.rating === 'good'
                      ? 'Gut'
                      : metric.rating === 'needs-improvement'
                        ? 'Verbesserungswürdig'
                        : 'Schlecht'}
                  </p>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-3 text-center py-8">
              <p>Lade Web Vitals Metriken...</p>
            </div>
          )}
        </div>
      </section>

      {/* Optimized Image Loading */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Optimierte Bildladung</h2>
        <div className="relative w-full aspect-[2/1] rounded-lg overflow-hidden mb-6">
          <Image
            {...heroImageProps}
            style={{ objectFit: 'cover' }}
            alt="Hero Image"
          />
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[2, 3, 4, 5].map((num) => (
            <ViewportLoader key={num} threshold={0.1} rootMargin="200px">
              <div className="aspect-square rounded-lg overflow-hidden relative">
                <Image
                  {...getOptimizedImageProps(
                    `/placeholder-fullscreen-${num}.jpg`,
                    `Lazy Loaded Image ${num}`,
                    { width: 400, height: 400 }
                  )}
                  style={{ objectFit: 'cover' }}
                  alt={`Lazy Loaded Image ${num}`}
                />
              </div>
            </ViewportLoader>
          ))}
        </div>
      </section>

      {/* Debounce & Throttle */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Debounce & Throttle</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Debounce</CardTitle>
              <CardDescription>Verzögert die Verarbeitung von Eingaben</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Eingabe</label>
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="quechua-input w-full"
                  placeholder="Tippen Sie hier..."
                />
              </div>
              <div className="mt-4">
                <p><strong>Direkter Wert:</strong> {inputValue}</p>
                <p><strong>Debounced Wert (500ms):</strong> {debouncedValue}</p>
              </div>
            </CardContent>
          </Card>

          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Throttle</CardTitle>
              <CardDescription>Begrenzt die Häufigkeit von Ereignissen</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <p>Klicken Sie schnell auf den Button. Die Funktion wird maximal alle 300ms ausgeführt.</p>
                <p className="mt-4"><strong>Zähler:</strong> {count}</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleThrottledClick}
                className="quechua-ui-button"
              >
                Klicken Sie schnell
              </Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Lazy Loading Components */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Lazy Loading Komponenten</h2>
        <div className="grid grid-cols-1 gap-6">
          {Array.from({ length: 10 }).map((_, index) => (
            <ViewportLoader key={index} threshold={0.1} rootMargin="100px">
              <Card className="quechua-card">
                <CardHeader>
                  <CardTitle>Lazy Loaded Komponente {index + 1}</CardTitle>
                  <CardDescription>Diese Komponente wird erst geladen, wenn sie im Viewport ist</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>Diese Karte wurde erst geladen, als sie in den sichtbaren Bereich gescrollt wurde.</p>
                  <p className="mt-2">Dies verbessert die initiale Ladezeit der Seite erheblich.</p>
                </CardContent>
              </Card>
            </ViewportLoader>
          ))}
        </div>
      </section>
    </div>
  );
}
