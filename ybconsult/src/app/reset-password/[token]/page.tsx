"use client";

import { useState, useEffect } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";
import { AlertCircle, CheckCircle } from "lucide-react";

// Validierungsschema mit Zod
const resetPasswordSchema = z.object({
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: "Die Passwörter stimmen nicht überein",
  path: ["confirmPassword"]
});

// Typ aus dem Schema ableiten
type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;

/**
 * Reset Password Page
 * Ermöglicht Benutzern, ihr Passwort mit einem gültigen Token zurückzusetzen
 */
export default function ResetPasswordPage({ params }: { params: { token: string } }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [isCheckingToken, setIsCheckingToken] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordInput>({
    resolver: zodResolver(resetPasswordSchema),
  });

  // Token-Validierung beim Laden der Seite
  useEffect(() => {
    const validateToken = async () => {
      try {
        const response = await fetch(`/api/auth/validate-reset-token?token=${params.token}`);
        const data = await response.json();
        
        setIsValidToken(response.ok);
        if (!response.ok) {
          setError(data.message || "Ungültiger oder abgelaufener Token.");
        }
      } catch (e) {
        console.error("Fehler bei der Token-Validierung:", e);
        setIsValidToken(false);
        setError("Bei der Überprüfung des Tokens ist ein Fehler aufgetreten.");
      } finally {
        setIsCheckingToken(false);
      }
    };

    validateToken();
  }, [params.token]);

  /**
   * Behandelt die Formularübermittlung für das Zurücksetzen des Passworts
   */
  const onSubmit: SubmitHandler<ResetPasswordInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Passwort-Reset wird durchgeführt");

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: params.token,
          password: data.password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Passwort-Reset fehlgeschlagen:", result.message);
        setError(result.message || "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
        toast.error("Reset Fehlgeschlagen", { description: result.message || "Ein Fehler ist aufgetreten." });
      } else {
        console.log("Passwort erfolgreich zurückgesetzt");
        toast.success("Passwort Zurückgesetzt", { description: "Ihr Passwort wurde erfolgreich zurückgesetzt. Sie können sich jetzt anmelden." });
        setIsSubmitted(true);
      }
    } catch (e) {
      console.error("Ausnahme während des Passwort-Resets:", e);
      setError("Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  // Lade-Indikator während der Token-Überprüfung
  if (isCheckingToken) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">Passwort zurücksetzen</CardTitle>
            <CardDescription className="text-center">
              Token wird überprüft...
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Passwort zurücksetzen</CardTitle>
          <CardDescription className="text-center">
            {isValidToken && !isSubmitted 
              ? "Erstellen Sie ein neues Passwort für Ihr Konto." 
              : isSubmitted 
                ? "Ihr Passwort wurde erfolgreich zurückgesetzt." 
                : "Der Link zum Zurücksetzen des Passworts ist ungültig oder abgelaufen."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isValidToken && !isSubmitted ? (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password">Neues Passwort</Label>
                <Input
                  id="password"
                  type="password"
                  {...register("password")}
                  aria-invalid={errors.password ? "true" : "false"}
                />
                {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Passwort bestätigen</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  {...register("confirmPassword")}
                  aria-invalid={errors.confirmPassword ? "true" : "false"}
                />
                {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>}
              </div>

              {error && <p className="text-sm text-red-500 text-center">{error}</p>}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Wird zurückgesetzt..." : "Passwort zurücksetzen"}
              </Button>
            </form>
          ) : isSubmitted ? (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle className="h-12 w-12 text-green-500" />
              </div>
              <p>
                Ihr Passwort wurde erfolgreich zurückgesetzt. Sie können sich jetzt mit Ihrem neuen Passwort anmelden.
              </p>
              <Button onClick={() => router.push("/login")} className="w-full">
                Zur Anmeldung
              </Button>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
              <p>
                {error || "Der Link zum Zurücksetzen des Passworts ist ungültig oder abgelaufen."}
              </p>
              <p className="text-sm text-muted-foreground">
                Bitte fordern Sie einen neuen Link zum Zurücksetzen des Passworts an.
              </p>
              <Button onClick={() => router.push("/forgot-password")} className="w-full">
                Neuen Reset-Link anfordern
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/login" className="text-sm text-primary hover:underline">
            Zurück zur Anmeldung
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
