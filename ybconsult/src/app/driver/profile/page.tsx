"use client";

import { useState, useEffect } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Loader2, Upload, X, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { de } from "date-fns/locale";

// Validierungsschema mit Zod
const profileSchema = z.object({
  firstName: z.string().min(2, "Vorname muss mindestens 2 Zeichen lang sein"),
  lastName: z.string().min(2, "Nachname muss mindestens 2 Zeichen lang sein"),
  phoneNumber: z.string().min(5, "Telefonnummer ist erforderlich"),
  dateOfBirth: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
});

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "Aktuelles Passwort ist erforderlich"),
  newPassword: z.string().min(8, "Neues Passwort muss mindestens 8 Zeichen lang sein"),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Die Passwörter stimmen nicht überein",
  path: ["confirmPassword"]
});

// Typen aus den Schemas ableiten
type ProfileFormInput = z.infer<typeof profileSchema>;
type PasswordFormInput = z.infer<typeof passwordSchema>;

// Dokumententypen
const documentTypes = [
  { id: "DRIVING_LICENSE", label: "Führerschein" },
  { id: "BUSINESS_REGISTRATION", label: "Gewerbeanmeldung" },
  { id: "INSURANCE", label: "Versicherungsnachweis" },
  { id: "ID_CARD", label: "Personalausweis" },
  { id: "OTHER", label: "Sonstiges Dokument" }
];

// Typ für Dokumente
type Document = {
  id: string;
  documentType: string;
  fileName: string;
  fileUrl: string;
  uploadedAt: string;
  verificationStatus: string;
  verifiedAt?: string;
};

/**
 * Driver Profile Page
 * Ermöglicht Fahrern, ihr Profil zu verwalten, Dokumente hochzuladen und ihr Passwort zu ändern
 */
export default function DriverProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [isDocumentLoading, setIsDocumentLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [documentError, setDocumentError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileFormInput | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>("DRIVING_LICENSE");

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ProfileFormInput>({
    resolver: zodResolver(profileSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors },
    reset: resetPassword
  } = useForm<PasswordFormInput>({
    resolver: zodResolver(passwordSchema),
  });

  // Überprüfen, ob der Benutzer angemeldet ist und die richtige Rolle hat
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (session?.user?.role !== "DRIVER") {
      router.push("/");
    }
  }, [session, status, router]);

  // Profildaten und Dokumente laden
  useEffect(() => {
    const fetchProfileData = async () => {
      if (status !== "authenticated") return;

      try {
        // Profildaten laden
        const profileResponse = await fetch("/api/driver/profile");
        
        if (!profileResponse.ok) {
          throw new Error("Fehler beim Laden der Profildaten");
        }
        
        const profileData = await profileResponse.json();
        setProfileData(profileData);
        reset({
          ...profileData,
          dateOfBirth: profileData.dateOfBirth ? format(new Date(profileData.dateOfBirth), "yyyy-MM-dd") : undefined
        });

        // Dokumente laden
        const documentsResponse = await fetch("/api/driver/documents");
        
        if (!documentsResponse.ok) {
          throw new Error("Fehler beim Laden der Dokumente");
        }
        
        const documentsData = await documentsResponse.json();
        setDocuments(documentsData);
      } catch (error) {
        console.error("Fehler beim Laden des Profils:", error);
        toast.error("Fehler", { description: "Profildaten konnten nicht geladen werden." });
      }
    };

    fetchProfileData();
  }, [status, reset]);

  /**
   * Behandelt die Formularübermittlung für die Profilaktualisierung
   */
  const onSubmit: SubmitHandler<ProfileFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/driver/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.message || "Fehler beim Aktualisieren des Profils");
      }

      toast.success("Profil aktualisiert", { description: "Ihre Profildaten wurden erfolgreich aktualisiert." });
      
      // Aktualisierte Daten laden
      const updatedData = await response.json();
      setProfileData(updatedData);
      reset({
        ...updatedData,
        dateOfBirth: updatedData.dateOfBirth ? format(new Date(updatedData.dateOfBirth), "yyyy-MM-dd") : undefined
      });
    } catch (error) {
      console.error("Fehler beim Aktualisieren des Profils:", error);
      setError(error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten");
      toast.error("Fehler", { description: "Profil konnte nicht aktualisiert werden." });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Behandelt die Formularübermittlung für die Passwortänderung
   */
  const onPasswordSubmit: SubmitHandler<PasswordFormInput> = async (data) => {
    setIsPasswordLoading(true);
    setPasswordError(null);

    try {
      const response = await fetch("/api/auth/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
        }),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.message || "Fehler beim Ändern des Passworts");
      }

      toast.success("Passwort geändert", { description: "Ihr Passwort wurde erfolgreich geändert." });
      resetPassword(); // Passwortformular zurücksetzen
    } catch (error) {
      console.error("Fehler beim Ändern des Passworts:", error);
      setPasswordError(error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten");
      toast.error("Fehler", { description: "Passwort konnte nicht geändert werden." });
    } finally {
      setIsPasswordLoading(false);
    }
  };

  /**
   * Behandelt das Hochladen eines neuen Dokuments
   */
  const handleDocumentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsDocumentLoading(true);
    setDocumentError(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("documentType", selectedDocumentType);

      const response = await fetch("/api/driver/documents", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.message || "Fehler beim Hochladen des Dokuments");
      }

      toast.success("Dokument hochgeladen", { description: "Ihr Dokument wurde erfolgreich hochgeladen und wird geprüft." });
      
      // Aktualisierte Dokumentenliste laden
      const documentsResponse = await fetch("/api/driver/documents");
      const documentsData = await documentsResponse.json();
      setDocuments(documentsData);
    } catch (error) {
      console.error("Fehler beim Hochladen des Dokuments:", error);
      setDocumentError(error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten");
      toast.error("Fehler", { description: "Dokument konnte nicht hochgeladen werden." });
    } finally {
      setIsDocumentLoading(false);
      // Formular zurücksetzen
      event.target.value = "";
    }
  };

  /**
   * Gibt die Farbe für den Verifizierungsstatus zurück
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "REJECTED":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
    }
  };

  /**
   * Gibt den deutschen Text für den Verifizierungsstatus zurück
   */
  const getStatusText = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return "Verifiziert";
      case "REJECTED":
        return "Abgelehnt";
      default:
        return "Ausstehend";
    }
  };

  /**
   * Gibt das Icon für den Verifizierungsstatus zurück
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return <CheckCircle className="h-4 w-4" />;
      case "REJECTED":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Lade-Indikator während der Sitzungsprüfung
  if (status === "loading" || !profileData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Mein Profil</h1>
      
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profildaten</TabsTrigger>
          <TabsTrigger value="documents">Dokumente</TabsTrigger>
          <TabsTrigger value="password">Passwort ändern</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profildaten</CardTitle>
              <CardDescription>
                Hier können Sie Ihre persönlichen Daten und Kontaktinformationen verwalten.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Persönliche Daten */}
                  <div className="space-y-2 md:col-span-2">
                    <h3 className="text-lg font-medium">Persönliche Daten</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">Vorname *</Label>
                        <Input
                          id="firstName"
                          {...register("firstName")}
                          aria-invalid={errors.firstName ? "true" : "false"}
                        />
                        {errors.firstName && <p className="text-sm text-red-500">{errors.firstName.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Nachname *</Label>
                        <Input
                          id="lastName"
                          {...register("lastName")}
                          aria-invalid={errors.lastName ? "true" : "false"}
                        />
                        {errors.lastName && <p className="text-sm text-red-500">{errors.lastName.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phoneNumber">Telefonnummer *</Label>
                        <Input
                          id="phoneNumber"
                          type="tel"
                          {...register("phoneNumber")}
                          aria-invalid={errors.phoneNumber ? "true" : "false"}
                        />
                        {errors.phoneNumber && <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dateOfBirth">Geburtsdatum (optional)</Label>
                        <Input
                          id="dateOfBirth"
                          type="date"
                          {...register("dateOfBirth")}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Adresse */}
                  <div className="space-y-2 md:col-span-2">
                    <h3 className="text-lg font-medium">Adresse</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="addressLine1">Straße, Hausnummer *</Label>
                        <Input
                          id="addressLine1"
                          {...register("addressLine1")}
                          aria-invalid={errors.addressLine1 ? "true" : "false"}
                        />
                        {errors.addressLine1 && <p className="text-sm text-red-500">{errors.addressLine1.message}</p>}
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="addressLine2">Adresszusatz (optional)</Label>
                        <Input
                          id="addressLine2"
                          {...register("addressLine2")}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="postalCode">Postleitzahl *</Label>
                        <Input
                          id="postalCode"
                          {...register("postalCode")}
                          aria-invalid={errors.postalCode ? "true" : "false"}
                        />
                        {errors.postalCode && <p className="text-sm text-red-500">{errors.postalCode.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="city">Stadt *</Label>
                        <Input
                          id="city"
                          {...register("city")}
                          aria-invalid={errors.city ? "true" : "false"}
                        />
                        {errors.city && <p className="text-sm text-red-500">{errors.city.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="country">Land *</Label>
                        <Input
                          id="country"
                          {...register("country")}
                          aria-invalid={errors.country ? "true" : "false"}
                        />
                        {errors.country && <p className="text-sm text-red-500">{errors.country.message}</p>}
                      </div>
                    </div>
                  </div>
                </div>

                {error && <p className="text-sm text-red-500 text-center">{error}</p>}

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Wird gespeichert..." : "Änderungen speichern"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Dokumente</CardTitle>
              <CardDescription>
                Hier können Sie Ihre Dokumente hochladen und verwalten. Alle Dokumente werden vor der Freigabe geprüft.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Dokument hochladen */}
                <div className="border rounded-md p-4">
                  <h3 className="text-lg font-medium mb-4">Neues Dokument hochladen</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <Label htmlFor="documentType">Dokumenttyp</Label>
                      <select
                        id="documentType"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={selectedDocumentType}
                        onChange={(e) => setSelectedDocumentType(e.target.value)}
                      >
                        {documentTypes.map((type) => (
                          <option key={type.id} value={type.id}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="documentFile">Datei</Label>
                      <Input
                        id="documentFile"
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={handleDocumentUpload}
                        disabled={isDocumentLoading}
                      />
                    </div>
                  </div>
                  
                  {documentError && <p className="text-sm text-red-500 mb-4">{documentError}</p>}
                  
                  <p className="text-sm text-muted-foreground">
                    Unterstützte Dateiformate: PDF, JPG, JPEG, PNG. Maximale Dateigröße: 5 MB.
                  </p>
                </div>
                
                {/* Dokumentenliste */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Meine Dokumente</h3>
                  
                  {documents.length === 0 ? (
                    <p className="text-muted-foreground">Sie haben noch keine Dokumente hochgeladen.</p>
                  ) : (
                    <div className="space-y-4">
                      {documents.map((doc) => (
                        <div key={doc.id} className="border rounded-md p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                          <div className="flex items-center gap-3">
                            <FileText className="h-8 w-8 text-primary" />
                            <div>
                              <p className="font-medium">{doc.fileName}</p>
                              <p className="text-sm text-muted-foreground">
                                {documentTypes.find(t => t.id === doc.documentType)?.label || doc.documentType}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Hochgeladen am {format(new Date(doc.uploadedAt), "dd.MM.yyyy", { locale: de })}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(doc.verificationStatus)} variant="outline">
                              <span className="flex items-center gap-1">
                                {getStatusIcon(doc.verificationStatus)}
                                {getStatusText(doc.verificationStatus)}
                              </span>
                            </Badge>
                            
                            <Button size="sm" variant="outline" asChild>
                              <a href={doc.fileUrl} target="_blank" rel="noopener noreferrer">
                                Anzeigen
                              </a>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>Passwort ändern</CardTitle>
              <CardDescription>
                Hier können Sie Ihr Passwort ändern. Aus Sicherheitsgründen benötigen wir Ihr aktuelles Passwort.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmitPassword(onPasswordSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Aktuelles Passwort *</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    {...registerPassword("currentPassword")}
                    aria-invalid={passwordErrors.currentPassword ? "true" : "false"}
                  />
                  {passwordErrors.currentPassword && <p className="text-sm text-red-500">{passwordErrors.currentPassword.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Neues Passwort *</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    {...registerPassword("newPassword")}
                    aria-invalid={passwordErrors.newPassword ? "true" : "false"}
                  />
                  {passwordErrors.newPassword && <p className="text-sm text-red-500">{passwordErrors.newPassword.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Neues Passwort bestätigen *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    {...registerPassword("confirmPassword")}
                    aria-invalid={passwordErrors.confirmPassword ? "true" : "false"}
                  />
                  {passwordErrors.confirmPassword && <p className="text-sm text-red-500">{passwordErrors.confirmPassword.message}</p>}
                </div>

                {passwordError && <p className="text-sm text-red-500 text-center">{passwordError}</p>}

                <Button type="submit" disabled={isPasswordLoading}>
                  {isPasswordLoading ? "Wird geändert..." : "Passwort ändern"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
