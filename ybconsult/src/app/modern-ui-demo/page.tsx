"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/gradient-button"
import { ThemeToggle } from "@/components/ui/theme-toggle"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

export default function ModernUIDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Modern UI/UX Demo
            </h1>
            <p className="text-muted-foreground mt-2">
              Experience the new enhanced design system with gradient buttons and interactive effects
            </p>
          </div>
          <ThemeToggle />
        </div>

        {/* Gradient Buttons Showcase */}
        <Card className="modern-ui-card">
          <CardHeader>
            <CardTitle>Enhanced Gradient Buttons</CardTitle>
            <CardDescription>
              Buttons with custom CSS properties, color transitions, and border animations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label>Default Gradient</Label>
                <GradientButton variant="default" size="default">
                  Primary Action
                </GradientButton>
              </div>
              <div className="space-y-2">
                <Label>Secondary Gradient</Label>
                <GradientButton variant="secondary" size="default">
                  Secondary Action
                </GradientButton>
              </div>
              <div className="space-y-2">
                <Label>Outline Style</Label>
                <GradientButton variant="outline" size="default">
                  Outline Button
                </GradientButton>
              </div>
              <div className="space-y-2">
                <Label>Ghost Style</Label>
                <GradientButton variant="ghost" size="default">
                  Ghost Button
                </GradientButton>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Small Size</Label>
                <GradientButton variant="default" size="sm">
                  Small Button
                </GradientButton>
              </div>
              <div className="space-y-2">
                <Label>Default Size</Label>
                <GradientButton variant="default" size="default">
                  Default Button
                </GradientButton>
              </div>
              <div className="space-y-2">
                <Label>Large Size</Label>
                <GradientButton variant="default" size="lg">
                  Large Button
                </GradientButton>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interactive Components */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="modern-ui-card">
            <CardHeader>
              <CardTitle>Modern Form Elements</CardTitle>
              <CardDescription>Enhanced inputs with improved focus states</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="Enter your email"
                  className="modern-ui-input"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input 
                  id="name" 
                  type="text" 
                  placeholder="Enter your full name"
                  className="modern-ui-input"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <textarea 
                  id="message"
                  className="modern-ui-input min-h-[100px] resize-none"
                  placeholder="Enter your message..."
                />
              </div>
              <GradientButton variant="default" className="w-full">
                Submit Form
              </GradientButton>
            </CardContent>
          </Card>

          <Card className="modern-ui-card">
            <CardHeader>
              <CardTitle>Status Badges</CardTitle>
              <CardDescription>Various badge styles with modern colors</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="default">Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="destructive">Error</Badge>
                <Badge variant="outline">Outline</Badge>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Order Status Examples:</p>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Completed</Badge>
                  <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending</Badge>
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">In Progress</Badge>
                  <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Cancelled</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs Demo */}
        <Card className="modern-ui-card">
          <CardHeader>
            <CardTitle>Interactive Tabs</CardTitle>
            <CardDescription>Tabbed content with smooth transitions</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="features" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="design">Design</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
              </TabsList>
              <TabsContent value="features" className="space-y-4">
                <h3 className="text-lg font-semibold">Enhanced Features</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Gradient buttons with custom CSS properties</li>
                  <li>• Interactive border animations on hover</li>
                  <li>• Smooth color transitions</li>
                  <li>• Enhanced focus states with ring effects</li>
                  <li>• Modern card hover effects</li>
                </ul>
              </TabsContent>
              <TabsContent value="design" className="space-y-4">
                <h3 className="text-lg font-semibold">Design System</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Updated background color palette</li>
                  <li>• Consistent spacing and typography</li>
                  <li>• Accessible color contrasts</li>
                  <li>• Responsive design patterns</li>
                  <li>• Theme switching capabilities</li>
                </ul>
              </TabsContent>
              <TabsContent value="performance" className="space-y-4">
                <h3 className="text-lg font-semibold">Performance Optimizations</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• CSS-based animations for better performance</li>
                  <li>• Optimized transition timing functions</li>
                  <li>• Reduced layout shifts</li>
                  <li>• Hardware-accelerated transforms</li>
                  <li>• Minimal JavaScript for interactions</li>
                </ul>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-muted-foreground">
          <p>Modern UI/UX Demo - Enhanced with gradient buttons and interactive effects</p>
        </div>
      </div>
    </div>
  )
}
