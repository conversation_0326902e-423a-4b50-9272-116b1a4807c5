"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { Grad<PERSON>Button } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";

// Validierungsschema mit Zod
const registerSchema = z.object({
  companyName: z.string().min(2, "Firmenname muss mindestens 2 Zeichen lang sein"),
  contactPersonName: z.string().min(2, "Name der Kontaktperson muss mindestens 2 Zeichen lang sein"),
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein"),
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
  confirmPassword: z.string(),
  phoneNumber: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
  vatId: z.string().optional(),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "Sie müssen die Nutzungsbedingungen akzeptieren, um fortzufahren"
  })
}).refine(data => data.password === data.confirmPassword, {
  message: "Die Passwörter stimmen nicht überein",
  path: ["confirmPassword"]
});

// Typ aus dem Schema ableiten
type RegisterFormInput = z.infer<typeof registerSchema>;

/**
 * Business Client Registration Page
 * Ermöglicht Geschäftskunden, sich mit Unternehmensdaten zu registrieren
 */
export default function RegisterClientPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<RegisterFormInput>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      termsAccepted: false
    }
  });

  /**
   * Behandelt die Formularübermittlung für die Registrierung
   */
  const onSubmit: SubmitHandler<RegisterFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Registrierungsversuch für Geschäftskunden:", data.email);

    try {
      const response = await fetch("/api/auth/register-client", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          password: data.password,
          companyName: data.companyName,
          contactPersonName: data.contactPersonName,
          phoneNumber: data.phoneNumber,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          postalCode: data.postalCode,
          country: data.country,
          vatId: data.vatId
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Registrierung fehlgeschlagen:", result.message);
        setError(result.message || "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
        toast.error("Registrierung Fehlgeschlagen", { description: result.message || "Ein Fehler ist aufgetreten." });
      } else {
        console.log("Registrierung erfolgreich, Weiterleitung zur Bestätigungsseite...");
        toast.success("Registrierung Erfolgreich", { description: "Bitte überprüfen Sie Ihre E-Mails, um Ihre Registrierung zu bestätigen." });
        router.push("/auth/verify-request");
      }
    } catch (e) {
      console.error("Ausnahme während der Registrierung:", e);
      setError("Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 py-10">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Geschäftskunden-Registrierung</CardTitle>
          <CardDescription className="text-center">
            Erstellen Sie ein Geschäftskonto, um Fahrzeugüberführungen zu beauftragen.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Unternehmensdaten */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Unternehmensdaten</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">Firmenname *</Label>
                    <Input
                      id="companyName"
                      {...register("companyName")}
                      aria-invalid={errors.companyName ? "true" : "false"}
                    />
                    {errors.companyName && <p className="text-sm text-red-500">{errors.companyName.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vatId">USt-IdNr. (optional)</Label>
                    <Input
                      id="vatId"
                      {...register("vatId")}
                    />
                  </div>
                </div>
              </div>

              {/* Kontaktdaten */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Kontaktdaten</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactPersonName">Name der Kontaktperson *</Label>
                    <Input
                      id="contactPersonName"
                      {...register("contactPersonName")}
                      aria-invalid={errors.contactPersonName ? "true" : "false"}
                    />
                    {errors.contactPersonName && <p className="text-sm text-red-500">{errors.contactPersonName.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Telefonnummer (optional)</Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      {...register("phoneNumber")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">E-Mail *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...register("email")}
                      aria-invalid={errors.email ? "true" : "false"}
                    />
                    {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
                  </div>
                </div>
              </div>

              {/* Adresse */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Adresse</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="addressLine1">Straße, Hausnummer *</Label>
                    <Input
                      id="addressLine1"
                      {...register("addressLine1")}
                      aria-invalid={errors.addressLine1 ? "true" : "false"}
                    />
                    {errors.addressLine1 && <p className="text-sm text-red-500">{errors.addressLine1.message}</p>}
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="addressLine2">Adresszusatz (optional)</Label>
                    <Input
                      id="addressLine2"
                      {...register("addressLine2")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postleitzahl *</Label>
                    <Input
                      id="postalCode"
                      {...register("postalCode")}
                      aria-invalid={errors.postalCode ? "true" : "false"}
                    />
                    {errors.postalCode && <p className="text-sm text-red-500">{errors.postalCode.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">Stadt *</Label>
                    <Input
                      id="city"
                      {...register("city")}
                      aria-invalid={errors.city ? "true" : "false"}
                    />
                    {errors.city && <p className="text-sm text-red-500">{errors.city.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country">Land *</Label>
                    <Input
                      id="country"
                      {...register("country")}
                      aria-invalid={errors.country ? "true" : "false"}
                    />
                    {errors.country && <p className="text-sm text-red-500">{errors.country.message}</p>}
                  </div>
                </div>
              </div>

              {/* Passwort */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Passwort</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">Passwort *</Label>
                    <Input
                      id="password"
                      type="password"
                      {...register("password")}
                      aria-invalid={errors.password ? "true" : "false"}
                    />
                    {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Passwort bestätigen *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...register("confirmPassword")}
                      aria-invalid={errors.confirmPassword ? "true" : "false"}
                    />
                    {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>}
                  </div>
                </div>
              </div>

              {/* Nutzungsbedingungen */}
              <div className="space-y-2 md:col-span-2">
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="termsAccepted"
                    {...register("termsAccepted")}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <label
                      htmlFor="termsAccepted"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Ich akzeptiere die Nutzungsbedingungen *
                    </label>
                    <p className="text-sm text-muted-foreground">
                      Durch die Registrierung stimmen Sie unseren <Link href="/terms" className="text-primary transition-colors hover:text-primary/80">Nutzungsbedingungen</Link> und <Link href="/privacy" className="text-primary transition-colors hover:text-primary/80">Datenschutzrichtlinien</Link> zu.
                    </p>
                  </div>
                </div>
                {errors.termsAccepted && <p className="text-sm text-red-500">{errors.termsAccepted.message}</p>}
              </div>
            </div>

            {error && <p className="text-sm text-red-500 text-center">{error}</p>}

            <GradientButton type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Registrierung wird verarbeitet..." : "Registrieren"}
            </GradientButton>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-center text-sm">
          <p>
            Bereits registriert?{" "}
            <Link href="/login" className="font-medium text-primary hover:underline">
              Hier anmelden
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
