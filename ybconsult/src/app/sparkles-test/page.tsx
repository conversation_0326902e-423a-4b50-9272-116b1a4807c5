"use client";

import { SparklesCore } from "@/components/ui/sparkles";

export default function SparklesTestPage() {
  return (
    <div className="min-h-screen bg-black p-8">
      <div className="max-w-4xl mx-auto space-y-12">
        <h1 className="text-4xl font-bold text-white text-center mb-12">
          Sparkles Implementation Test
        </h1>

        {/* Test 1: Hero Title Style */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Hero Title Style</h2>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
            Innovative <span className="relative inline-block">
              <SparklesCore
                id="test-hero-sparkles"
                className="absolute inset-0 -inset-x-2 -inset-y-1"
                background="transparent"
                particleColor="#F8E7D1"
                particleDensity={30}
                minSize={0.5}
                maxSize={1.5}
                speed={2}
              />
              <span className="relative z-10" style={{ color: '#F8E7D1' }}>Fahrzeugüberführungen</span>
            </span> für Ihr Unternehmen
          </h1>
        </div>

        {/* Test 2: Section Title Style */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Section Title Style</h2>
          <div className="relative inline-block">
            <SparklesCore
              id="test-section-sparkles"
              className="absolute inset-0 -inset-x-4 -inset-y-2"
              background="transparent"
              particleColor="#F8E7D1"
              particleDensity={25}
              minSize={0.4}
              maxSize={1.2}
              speed={1.5}
            />
            <h2 className="relative z-10 text-3xl md:text-4xl font-bold" style={{ color: '#F8E7D1' }}>
              Warum YoungMobility?
            </h2>
          </div>
        </div>

        {/* Test 3: Paragraph Text Style */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Paragraph Text Style</h2>
          <div className="relative inline-block max-w-2xl mx-auto">
            <SparklesCore
              id="test-paragraph-sparkles"
              className="absolute inset-0 -inset-x-3 -inset-y-1"
              background="transparent"
              particleColor="#F8E7D1"
              particleDensity={20}
              minSize={0.3}
              maxSize={1}
              speed={1}
            />
            <p className="relative z-10 text-lg text-[#F8E7D1]">
              Unsere Plattform bietet maßgeschneiderte Lösungen für verschiedene Branchen
            </p>
          </div>
        </div>

        {/* Test 4: Card Text Style */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Card Text Style</h2>
          <div className="bg-gray-900 p-6 rounded-xl max-w-md mx-auto">
            <h3 className="text-xl font-bold text-white mb-2">Für Unternehmen</h3>
            <div className="relative">
              <SparklesCore
                id="test-card-sparkles"
                className="absolute inset-0 -inset-x-2 -inset-y-1"
                background="transparent"
                particleColor="#F8E7D1"
                particleDensity={15}
                minSize={0.2}
                maxSize={0.8}
                speed={0.8}
              />
              <p className="relative z-10 text-[#F8E7D1]">
                Optimieren Sie Ihre Fahrzeuglogistik mit unserer effizienten und transparenten Plattform.
              </p>
            </div>
          </div>
        </div>

        {/* Performance Test */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Performance Test (Multiple Sparkles)</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-900 p-4 rounded-lg">
                <div className="relative">
                  <SparklesCore
                    id={`test-multi-sparkles-${i}`}
                    className="absolute inset-0 -inset-x-1 -inset-y-1"
                    background="transparent"
                    particleColor="#F8E7D1"
                    particleDensity={10}
                    minSize={0.2}
                    maxSize={0.6}
                    speed={0.5 + i * 0.2}
                  />
                  <p className="relative z-10 text-[#F8E7D1] text-sm">
                    Test Card {i}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="text-center text-gray-400 text-sm">
          <p>This test page verifies that sparkles are working correctly behind cream-colored text.</p>
          <p>Navigate to <code>/sparkles-test</code> to view this test page.</p>
        </div>
      </div>
    </div>
  );
}
