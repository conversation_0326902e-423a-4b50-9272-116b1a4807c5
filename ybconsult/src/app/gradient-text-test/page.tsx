"use client"

import { GradientButton } from "@/components/ui/gradient-button"
import { useState } from "react"

export default function GradientTextTestPage() {
  const [hoveredButton, setHoveredButton] = useState<string | null>(null)

  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black text-center">🎨 Outline Button Gradient Text Showcase</h1>
      
      <div className="max-w-4xl mx-auto space-y-12">
        {/* Feature Banner */}
        <div className="p-8 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg border-2 border-purple-400">
          <h2 className="text-2xl font-bold mb-4 text-purple-800 text-center">✨ Cohesive Gradient Design Achieved!</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-purple-700">
            <div>
              <h3 className="font-bold mb-2">🎯 What's New:</h3>
              <ul className="space-y-1 text-sm">
                <li>• Text gradient matches border gradient exactly</li>
                <li>• Same CSS custom properties for both effects</li>
                <li>• Unified animation timing (0.5s transition)</li>
                <li>• Black base text to match black base border</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-2">🚀 Technical Features:</h3>
              <ul className="space-y-1 text-sm">
                <li>• CSS background-clip: text for gradient text</li>
                <li>• Radial gradient with position animation</li>
                <li>• Same color palette: pink → coral → orange → brown → black</li>
                <li>• Perfect synchronization with border animation</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Interactive Demo */}
        <div className="p-8 bg-gray-50 rounded-lg border-2 border-gray-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">🎮 Interactive Gradient Text Demo</h2>
          
          <div className="text-center space-y-6">
            <p className="text-gray-700">
              Hover over the Outline Button below to see the synchronized gradient text and border animation:
            </p>
            
            <div className="flex justify-center">
              <GradientButton 
                variant="outline"
                onMouseEnter={() => setHoveredButton('outline')}
                onMouseLeave={() => setHoveredButton(null)}
              >
                Gradient Text Magic
              </GradientButton>
            </div>
            
            {hoveredButton === 'outline' && (
              <div className="p-4 bg-white rounded border-2 border-purple-400">
                <p className="text-purple-700 font-medium">
                  🎯 Hover detected! Both the border and text should now display the same beautiful gradient animation!
                </p>
              </div>
            )}
            
            <p className="text-sm text-gray-600">
              Notice how the text gradient perfectly matches the border gradient colors and animation timing.
            </p>
          </div>
        </div>

        {/* Before vs After Comparison */}
        <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-blue-800">📊 Before vs After Text Behavior</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-bold text-red-600">❌ Before (Inconsistent)</h3>
              <div className="p-4 bg-white rounded border">
                <p className="text-sm text-gray-600 mb-3">Text behavior was:</p>
                <div className="space-y-2 text-sm">
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Base:</strong> White text (#ffffff)
                  </div>
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Hover:</strong> White text (#ffffff)
                  </div>
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Border:</strong> Animated gradient
                  </div>
                </div>
                <p className="text-xs text-red-600 mt-3 font-medium">
                  Text didn't match the beautiful border animation!
                </p>
              </div>
            </div>
            
            <div className="text-center space-y-4">
              <h3 className="text-lg font-bold text-green-600">✅ After (Cohesive Design)</h3>
              <div className="p-4 bg-white rounded border">
                <p className="text-sm text-gray-600 mb-3">Text behavior is now:</p>
                <div className="space-y-2 text-sm">
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Base:</strong> Black text (#000000)
                  </div>
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Hover:</strong> Gradient text (pink → coral → orange)
                  </div>
                  <div className="p-2 bg-gray-100 rounded">
                    <strong>Border:</strong> Matching gradient animation
                  </div>
                </div>
                <p className="text-xs text-green-600 mt-3 font-medium">
                  Perfect synchronization between text and border!
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Color Palette Showcase */}
        <div className="p-8 bg-yellow-50 rounded-lg border-2 border-yellow-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-yellow-800">🎨 Gradient Color Palette</h2>
          
          <div className="space-y-6">
            <p className="text-center text-gray-700">
              Both the border and text use the exact same gradient colors and animation:
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto rounded-full" style={{backgroundColor: '#c96287'}}></div>
                <p className="text-xs font-medium">Pink</p>
                <p className="text-xs text-gray-500">#c96287</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto rounded-full" style={{backgroundColor: '#c66c64'}}></div>
                <p className="text-xs font-medium">Coral</p>
                <p className="text-xs text-gray-500">#c66c64</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto rounded-full" style={{backgroundColor: '#cc7d23'}}></div>
                <p className="text-xs font-medium">Orange</p>
                <p className="text-xs text-gray-500">#cc7d23</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto rounded-full" style={{backgroundColor: '#37140a'}}></div>
                <p className="text-xs font-medium">Brown</p>
                <p className="text-xs text-gray-500">#37140a</p>
              </div>
              <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto rounded-full" style={{backgroundColor: '#000000'}}></div>
                <p className="text-xs font-medium">Black</p>
                <p className="text-xs text-gray-500">#000000</p>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">Animation Details:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• <strong>Position:</strong> Radial gradient moves from (11.14%, 140%) to (0%, 91.51%)</li>
                <li>• <strong>Spread:</strong> Changes from 150% × 180% to 120% × 103%</li>
                <li>• <strong>Timing:</strong> Smooth 0.5s transition for both border and text</li>
                <li>• <strong>Synchronization:</strong> Both effects use identical CSS custom properties</li>
              </ul>
            </div>
          </div>
        </div>

        {/* All Button Variants Comparison */}
        <div className="p-8 bg-green-50 rounded-lg border-2 border-green-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-green-800">🔄 Button Variants Comparison</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Default</h4>
              <GradientButton variant="default">Default Button</GradientButton>
              <p className="text-xs text-gray-500">Solid gradient background</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Variant</h4>
              <GradientButton variant="variant">Variant Button</GradientButton>
              <p className="text-xs text-gray-500">Alternative gradient style</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-red-700">Destructive</h4>
              <GradientButton variant="destructive">Delete Button</GradientButton>
              <p className="text-xs text-gray-500">Red gradient with black hover text</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-purple-700">Outline (ENHANCED)</h4>
              <GradientButton variant="outline">Outline Button</GradientButton>
              <p className="text-xs text-purple-600">Gradient border + gradient text</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Ghost</h4>
              <GradientButton variant="ghost">Ghost Button</GradientButton>
              <p className="text-xs text-gray-500">Subtle gray gradient</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-blue-700">Link</h4>
              <GradientButton variant="link">Link Button</GradientButton>
              <p className="text-xs text-gray-500">Minimal link style</p>
            </div>
          </div>
          
          <p className="text-center text-green-700 font-medium mt-6">
            ✅ The Outline Button now stands out with its unique synchronized gradient text and border animation
          </p>
        </div>

        {/* Technical Implementation */}
        <div className="p-8 bg-indigo-50 rounded-lg border-2 border-indigo-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-indigo-800">⚙️ Technical Implementation</h2>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold text-gray-800 mb-3">🎨 CSS Gradient Text:</h3>
                <div className="bg-white p-4 rounded border text-xs font-mono">
                  <pre>{`background: radial-gradient(
  var(--spread-x) var(--spread-y) 
  at var(--pos-x) var(--pos-y),
  var(--color-1) var(--stop-1),
  var(--color-2) var(--stop-2),
  var(--color-3) var(--stop-3),
  var(--color-4) var(--stop-4),
  var(--color-5) var(--stop-5)
);
background-clip: text;
-webkit-background-clip: text;
color: transparent;`}</pre>
                </div>
              </div>
              
              <div>
                <h3 className="font-bold text-gray-800 mb-3">✅ Key Benefits:</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Perfect synchronization between border and text</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Same CSS custom properties for consistency</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Unified animation timing and easing</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Cross-browser compatibility with webkit prefix</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Cohesive design language throughout</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">CSS Custom Properties Used:</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-xs font-mono">
                <div>
                  <p><strong>Position:</strong></p>
                  <p>--pos-x, --pos-y</p>
                </div>
                <div>
                  <p><strong>Spread:</strong></p>
                  <p>--spread-x, --spread-y</p>
                </div>
                <div>
                  <p><strong>Colors:</strong></p>
                  <p>--color-1 to --color-5</p>
                </div>
                <div>
                  <p><strong>Stops:</strong></p>
                  <p>--stop-1 to --stop-5</p>
                </div>
                <div>
                  <p><strong>Transition:</strong></p>
                  <p>0.5s ease</p>
                </div>
                <div>
                  <p><strong>Fallback:</strong></p>
                  <p>border-color</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Success Confirmation */}
        <div className="p-8 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border-2 border-green-400">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4 text-green-800">🎉 Cohesive Gradient Design Complete!</h2>
            <p className="text-lg text-green-700 mb-6">
              The Outline Button now features perfectly synchronized gradient text and border animations
            </p>
            <div className="flex justify-center">
              <GradientButton variant="outline">Perfect Harmony Achieved!</GradientButton>
            </div>
            <p className="text-sm text-green-600 mt-4">
              Hover over the button above to experience the beautiful synchronized gradient effect
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
