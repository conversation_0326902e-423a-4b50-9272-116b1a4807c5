"use client"

import { GradientButton } from "@/components/ui/gradient-button"

export default function SizeTestPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black text-center">📏 Button Size Consistency Test</h1>
      
      <div className="max-w-4xl mx-auto space-y-12">
        {/* Alignment Grid Test */}
        <div className="p-8 bg-gray-50 rounded-lg border-2 border-gray-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">Grid Alignment Test</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 place-items-center">
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Default</p>
              <GradientButton variant="default">Default</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Variant</p>
              <GradientButton variant="variant">Variant</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Destructive</p>
              <GradientButton variant="destructive">Destructive</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-purple-600">Outline (FIXED)</p>
              <GradientButton variant="outline">Outline</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Ghost</p>
              <GradientButton variant="ghost">Ghost</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Link</p>
              <GradientButton variant="link">Link</GradientButton>
            </div>
          </div>
          <p className="text-center text-green-700 font-medium mt-6">
            ✅ All buttons should be perfectly aligned in the grid
          </p>
        </div>

        {/* Side-by-Side Comparison */}
        <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-blue-800">Side-by-Side Size Comparison</h2>
          <div className="flex flex-wrap justify-center items-center gap-4">
            <GradientButton variant="default">Default Button</GradientButton>
            <GradientButton variant="outline">Outline Button</GradientButton>
            <GradientButton variant="destructive">Destructive Button</GradientButton>
            <GradientButton variant="ghost">Ghost Button</GradientButton>
          </div>
          <p className="text-center text-blue-700 font-medium mt-6">
            ✅ All buttons should have identical height and similar width (based on text length)
          </p>
        </div>

        {/* Same Text Length Test */}
        <div className="p-8 bg-purple-50 rounded-lg border-2 border-purple-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-purple-800">Same Text Length Test</h2>
          <div className="flex flex-wrap justify-center items-center gap-4">
            <GradientButton variant="default">Test</GradientButton>
            <GradientButton variant="outline">Test</GradientButton>
            <GradientButton variant="destructive">Test</GradientButton>
            <GradientButton variant="ghost">Test</GradientButton>
          </div>
          <p className="text-center text-purple-700 font-medium mt-6">
            ✅ With identical text, all buttons should be exactly the same size
          </p>
        </div>

        {/* Stacked Alignment Test */}
        <div className="p-8 bg-green-50 rounded-lg border-2 border-green-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-green-800">Stacked Alignment Test</h2>
          <div className="flex flex-col items-center space-y-4">
            <GradientButton variant="default">Default Button Text</GradientButton>
            <GradientButton variant="outline">Outline Button Text</GradientButton>
            <GradientButton variant="destructive">Destructive Button Text</GradientButton>
            <GradientButton variant="ghost">Ghost Button Text</GradientButton>
          </div>
          <p className="text-center text-green-700 font-medium mt-6">
            ✅ Stacked buttons should have perfect left and right edge alignment
          </p>
        </div>

        {/* Technical Details */}
        <div className="p-8 bg-yellow-50 rounded-lg border-2 border-yellow-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-yellow-800">Technical Size Fix Details</h2>
          <div className="space-y-4 text-sm text-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold text-gray-800 mb-2">🎯 Problem Identified:</h3>
                <ul className="space-y-1">
                  <li>• Outline Button had 2px border adding to total size</li>
                  <li>• Other buttons have no border (border: none)</li>
                  <li>• Border added 4px to width and height (2px each side)</li>
                  <li>• Caused misalignment in grids and layouts</li>
                </ul>
              </div>
              <div>
                <h3 className="font-bold text-gray-800 mb-2">🔧 Solution Applied:</h3>
                <ul className="space-y-1">
                  <li>• Adjusted padding to compensate for border</li>
                  <li>• Used calc() to subtract border width from padding</li>
                  <li>• Maintained box-sizing: border-box</li>
                  <li>• Preserved visual appearance and functionality</li>
                </ul>
              </div>
            </div>
            <div className="mt-6 p-4 bg-white rounded border">
              <h4 className="font-bold text-gray-800 mb-2">CSS Fix Applied:</h4>
              <code className="text-xs text-gray-600">
                padding: calc(1rem - 2px) calc(2.25rem - 2px) !important;
              </code>
              <p className="text-xs text-gray-500 mt-2">
                This subtracts the 2px border from the original px-9 py-4 padding
              </p>
            </div>
          </div>
        </div>

        {/* Success Confirmation */}
        <div className="p-8 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border-2 border-green-400">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4 text-green-800">✅ Size Fix Complete!</h2>
            <p className="text-lg text-green-700 mb-4">
              The Outline Button now has perfect size consistency with all other button variants
            </p>
            <div className="flex justify-center space-x-4">
              <GradientButton variant="default">Perfect</GradientButton>
              <GradientButton variant="outline">Alignment</GradientButton>
              <GradientButton variant="destructive">Achieved</GradientButton>
            </div>
            <p className="text-sm text-green-600 mt-4">
              All buttons now share identical dimensions and perfect grid alignment
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
