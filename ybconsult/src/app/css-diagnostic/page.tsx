"use client"

import { GradientButton } from "@/components/ui/gradient-button"
import { useEffect, useState } from "react"

export default function CssDiagnosticPage() {
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    // Get computed styles for outline button after component mounts
    const timer = setTimeout(() => {
      const outlineButton = document.querySelector('.gradient-button-outline')
      if (outlineButton) {
        const computedStyles = window.getComputedStyle(outlineButton)
        const pseudoAfter = window.getComputedStyle(outlineButton, '::after')
        const pseudoBefore = window.getComputedStyle(outlineButton, '::before')
        
        setDebugInfo({
          classes: outlineButton.className,
          border: computedStyles.border,
          borderColor: computedStyles.borderColor,
          borderWidth: computedStyles.borderWidth,
          borderStyle: computedStyles.borderStyle,
          background: computedStyles.background,
          backgroundColor: computedStyles.backgroundColor,
          backgroundImage: computedStyles.backgroundImage,
          position: computedStyles.position,
          zIndex: computedStyles.zIndex,
          afterContent: pseudoAfter.content,
          afterDisplay: pseudoAfter.display,
          afterBackground: pseudoAfter.background,
          afterZIndex: pseudoAfter.zIndex,
          beforeDisplay: pseudoBefore.display,
          beforeContent: pseudoBefore.content,
        })
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black">🔬 CSS DIAGNOSTIC PAGE</h1>
      
      <div className="space-y-8">
        {/* Test Button */}
        <div className="p-6 bg-gray-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Outline Button Test</h2>
          <GradientButton variant="outline">Diagnostic Outline Button</GradientButton>
        </div>

        {/* CSS Classes Applied */}
        <div className="p-6 bg-blue-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Applied CSS Classes</h2>
          <div className="bg-white p-4 rounded border font-mono text-sm">
            <p><strong>Classes:</strong> {debugInfo.classes || 'Loading...'}</p>
          </div>
        </div>

        {/* Computed Styles */}
        <div className="p-6 bg-green-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Computed Styles</h2>
          <div className="bg-white p-4 rounded border font-mono text-sm space-y-2">
            <p><strong>Border:</strong> {debugInfo.border || 'Loading...'}</p>
            <p><strong>Border Color:</strong> {debugInfo.borderColor || 'Loading...'}</p>
            <p><strong>Border Width:</strong> {debugInfo.borderWidth || 'Loading...'}</p>
            <p><strong>Border Style:</strong> {debugInfo.borderStyle || 'Loading...'}</p>
            <p><strong>Background:</strong> {debugInfo.background || 'Loading...'}</p>
            <p><strong>Background Color:</strong> {debugInfo.backgroundColor || 'Loading...'}</p>
            <p><strong>Position:</strong> {debugInfo.position || 'Loading...'}</p>
            <p><strong>Z-Index:</strong> {debugInfo.zIndex || 'Loading...'}</p>
          </div>
        </div>

        {/* Pseudo-element Styles */}
        <div className="p-6 bg-yellow-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Pseudo-element Styles</h2>
          <div className="bg-white p-4 rounded border font-mono text-sm space-y-2">
            <h3 className="font-bold">::after</h3>
            <p><strong>Content:</strong> {debugInfo.afterContent || 'Loading...'}</p>
            <p><strong>Display:</strong> {debugInfo.afterDisplay || 'Loading...'}</p>
            <p><strong>Background:</strong> {debugInfo.afterBackground || 'Loading...'}</p>
            <p><strong>Z-Index:</strong> {debugInfo.afterZIndex || 'Loading...'}</p>
            
            <h3 className="font-bold mt-4">::before</h3>
            <p><strong>Content:</strong> {debugInfo.beforeContent || 'Loading...'}</p>
            <p><strong>Display:</strong> {debugInfo.beforeDisplay || 'Loading...'}</p>
          </div>
        </div>

        {/* Expected vs Actual */}
        <div className="p-6 bg-red-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-red-800">Expected CSS Rules</h2>
          <div className="bg-white p-4 rounded border font-mono text-sm space-y-2">
            <p><strong>Expected Selector:</strong> .gradient-button.gradient-button-outline</p>
            <p><strong>Expected Border:</strong> 2px solid #000 !important</p>
            <p><strong>Expected Background:</strong> transparent !important</p>
            <p><strong>Expected ::after Display:</strong> block !important</p>
            <p><strong>Expected ::before Display:</strong> none !important</p>
          </div>
        </div>

        {/* Manual Test Button */}
        <div className="p-6 bg-purple-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Manual CSS Test</h2>
          <button 
            className="gradient-button gradient-button-outline"
            style={{
              border: '3px solid #ff0000 !important',
              background: 'transparent !important',
              color: '#ffffff',
              padding: '16px 36px',
              borderRadius: '11px',
              minWidth: '132px'
            }}
          >
            Manual Override Test
          </button>
          <p className="text-sm text-gray-600 mt-2">
            This button uses inline styles to force a red border
          </p>
        </div>

        {/* Page Context Info */}
        <div className="p-6 bg-indigo-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Page Context</h2>
          <div className="bg-white p-4 rounded border font-mono text-sm space-y-2">
            <p><strong>Page URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</p>
            <p><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'Loading...'}</p>
            <p><strong>CSS Loaded:</strong> {typeof document !== 'undefined' ? document.styleSheets.length : 'Loading...'} stylesheets</p>
          </div>
        </div>
      </div>
    </div>
  )
}
