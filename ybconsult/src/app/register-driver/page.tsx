"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";

import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";
import { Upload, X } from "lucide-react";

// Validierungsschema mit Zod
const registerSchema = z.object({
  firstName: z.string().min(2, "Vorname muss mindestens 2 Zeichen lang sein"),
  lastName: z.string().min(2, "Nachname muss mindestens 2 Zeichen lang sein"),
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein"),
  password: z.string().min(8, "Passwort muss mindestens 8 Zeichen lang sein"),
  confirmPassword: z.string(),
  phoneNumber: z.string().min(5, "Telefonnummer ist erforderlich"),
  dateOfBirth: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
  termsAccepted: z.boolean().refine(val => val === true, {
    message: "Sie müssen die Nutzungsbedingungen akzeptieren, um fortzufahren"
  })
}).refine(data => data.password === data.confirmPassword, {
  message: "Die Passwörter stimmen nicht überein",
  path: ["confirmPassword"]
});

// Typ aus dem Schema ableiten
type RegisterFormInput = z.infer<typeof registerSchema>;

// Dokumententypen
const documentTypes = [
  { id: "DRIVING_LICENSE", label: "Führerschein" },
  { id: "BUSINESS_REGISTRATION", label: "Gewerbeanmeldung" },
  { id: "INSURANCE", label: "Versicherungsnachweis" }
];

/**
 * Driver Registration Page
 * Ermöglicht Fahrern (YoungMovers), sich mit persönlichen Daten und Dokumenten zu registrieren
 */
export default function RegisterDriverPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [documents, setDocuments] = useState<Record<string, File | null>>({
    DRIVING_LICENSE: null,
    BUSINESS_REGISTRATION: null,
    INSURANCE: null
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<RegisterFormInput>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      termsAccepted: false
    }
  });

  /**
   * Behandelt das Hochladen von Dokumenten
   */
  const handleFileChange = (documentType: string, file: File | null) => {
    setDocuments(prev => ({
      ...prev,
      [documentType]: file
    }));
  };

  /**
   * Entfernt ein hochgeladenes Dokument
   */
  const removeDocument = (documentType: string) => {
    setDocuments(prev => ({
      ...prev,
      [documentType]: null
    }));
  };

  /**
   * Behandelt die Formularübermittlung für die Registrierung
   */
  const onSubmit: SubmitHandler<RegisterFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Registrierungsversuch für Fahrer:", data.email);

    // Überprüfen, ob alle erforderlichen Dokumente hochgeladen wurden
    if (!documents.DRIVING_LICENSE) {
      setError("Bitte laden Sie Ihren Führerschein hoch.");
      setIsLoading(false);
      return;
    }

    try {
      // Formular-Daten vorbereiten
      const formData = new FormData();
      formData.append("email", data.email);
      formData.append("password", data.password);
      formData.append("firstName", data.firstName);
      formData.append("lastName", data.lastName);
      formData.append("phoneNumber", data.phoneNumber);
      if (data.dateOfBirth) formData.append("dateOfBirth", data.dateOfBirth);
      formData.append("addressLine1", data.addressLine1);
      if (data.addressLine2) formData.append("addressLine2", data.addressLine2);
      formData.append("city", data.city);
      formData.append("postalCode", data.postalCode);
      formData.append("country", data.country);

      // Dokumente hinzufügen
      Object.entries(documents).forEach(([type, file]) => {
        if (file) {
          formData.append(`documents[${type}]`, file);
        }
      });

      const response = await fetch("/api/auth/register-driver", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Registrierung fehlgeschlagen:", result.message);
        setError(result.message || "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
        toast.error("Registrierung Fehlgeschlagen", { description: result.message || "Ein Fehler ist aufgetreten." });
      } else {
        console.log("Registrierung erfolgreich, Weiterleitung zur Bestätigungsseite...");
        toast.success("Registrierung Erfolgreich", { description: "Ihre Registrierung wird nun vom Administrator geprüft." });
        router.push("/auth/verify-request");
      }
    } catch (e) {
      console.error("Ausnahme während der Registrierung:", e);
      setError("Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 py-10">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Fahrer-Registrierung (YoungMover)</CardTitle>
          <CardDescription className="text-center">
            Erstellen Sie ein Fahrerkonto, um Fahrzeugüberführungen durchzuführen.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Persönliche Daten */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Persönliche Daten</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Vorname *</Label>
                    <Input
                      id="firstName"
                      {...register("firstName")}
                      aria-invalid={errors.firstName ? "true" : "false"}
                    />
                    {errors.firstName && <p className="text-sm text-red-500">{errors.firstName.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nachname *</Label>
                    <Input
                      id="lastName"
                      {...register("lastName")}
                      aria-invalid={errors.lastName ? "true" : "false"}
                    />
                    {errors.lastName && <p className="text-sm text-red-500">{errors.lastName.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">E-Mail *</Label>
                    <Input
                      id="email"
                      type="email"
                      {...register("email")}
                      aria-invalid={errors.email ? "true" : "false"}
                    />
                    {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Telefonnummer *</Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      {...register("phoneNumber")}
                      aria-invalid={errors.phoneNumber ? "true" : "false"}
                    />
                    {errors.phoneNumber && <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Geburtsdatum (optional)</Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      {...register("dateOfBirth")}
                    />
                  </div>
                </div>
              </div>

              {/* Adresse */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Adresse</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="addressLine1">Straße, Hausnummer *</Label>
                    <Input
                      id="addressLine1"
                      {...register("addressLine1")}
                      aria-invalid={errors.addressLine1 ? "true" : "false"}
                    />
                    {errors.addressLine1 && <p className="text-sm text-red-500">{errors.addressLine1.message}</p>}
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="addressLine2">Adresszusatz (optional)</Label>
                    <Input
                      id="addressLine2"
                      {...register("addressLine2")}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postleitzahl *</Label>
                    <Input
                      id="postalCode"
                      {...register("postalCode")}
                      aria-invalid={errors.postalCode ? "true" : "false"}
                    />
                    {errors.postalCode && <p className="text-sm text-red-500">{errors.postalCode.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">Stadt *</Label>
                    <Input
                      id="city"
                      {...register("city")}
                      aria-invalid={errors.city ? "true" : "false"}
                    />
                    {errors.city && <p className="text-sm text-red-500">{errors.city.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="country">Land *</Label>
                    <Input
                      id="country"
                      {...register("country")}
                      aria-invalid={errors.country ? "true" : "false"}
                    />
                    {errors.country && <p className="text-sm text-red-500">{errors.country.message}</p>}
                  </div>
                </div>
              </div>

              {/* Dokumente */}
              <div className="space-y-4 md:col-span-2">
                <h3 className="text-lg font-medium">Dokumente</h3>
                <p className="text-sm text-muted-foreground">
                  Bitte laden Sie die folgenden Dokumente hoch. Diese werden für die Verifizierung Ihres Kontos benötigt.
                </p>

                <div className="space-y-4">
                  {documentTypes.map((docType) => (
                    <div key={docType.id} className="border rounded-md p-4">
                      <Label className="mb-2 block">{docType.label} {docType.id === "DRIVING_LICENSE" && "*"}</Label>

                      {documents[docType.id] ? (
                        <div className="flex items-center justify-between bg-muted p-2 rounded-md">
                          <span className="text-sm truncate">{documents[docType.id]?.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeDocument(docType.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Input
                            type="file"
                            id={`document-${docType.id}`}
                            accept=".pdf,.jpg,.jpeg,.png"
                            onChange={(e) => {
                              const file = e.target.files?.[0] || null;
                              handleFileChange(docType.id, file);
                            }}
                            className="hidden"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => document.getElementById(`document-${docType.id}`)?.click()}
                            className="w-full"
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Dokument hochladen
                          </Button>
                        </div>
                      )}

                      {docType.id === "DRIVING_LICENSE" && !documents.DRIVING_LICENSE && (
                        <p className="text-sm text-red-500 mt-1">Führerschein ist erforderlich</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Passwort */}
              <div className="space-y-2 md:col-span-2">
                <h3 className="text-lg font-medium">Passwort</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">Passwort *</Label>
                    <Input
                      id="password"
                      type="password"
                      {...register("password")}
                      aria-invalid={errors.password ? "true" : "false"}
                    />
                    {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Passwort bestätigen *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...register("confirmPassword")}
                      aria-invalid={errors.confirmPassword ? "true" : "false"}
                    />
                    {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>}
                  </div>
                </div>
              </div>

              {/* Nutzungsbedingungen */}
              <div className="space-y-2 md:col-span-2">
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="termsAccepted"
                    {...register("termsAccepted")}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <label
                      htmlFor="termsAccepted"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Ich akzeptiere die Nutzungsbedingungen *
                    </label>
                    <p className="text-sm text-muted-foreground">
                      Durch die Registrierung stimmen Sie unseren <Link href="/terms" className="text-primary hover:underline">Nutzungsbedingungen</Link> und <Link href="/privacy" className="text-primary hover:underline">Datenschutzrichtlinien</Link> zu.
                    </p>
                  </div>
                </div>
                {errors.termsAccepted && <p className="text-sm text-red-500">{errors.termsAccepted.message}</p>}
              </div>
            </div>

            {error && <p className="text-sm text-red-500 text-center">{error}</p>}

            <GradientButton type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Registrierung wird verarbeitet..." : "Registrieren"}
            </GradientButton>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-center text-sm">
          <p>
            Bereits registriert?{" "}
            <Link href="/login" className="font-medium text-primary hover:underline">
              Hier anmelden
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
