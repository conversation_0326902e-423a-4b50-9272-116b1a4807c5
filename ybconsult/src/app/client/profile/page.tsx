"use client";

import { useState, useEffect } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

// Validierungsschema mit Zod
const profileSchema = z.object({
  companyName: z.string().min(2, "Firmenname muss mindestens 2 Zeichen lang sein"),
  contactPersonName: z.string().min(2, "Name der Kontaktperson muss mindestens 2 Zeichen lang sein"),
  phoneNumber: z.string().optional(),
  addressLine1: z.string().min(3, "Adresse muss mindestens 3 Zeichen lang sein"),
  addressLine2: z.string().optional(),
  city: z.string().min(2, "Stadt muss mindestens 2 Zeichen lang sein"),
  postalCode: z.string().min(4, "Postleitzahl muss mindestens 4 Zeichen lang sein"),
  country: z.string().min(2, "Land muss mindestens 2 Zeichen lang sein"),
  vatId: z.string().optional(),
});

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "Aktuelles Passwort ist erforderlich"),
  newPassword: z.string().min(8, "Neues Passwort muss mindestens 8 Zeichen lang sein"),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Die Passwörter stimmen nicht überein",
  path: ["confirmPassword"]
});

// Typen aus den Schemas ableiten
type ProfileFormInput = z.infer<typeof profileSchema>;
type PasswordFormInput = z.infer<typeof passwordSchema>;

/**
 * Client Profile Page
 * Ermöglicht Geschäftskunden, ihr Profil zu verwalten und ihr Passwort zu ändern
 */
export default function ClientProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileFormInput | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ProfileFormInput>({
    resolver: zodResolver(profileSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors },
    reset: resetPassword
  } = useForm<PasswordFormInput>({
    resolver: zodResolver(passwordSchema),
  });

  // Überprüfen, ob der Benutzer angemeldet ist und die richtige Rolle hat
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    } else if (session?.user?.role !== "BUSINESS_CLIENT") {
      router.push("/");
    }
  }, [session, status, router]);

  // Profildaten laden
  useEffect(() => {
    const fetchProfileData = async () => {
      if (status !== "authenticated") return;

      try {
        const response = await fetch("/api/client/profile");
        
        if (!response.ok) {
          throw new Error("Fehler beim Laden der Profildaten");
        }
        
        const data = await response.json();
        setProfileData(data);
        reset(data); // Formular mit den geladenen Daten füllen
      } catch (error) {
        console.error("Fehler beim Laden des Profils:", error);
        toast.error("Fehler", { description: "Profildaten konnten nicht geladen werden." });
      }
    };

    fetchProfileData();
  }, [status, reset]);

  /**
   * Behandelt die Formularübermittlung für die Profilaktualisierung
   */
  const onSubmit: SubmitHandler<ProfileFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/client/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.message || "Fehler beim Aktualisieren des Profils");
      }

      toast.success("Profil aktualisiert", { description: "Ihre Profildaten wurden erfolgreich aktualisiert." });
      
      // Aktualisierte Daten laden
      const updatedData = await response.json();
      setProfileData(updatedData);
      reset(updatedData);
    } catch (error) {
      console.error("Fehler beim Aktualisieren des Profils:", error);
      setError(error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten");
      toast.error("Fehler", { description: "Profil konnte nicht aktualisiert werden." });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Behandelt die Formularübermittlung für die Passwortänderung
   */
  const onPasswordSubmit: SubmitHandler<PasswordFormInput> = async (data) => {
    setIsPasswordLoading(true);
    setPasswordError(null);

    try {
      const response = await fetch("/api/auth/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
        }),
      });

      if (!response.ok) {
        const result = await response.json();
        throw new Error(result.message || "Fehler beim Ändern des Passworts");
      }

      toast.success("Passwort geändert", { description: "Ihr Passwort wurde erfolgreich geändert." });
      resetPassword(); // Passwortformular zurücksetzen
    } catch (error) {
      console.error("Fehler beim Ändern des Passworts:", error);
      setPasswordError(error instanceof Error ? error.message : "Ein unbekannter Fehler ist aufgetreten");
      toast.error("Fehler", { description: "Passwort konnte nicht geändert werden." });
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // Lade-Indikator während der Sitzungsprüfung
  if (status === "loading" || !profileData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Mein Profil</h1>
      
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile">Profildaten</TabsTrigger>
          <TabsTrigger value="password">Passwort ändern</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profildaten</CardTitle>
              <CardDescription>
                Hier können Sie Ihre Unternehmensdaten und Kontaktinformationen verwalten.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Unternehmensdaten */}
                  <div className="space-y-2 md:col-span-2">
                    <h3 className="text-lg font-medium">Unternehmensdaten</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="companyName">Firmenname *</Label>
                        <Input
                          id="companyName"
                          {...register("companyName")}
                          aria-invalid={errors.companyName ? "true" : "false"}
                        />
                        {errors.companyName && <p className="text-sm text-red-500">{errors.companyName.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="vatId">USt-IdNr. (optional)</Label>
                        <Input
                          id="vatId"
                          {...register("vatId")}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Kontaktdaten */}
                  <div className="space-y-2 md:col-span-2">
                    <h3 className="text-lg font-medium">Kontaktdaten</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="contactPersonName">Name der Kontaktperson *</Label>
                        <Input
                          id="contactPersonName"
                          {...register("contactPersonName")}
                          aria-invalid={errors.contactPersonName ? "true" : "false"}
                        />
                        {errors.contactPersonName && <p className="text-sm text-red-500">{errors.contactPersonName.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phoneNumber">Telefonnummer (optional)</Label>
                        <Input
                          id="phoneNumber"
                          type="tel"
                          {...register("phoneNumber")}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Adresse */}
                  <div className="space-y-2 md:col-span-2">
                    <h3 className="text-lg font-medium">Adresse</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="addressLine1">Straße, Hausnummer *</Label>
                        <Input
                          id="addressLine1"
                          {...register("addressLine1")}
                          aria-invalid={errors.addressLine1 ? "true" : "false"}
                        />
                        {errors.addressLine1 && <p className="text-sm text-red-500">{errors.addressLine1.message}</p>}
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="addressLine2">Adresszusatz (optional)</Label>
                        <Input
                          id="addressLine2"
                          {...register("addressLine2")}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="postalCode">Postleitzahl *</Label>
                        <Input
                          id="postalCode"
                          {...register("postalCode")}
                          aria-invalid={errors.postalCode ? "true" : "false"}
                        />
                        {errors.postalCode && <p className="text-sm text-red-500">{errors.postalCode.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="city">Stadt *</Label>
                        <Input
                          id="city"
                          {...register("city")}
                          aria-invalid={errors.city ? "true" : "false"}
                        />
                        {errors.city && <p className="text-sm text-red-500">{errors.city.message}</p>}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="country">Land *</Label>
                        <Input
                          id="country"
                          {...register("country")}
                          aria-invalid={errors.country ? "true" : "false"}
                        />
                        {errors.country && <p className="text-sm text-red-500">{errors.country.message}</p>}
                      </div>
                    </div>
                  </div>
                </div>

                {error && <p className="text-sm text-red-500 text-center">{error}</p>}

                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Wird gespeichert..." : "Änderungen speichern"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="password">
          <Card>
            <CardHeader>
              <CardTitle>Passwort ändern</CardTitle>
              <CardDescription>
                Hier können Sie Ihr Passwort ändern. Aus Sicherheitsgründen benötigen wir Ihr aktuelles Passwort.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmitPassword(onPasswordSubmit)} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Aktuelles Passwort *</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    {...registerPassword("currentPassword")}
                    aria-invalid={passwordErrors.currentPassword ? "true" : "false"}
                  />
                  {passwordErrors.currentPassword && <p className="text-sm text-red-500">{passwordErrors.currentPassword.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Neues Passwort *</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    {...registerPassword("newPassword")}
                    aria-invalid={passwordErrors.newPassword ? "true" : "false"}
                  />
                  {passwordErrors.newPassword && <p className="text-sm text-red-500">{passwordErrors.newPassword.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Neues Passwort bestätigen *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    {...registerPassword("confirmPassword")}
                    aria-invalid={passwordErrors.confirmPassword ? "true" : "false"}
                  />
                  {passwordErrors.confirmPassword && <p className="text-sm text-red-500">{passwordErrors.confirmPassword.message}</p>}
                </div>

                {passwordError && <p className="text-sm text-red-500 text-center">{passwordError}</p>}

                <Button type="submit" disabled={isPasswordLoading}>
                  {isPasswordLoading ? "Wird geändert..." : "Passwort ändern"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
