export default function ClientBillingPage() {
  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>Rechnungen</h1>
      
      {/* Übersicht */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        padding: '20px', 
        backgroundColor: 'white',
        marginBottom: '30px'
      }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '15px' }}>Rechnungsübersicht</h2>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '20px' }}>
          <div style={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: '6px', 
            padding: '15px'
          }}>
            <div style={{ color: '#64748b', marginBottom: '5px' }}>Offene Rechnungen</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>0</div>
          </div>
          
          <div style={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: '6px', 
            padding: '15px'
          }}>
            <div style={{ color: '#64748b', marginBottom: '5px' }}>Bezahlte Rechnungen</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>0</div>
          </div>
          
          <div style={{ 
            border: '1px solid #e2e8f0', 
            borderRadius: '6px', 
            padding: '15px'
          }}>
            <div style={{ color: '#64748b', marginBottom: '5px' }}>Gesamtbetrag (Jahr)</div>
            <div style={{ fontSize: '24px', fontWeight: 'bold' }}>0,00 €</div>
          </div>
        </div>
      </div>
      
      {/* Rechnungsliste */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        backgroundColor: 'white',
        marginBottom: '30px',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '15px 20px', borderBottom: '1px solid #e2e8f0' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>Rechnungen</h2>
        </div>
        
        <div style={{ padding: '30px 0', textAlign: 'center' }}>
          <p style={{ color: '#64748b', marginBottom: '15px' }}>Keine Rechnungen vorhanden</p>
          <p style={{ color: '#64748b', fontSize: '14px' }}>
            Rechnungen werden nach Abschluss von Aufträgen automatisch erstellt.
          </p>
        </div>
      </div>
      
      {/* Zahlungsmethoden */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        backgroundColor: 'white',
        marginBottom: '30px',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '15px 20px', borderBottom: '1px solid #e2e8f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold' }}>Zahlungsmethoden</h2>
          <button style={{ 
            padding: '8px 16px',
            backgroundColor: '#3b82f6',
            color: 'white',
            borderRadius: '6px',
            border: 'none',
            fontWeight: '500',
            cursor: 'pointer'
          }}>
            Hinzufügen
          </button>
        </div>
        
        <div style={{ padding: '30px 0', textAlign: 'center' }}>
          <p style={{ color: '#64748b', marginBottom: '15px' }}>Keine Zahlungsmethoden hinterlegt</p>
          <p style={{ color: '#64748b', fontSize: '14px' }}>
            Fügen Sie eine Zahlungsmethode hinzu, um Rechnungen bequem bezahlen zu können.
          </p>
        </div>
      </div>
      
      {/* Navigation */}
      <div style={{ marginTop: '30px', borderTop: '1px solid #e2e8f0', paddingTop: '20px' }}>
        <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>Navigation</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <a 
            href="/client-dashboard" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Dashboard
          </a>
          <a 
            href="/client-orders" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Aufträge
          </a>
          <a 
            href="/client-orders-create" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Neuer Auftrag
          </a>
          <a 
            href="/client-billing" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Rechnungen
          </a>
        </div>
      </div>
    </div>
  );
}
