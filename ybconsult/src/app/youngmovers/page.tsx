import { GradientButton } from "@/components/ui/gradient-button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "YoungMovers - Werde Fahrer bei YoungMobility",
  description: "Werde Teil des YoungMovers-Netzwerks und verdiene flexibel mit Fahrzeugüberführungen. Bestimme deine Arbeitszeiten selbst und profitiere von attraktiven Vergütungen.",
  keywords: "YoungMovers, Fahrer werden, Fahrzeugüberführung, Nebenjob, Flexibles Arbeiten, Fahrerjob",
};

// Vorteile für Fahrer
const driverBenefits = [
  {
    title: "Flexible Arbeitszeiten",
    description: "Bestimme selbst, wann und wie viel du arbeiten möchtest. Nimm nur die Aufträge an, die zu deinem Zeitplan passen.",
    icon: "⏰",
  },
  {
    title: "Attraktive Vergütung",
    description: "Verdiene überdurchschnittlich mit Fahrzeugüberführungen. Wöchentliche Auszahlungen und Bonusprogramme.",
    icon: "💰",
  },
  {
    title: "Einfache Auftragsannahme",
    description: "Über unsere App siehst du alle verfügbaren Aufträge in deiner Nähe und kannst sie mit einem Klick annehmen.",
    icon: "📱",
  },
  {
    title: "Vielfältige Fahrzeuge",
    description: "Von Kleinwagen bis Luxusfahrzeuge - erlebe Abwechslung bei deinen Überführungen.",
    icon: "🚗",
  },
  {
    title: "Persönliche Betreuung",
    description: "Unser Support-Team steht dir bei Fragen und Problemen jederzeit zur Verfügung.",
    icon: "🤝",
  },
  {
    title: "Wachse mit uns",
    description: "Entwickle dich weiter und übernimm mehr Verantwortung in unserem wachsenden Netzwerk.",
    icon: "📈",
  },
];

// Fahrer-Testimonials
const driverTestimonials = [
  {
    name: "Max Müller",
    age: 28,
    location: "Berlin",
    quote: "Als Student bietet mir YoungMobility die perfekte Möglichkeit, flexibel neben dem Studium zu arbeiten. Ich kann meine Arbeitszeiten selbst bestimmen und verdiene dabei überdurchschnittlich gut.",
    imageUrl: "/images/driver-max.jpg",
    earnings: "1.200€ - 1.800€ pro Monat (Teilzeit)",
  },
  {
    name: "Lisa Schmidt",
    age: 34,
    location: "München",
    quote: "Nach meiner Elternzeit wollte ich wieder arbeiten, aber mit flexiblen Zeiten. Bei YoungMobility kann ich Aufträge annehmen, wenn mein Kind im Kindergarten ist, und habe trotzdem genug Zeit für die Familie.",
    imageUrl: "/images/driver-lisa.jpg",
    earnings: "1.500€ - 2.200€ pro Monat (Teilzeit)",
  },
  {
    name: "Thomas Weber",
    age: 42,
    location: "Hamburg",
    quote: "Ich arbeite hauptberuflich als Fahrer für YoungMobility und schätze besonders die Vielfalt der Fahrzeuge und Strecken. Kein Tag ist wie der andere, und die Vergütung ist deutlich besser als in meinem vorherigen Job.",
    imageUrl: "/images/driver-thomas.jpg",
    earnings: "2.800€ - 3.500€ pro Monat (Vollzeit)",
  },
];

// Häufig gestellte Fragen
const faqs = [
  {
    question: "Welche Voraussetzungen muss ich erfüllen, um YoungMover zu werden?",
    answer: "Du benötigst einen gültigen Führerschein (mindestens Klasse B) seit mindestens 2 Jahren, ein Mindestalter von 21 Jahren, ein Smartphone mit Internetzugang und ein sauberes polizeiliches Führungszeugnis. Außerdem solltest du zuverlässig sein und Freude am Fahren haben.",
  },
  {
    question: "Wie funktioniert die Vergütung?",
    answer: "Die Vergütung basiert auf der Entfernung, dem Fahrzeugtyp und der Dringlichkeit der Überführung. Du siehst die Vergütung für jeden Auftrag in der App, bevor du ihn annimmst. Die Auszahlung erfolgt wöchentlich auf dein Konto.",
  },
  {
    question: "Muss ich ein eigenes Fahrzeug haben?",
    answer: "Nein, du überführst die Fahrzeuge unserer Geschäftskunden. Du benötigst lediglich einen gültigen Führerschein und ein Smartphone mit unserer App.",
  },
  {
    question: "Wie viele Stunden muss ich mindestens arbeiten?",
    answer: "Es gibt keine Mindestarbeitszeit. Du entscheidest selbst, wann und wie viel du arbeiten möchtest. Du kannst YoungMover als Vollzeitjob oder als flexible Nebentätigkeit ausüben.",
  },
  {
    question: "Wie werden die Rückwege organisiert?",
    answer: "Nach einer Überführung kannst du entweder einen Rücktransport mit öffentlichen Verkehrsmitteln wählen (die Kosten werden erstattet) oder direkt einen weiteren Auftrag in der Nähe annehmen. Unsere App zeigt dir verfügbare Aufträge in deiner Umgebung an.",
  },
  {
    question: "Bin ich bei YoungMobility angestellt oder selbstständig?",
    answer: "Als YoungMover bist du selbstständig tätig. Du kannst dich entweder als Kleinunternehmer registrieren oder ein Gewerbe anmelden. Wir unterstützen dich bei allen administrativen Fragen und bieten dir Ressourcen zur korrekten steuerlichen Behandlung deiner Einkünfte.",
  },
];

export default function YoungMoversPage() {
  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[60vh] min-h-[500px] flex items-center justify-center text-center">
        <Image
          src="/images/youngmovers-hero.jpg"
          alt="Werde YoungMover"
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent"></div>
        <div className="relative z-10 p-8 md:p-12 max-w-4xl mx-auto text-left ml-8 md:ml-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Werde <span className="text-accent">YoungMover</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-100 mb-8 max-w-2xl">
            Verdiene flexibel mit Fahrzeugüberführungen. Bestimme deine Arbeitszeiten selbst und profitiere von attraktiven Vergütungen.
          </p>
          <GradientButton asChild>
            <Link href="/register-driver">Jetzt registrieren</Link>
          </GradientButton>
        </div>
      </section>

      {/* Wie es funktioniert */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-gray-900">So funktioniert's</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div className="bg-white p-8 rounded-xl shadow-md text-center relative">
            <div className="absolute -top-5 -left-5 w-12 h-12 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl">1</div>
            <div className="text-5xl mb-4 flex justify-center">📱</div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Registriere dich</h3>
            <p className="text-gray-700">Erstelle dein Profil in wenigen Minuten und lade deine Dokumente hoch. Nach einer kurzen Überprüfung kannst du loslegen.</p>
          </div>
          <div className="bg-white p-8 rounded-xl shadow-md text-center relative">
            <div className="absolute -top-5 -left-5 w-12 h-12 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl">2</div>
            <div className="text-5xl mb-4 flex justify-center">🚗</div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Nimm Aufträge an</h3>
            <p className="text-gray-700">Wähle aus verfügbaren Aufträgen in deiner Nähe. Du siehst alle Details und die Vergütung, bevor du zusagst.</p>
          </div>
          <div className="bg-white p-8 rounded-xl shadow-md text-center relative">
            <div className="absolute -top-5 -left-5 w-12 h-12 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl">3</div>
            <div className="text-5xl mb-4 flex justify-center">💰</div>
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Verdiene Geld</h3>
            <p className="text-gray-700">Führe die Überführungen durch und erhalte deine Vergütung wöchentlich auf dein Konto überwiesen.</p>
          </div>
        </div>
      </section>

      {/* Vorteile */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-gray-900">Deine Vorteile als YoungMover</h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {driverBenefits.map((benefit, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-md">
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-700">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Verdienstmöglichkeiten */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-gray-900">Deine Verdienstmöglichkeiten</h2>

          <div className="bg-white p-8 rounded-xl shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <h3 className="text-2xl font-bold text-accent mb-2">1.200€ - 1.800€</h3>
                <p className="text-gray-700">Monatlich bei 10-15 Stunden pro Woche</p>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-accent mb-2">2.000€ - 2.800€</h3>
                <p className="text-gray-700">Monatlich bei 20-30 Stunden pro Woche</p>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-accent mb-2">2.800€ - 3.500€</h3>
                <p className="text-gray-700">Monatlich bei Vollzeit (35+ Stunden)</p>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-gray-700">
                Die tatsächlichen Verdienstmöglichkeiten hängen von verschiedenen Faktoren ab, wie der Anzahl der angenommenen Aufträge, der Entfernung der Überführungen und dem Fahrzeugtyp. Die angegebenen Werte basieren auf den durchschnittlichen Verdiensten unserer aktiven Fahrer.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-gray-900">Das sagen unsere YoungMovers</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {driverTestimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="relative h-64">
                  <Image
                    src={testimonial.imageUrl}
                    alt={testimonial.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="font-semibold text-gray-900">{testimonial.name}, {testimonial.age}</h3>
                    <span className="text-sm text-gray-500">{testimonial.location}</span>
                  </div>
                  <p className="text-gray-700 mb-4 italic">"{testimonial.quote}"</p>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-700"><strong>Verdienst:</strong> {testimonial.earnings}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-gray-900">Häufig gestellte Fragen</h2>

        <div className="max-w-4xl mx-auto space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-md">
              <h3 className="text-xl font-semibold mb-3 text-gray-900">{faq.question}</h3>
              <p className="text-gray-700">{faq.answer}</p>
            </div>
          ))}
        </div>
      </section>

      {/* CTA */}
      <section className="bg-accent text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Bereit, YoungMover zu werden?</h2>
          <p className="text-xl mb-10 max-w-3xl mx-auto">
            Registriere dich jetzt und starte in wenigen Tagen mit deinen ersten Überführungen. Werde Teil unseres wachsenden Netzwerks von YoungMovers!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton asChild>
              <Link href="/register-driver">Jetzt registrieren</Link>
            </GradientButton>
            <GradientButton variant="variant" asChild>
              <Link href="/contact">Fragen? Kontaktiere uns</Link>
            </GradientButton>
          </div>
        </div>
      </section>
    </div>
  );
}
