"use client"

import { Grad<PERSON>Button } from "@/components/ui/gradient-button"

export default function DebugOutlinePage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black">🔍 DEBUG OUTLINE BUTTON</h1>

      <div className="space-y-8">
        {/* Test 1: Default But<PERSON> for Reference */}
        <div className="p-6 bg-gray-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Reference: Default Button</h2>
          <GradientButton variant="default">Default Button</GradientButton>
        </div>

        {/* Test 2: Outline Button */}
        <div className="p-6 bg-blue-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Test: Outline Button</h2>
          <GradientButton variant="outline">Outline Button</GradientButton>
          <p className="text-sm text-gray-600 mt-2">
            Should have a bright red border (4px) if CSS is working
          </p>
        </div>

        {/* Test 3: Manual Inline Style Button */}
        <div className="p-6 bg-green-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Test: Manual Inline Style</h2>
          <button
            style={{
              background: 'transparent',
              color: '#ffffff',
              border: '4px solid #ff0000',
              borderRadius: '11px',
              padding: '16px 36px',
              minWidth: '132px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = '#00ff00';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = '#ff0000';
            }}
          >
            Manual Style Button
          </button>
          <p className="text-sm text-gray-600 mt-2">
            This should definitely have a red border that turns green on hover
          </p>
        </div>

        {/* Test 4: CSS Class Override Test */}
        <div className="p-6 bg-yellow-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">Test: CSS Class Override</h2>
          <div
            className="gradient-button gradient-button-outline"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              background: 'transparent',
              color: '#ffffff',
              border: '4px solid #ff0000',
              borderRadius: '11px',
              padding: '16px 36px',
              minWidth: '132px',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}
          >
            CSS Class Test
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Testing if our CSS classes are being applied
          </p>
        </div>

        {/* Test 5: All Button Variants for Comparison */}
        <div className="p-6 bg-purple-100 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-black">All Variants for Comparison</h2>
          <div className="flex flex-wrap gap-4">
            <GradientButton variant="default">Default</GradientButton>
            <GradientButton variant="variant">Variant</GradientButton>
            <GradientButton variant="destructive">Destructive</GradientButton>
            <GradientButton variant="outline">Outline</GradientButton>
            <GradientButton variant="ghost">Ghost</GradientButton>
            <GradientButton variant="link">Link</GradientButton>
          </div>
        </div>

        {/* Debug Info */}
        <div className="p-6 bg-red-100 rounded-lg border-2 border-red-500">
          <h2 className="text-xl font-bold mb-4 text-red-800">🚨 DEBUG CHECKLIST</h2>
          <div className="space-y-2 text-sm text-red-700">
            <p><strong>1. Manual Inline Style Button:</strong> Should have red border → green on hover</p>
            <p><strong>2. Outline Button:</strong> Should have red border if CSS is working</p>
            <p><strong>3. CSS Class Test:</strong> Should show if our classes are being applied</p>
            <p><strong>4. Browser DevTools:</strong> Inspect the Outline Button and check:</p>
            <ul className="ml-4 space-y-1">
              <li>• Are our CSS rules present?</li>
              <li>• Are they being overridden by other styles?</li>
              <li>• What's the computed border value?</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
