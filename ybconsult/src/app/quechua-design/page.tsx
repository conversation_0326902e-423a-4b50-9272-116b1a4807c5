"use client";

import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { <PERSON>talScroller } from "@/components/common/HorizontalScroller";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { UATFeedbackForm } from "@/components/feedback/UATFeedbackForm";
import { detectBrowser, detectCSSSupport, applyBrowserFallbacks } from "@/utils/browser-compatibility";
import Image from "next/image";

/**
 * Quechua Design Showcase Page
 * Teil der Phase 8: UI/UX Polish & Quechua Design Consistency Review (YM-801)
 */
export default function QuechuaDesignPage() {
  const pageRef = useRef<HTMLDivElement>(null);
  const browser = detectBrowser();
  const cssSupport = detectCSSSupport();

  useEffect(() => {
    // <PERSON><PERSON>rowser-Fallbacks an, wenn nötig
    if (pageRef.current) {
      applyBrowserFallbacks(pageRef.current);
    }
  }, []);

  return (
    <div ref={pageRef} className="container mx-auto py-12 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-center mb-8">Quechua Design System</h1>
        <p className="text-center mb-12 max-w-3xl mx-auto">
          Diese Seite zeigt das verbesserte Quechua-inspirierte Design-System für YoungMobility.
          Alle UI-Elemente wurden überarbeitet, um ein konsistentes und ansprechendes Benutzererlebnis zu bieten.
        </p>

        {/* Browser-Informationen */}
        <Card className="quechua-card mb-12">
          <CardHeader>
            <CardTitle>Browser-Kompatibilität</CardTitle>
            <CardDescription>Informationen über Ihren Browser und unterstützte Funktionen</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Browser-Details</h3>
                <ul className="space-y-2">
                  <li><strong>Browser:</strong> {browser.name} {browser.version}</li>
                  <li><strong>Mobiles Gerät:</strong> {browser.isMobile ? 'Ja' : 'Nein'}</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">CSS-Unterstützung</h3>
                <ul className="space-y-2">
                  <li>
                    <strong>CSS Grid:</strong>
                    <span className={cssSupport.supportsGrid ? 'text-green-600' : 'text-red-600'}>
                      {cssSupport.supportsGrid ? ' Unterstützt' : ' Nicht unterstützt'}
                    </span>
                  </li>
                  <li>
                    <strong>Flexbox:</strong>
                    <span className={cssSupport.supportsFlexbox ? 'text-green-600' : 'text-red-600'}>
                      {cssSupport.supportsFlexbox ? ' Unterstützt' : ' Nicht unterstützt'}
                    </span>
                  </li>
                  <li>
                    <strong>CSS Custom Properties:</strong>
                    <span className={cssSupport.supportsCustomProperties ? 'text-green-600' : 'text-red-600'}>
                      {cssSupport.supportsCustomProperties ? ' Unterstützt' : ' Nicht unterstützt'}
                    </span>
                  </li>
                  <li>
                    <strong>Backdrop Filter:</strong>
                    <span className={cssSupport.supportsBackdropFilter ? 'text-green-600' : 'text-red-600'}>
                      {cssSupport.supportsBackdropFilter ? ' Unterstützt' : ' Nicht unterstützt'}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Farbpalette */}
        <section className="mb-16">
          <h2 className="text-center mb-8">Quechua-inspirierte Farbpalette</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-6 rounded-lg bg-quechua-cream flex flex-col items-center justify-center aspect-square">
              <div className="font-bold mb-2">Cream</div>
              <div className="text-sm opacity-75">#FFFDF6</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-beige flex flex-col items-center justify-center aspect-square">
              <div className="font-bold mb-2">Beige</div>
              <div className="text-sm opacity-75">#FAF6E9</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-light-green flex flex-col items-center justify-center aspect-square">
              <div className="font-bold mb-2">Light Green</div>
              <div className="text-sm opacity-75">#DDEB9D</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-medium-green flex flex-col items-center justify-center aspect-square text-white">
              <div className="font-bold mb-2">Medium Green</div>
              <div className="text-sm opacity-75">#A0C878</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-dark-green flex flex-col items-center justify-center aspect-square text-white">
              <div className="font-bold mb-2">Dark Green</div>
              <div className="text-sm opacity-75">#5A7D2A</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-earth-brown flex flex-col items-center justify-center aspect-square text-white">
              <div className="font-bold mb-2">Earth Brown</div>
              <div className="text-sm opacity-75">#8B4513</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-terracotta flex flex-col items-center justify-center aspect-square text-white">
              <div className="font-bold mb-2">Terracotta</div>
              <div className="text-sm opacity-75">#CD5C5C</div>
            </div>
            <div className="p-6 rounded-lg bg-quechua-sky-blue flex flex-col items-center justify-center aspect-square">
              <div className="font-bold mb-2">Sky Blue</div>
              <div className="text-sm opacity-75">#87CEEB</div>
            </div>
          </div>
        </section>

        {/* Buttons */}
        <section className="mb-16">
          <h2 className="text-center mb-8">Buttons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="flex flex-col items-center space-y-4">
              <h3 className="text-lg font-semibold mb-2">Standard Buttons</h3>
              <div className="flex flex-wrap gap-4 justify-center">
                <GradientButton>Standard Button</GradientButton>
                <GradientButton variant="variant">Secondary</GradientButton>
                <GradientButton>Destructive</GradientButton>
                <GradientButton variant="variant">Outline</GradientButton>
                <GradientButton>Ghost</GradientButton>
              </div>
            </div>
            <div className="flex flex-col items-center space-y-4">
              <h3 className="text-lg font-semibold mb-2">Quechua Gradient Buttons</h3>
              <div className="flex flex-wrap gap-4 justify-center">
                <GradientButton>Primary</GradientButton>
                <GradientButton variant="variant">Variant</GradientButton>
              </div>
            </div>
          </div>
        </section>

        {/* Horizontaler Scroller */}
        <section className="mb-16">
          <h2 className="text-center mb-8">Horizontaler Scroller</h2>
          <HorizontalScroller title="Quechua-inspirierte Bilder">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
              <div key={num} className="min-w-[280px] h-[200px] rounded-lg overflow-hidden flex-shrink-0 relative">
                <Image
                  src={`/placeholder-fullscreen-${num}.jpg`}
                  alt={`Placeholder Image ${num}`}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              </div>
            ))}
          </HorizontalScroller>
        </section>

        {/* Cards */}
        <section className="mb-16">
          <h2 className="text-center mb-8">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="quechua-card">
              <CardHeader>
                <CardTitle>Quechua Card</CardTitle>
                <CardDescription>Eine Karte im Quechua-Design</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Diese Karte verwendet das Quechua-Design mit speziellen Hover-Effekten und Farbgebung.</p>
              </CardContent>
              <CardFooter>
                <Button className="quechua-ui-button">Mehr erfahren</Button>
              </CardFooter>
            </Card>

            <Card className="quechua-card">
              <CardHeader>
                <CardTitle>Quechua Card</CardTitle>
                <CardDescription>Eine Karte im Quechua-Design</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Diese Karte verwendet das Quechua-Design mit speziellen Hover-Effekten und Farbgebung.</p>
              </CardContent>
              <CardFooter>
                <Button className="quechua-ui-button">Mehr erfahren</Button>
              </CardFooter>
            </Card>

            <Card className="quechua-card">
              <CardHeader>
                <CardTitle>Quechua Card</CardTitle>
                <CardDescription>Eine Karte im Quechua-Design</CardDescription>
              </CardHeader>
              <CardContent>
                <p>Diese Karte verwendet das Quechua-Design mit speziellen Hover-Effekten und Farbgebung.</p>
              </CardContent>
              <CardFooter>
                <Button className="quechua-ui-button">Mehr erfahren</Button>
              </CardFooter>
            </Card>
          </div>
        </section>

        {/* Muster */}
        <section className="mb-16">
          <h2 className="text-center mb-8">Quechua-inspirierte Muster</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-6 rounded-lg bg-quechua-pattern h-40 flex items-center justify-center">
              <span className="bg-white/80 px-4 py-2 rounded">Diagonal Pattern</span>
            </div>
            <div className="p-6 rounded-lg quechua-pattern-stripes h-40 flex items-center justify-center">
              <span className="bg-white/80 px-4 py-2 rounded">Stripes Pattern</span>
            </div>
            <div className="p-6 rounded-lg quechua-pattern-dots h-40 flex items-center justify-center">
              <span className="bg-white/80 px-4 py-2 rounded">Dots Pattern</span>
            </div>
          </div>
        </section>

        {/* Eingebettetes Feedback-Formular */}
        <section className="mb-16">
          <h2 className="text-center mb-8">UAT Feedback</h2>
          <UATFeedbackForm floating={false} />
        </section>
      </motion.div>

      {/* Floating Feedback Button */}
      <UATFeedbackForm />
    </div>
  );
}
