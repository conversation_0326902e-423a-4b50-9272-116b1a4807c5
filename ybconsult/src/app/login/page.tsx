// /Users/<USER>/Coding/ybconsulting/ybconsult/src/app/login/page.tsx
"use client";

import { useForm, SubmitHandler } from "react-hook-form";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner"; // Importiere toast von sonner


interface IFormInput {
  email: string;
  password: string;
}

/**
 * LoginPage component for user authentication.
 * Allows users to sign in using their email and password.
 */
export default function LoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<IFormInput>();

  /**
   * Handles the form submission for user login.
   * @param {IFormInput} data - The form data containing email and password.
   */
  const onSubmit: SubmitHandler<IFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Login attempt with:", data.email); // Logging login attempt

    try {
      const result = await signIn("credentials", {
        redirect: false, // We handle redirect manually
        email: data.email,
        password: data.password,
      });

      if (result?.error) {
        console.error("Login failed:", result.error); // Logging login failure
        setError(result.error);
        toast.error("Login Fehlgeschlagen", { description: result.error });
      } else if (result?.ok) {
        console.log("Login successful, redirecting..."); // Logging successful login
        toast.success("Login Erfolgreich", { description: "Sie werden weitergeleitet..." });

        // Holen Sie die Session, um die Benutzerrolle zu erhalten
        const session = await fetch("/api/auth/session");
        const sessionData = await session.json();

        // Rollenbasierte Weiterleitung
        if (sessionData?.user?.role) {
          switch (sessionData.user.role) {
            case "BUSINESS_CLIENT":
              router.push("/client/dashboard");
              break;
            case "DRIVER":
              router.push("/driver/dashboard");
              break;
            case "ADMIN":
              router.push("/admin/dashboard");
              break;
            default:
              router.push("/"); // Fallback zur Startseite
          }
        } else {
          // Fallback, wenn keine Rolle gefunden wurde
          router.push("/");
        }
      } else {
        // Fallback for unexpected result states
        console.warn("Login attempt returned an unexpected result:", result);
        setError("Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.");
        toast.error("Login Fehlgeschlagen", { description: "Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut." });
      }
    } catch (e) {
      console.error("Exception during login:", e); // Logging exception during login
      setError("Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Anmelden</CardTitle>
          <CardDescription className="text-center">
            Geben Sie Ihre E-Mail-Adresse und Ihr Passwort ein, um sich anzumelden.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-Mail</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email", { required: "E-Mail ist erforderlich" })}
                aria-invalid={errors.email ? "true" : "false"}
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Passwort</Label>
              <Input
                id="password"
                type="password"
                placeholder="Ihr Passwort"
                {...register("password", { required: "Passwort ist erforderlich" })}
                aria-invalid={errors.password ? "true" : "false"}
              />
              {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
            </div>
            {error && <p className="text-sm text-red-500 text-center">{error}</p>}
            <GradientButton type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Anmelden..." : "Anmelden"}
            </GradientButton>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm">
          {/* Placeholder for links like 'Forgot password?' or 'Sign up' */}
          {/* <p>Passwort vergessen? / Noch kein Konto?</p> */}
        </CardFooter>
      </Card>
    </div>
  );
}