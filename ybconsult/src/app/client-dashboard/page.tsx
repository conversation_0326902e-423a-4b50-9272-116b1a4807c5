export default function ClientDashboardPage() {
  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>Client Dashboard</h1>
      
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: '20px', marginBottom: '30px' }}>
        {/* Aktive Aufträge */}
        <div style={{ border: '1px solid #e2e8f0', borderRadius: '8px', padding: '20px', backgroundColor: 'white' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px' }}>Aktive Aufträge</h2>
          <p style={{ color: '#64748b', marginBottom: '10px' }}>Laufende Fahrzeugtransporte</p>
          <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '15px' }}>0</div>
          <a href="/client-orders" style={{ color: '#3b82f6', textDecoration: 'none' }}>
            Alle aktiven Aufträge anzeigen
          </a>
        </div>
        
        {/* Anstehende Aktionen */}
        <div style={{ border: '1px solid #e2e8f0', borderRadius: '8px', padding: '20px', backgroundColor: 'white' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px' }}>Anstehende Aktionen</h2>
          <p style={{ color: '#64748b', marginBottom: '10px' }}>Aufträge, die Ihre Aufmerksamkeit benötigen</p>
          <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '15px' }}>0</div>
          <a href="/client-orders?status=pending" style={{ color: '#3b82f6', textDecoration: 'none' }}>
            Anstehende Aktionen anzeigen
          </a>
        </div>
        
        {/* Statistik */}
        <div style={{ border: '1px solid #e2e8f0', borderRadius: '8px', padding: '20px', backgroundColor: 'white' }}>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px' }}>Statistik</h2>
          <p style={{ color: '#64748b', marginBottom: '10px' }}>Übersicht Ihrer Aktivitäten</p>
          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
              <span style={{ color: '#64748b' }}>Abgeschlossene Aufträge</span>
              <span style={{ fontWeight: '500' }}>0</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#64748b' }}>Durchschnittliche Bewertung</span>
              <span style={{ fontWeight: '500' }}>-</span>
            </div>
          </div>
          <a href="/client-orders?status=completed" style={{ color: '#3b82f6', textDecoration: 'none' }}>
            Abgeschlossene Aufträge anzeigen
          </a>
        </div>
      </div>
      
      {/* Letzte Aktivitäten */}
      <div>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '15px' }}>Letzte Aktivitäten</h2>
        <div style={{ border: '1px solid #e2e8f0', borderRadius: '8px', padding: '20px', backgroundColor: 'white' }}>
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <p style={{ color: '#64748b', marginBottom: '15px' }}>Keine Aktivitäten vorhanden</p>
            <a 
              href="/client-orders-create" 
              style={{ 
                display: 'inline-block',
                padding: '8px 16px',
                backgroundColor: '#3b82f6',
                color: 'white',
                borderRadius: '6px',
                textDecoration: 'none',
                fontWeight: '500'
              }}
            >
              Ersten Auftrag erstellen
            </a>
          </div>
        </div>
      </div>
      
      {/* Navigation */}
      <div style={{ marginTop: '30px', borderTop: '1px solid #e2e8f0', paddingTop: '20px' }}>
        <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>Navigation</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <a 
            href="/client-dashboard" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Dashboard
          </a>
          <a 
            href="/client-orders" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Aufträge
          </a>
          <a 
            href="/client-orders-create" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Neuer Auftrag
          </a>
          <a 
            href="/client-billing" 
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Rechnungen
          </a>
        </div>
      </div>
    </div>
  );
}
