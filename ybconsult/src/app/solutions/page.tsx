import { GradientButton } from "@/components/ui/gradient-button";
import { SparklesCore } from "@/components/ui/sparkles";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Lösungen für Geschäftskunden | YoungMobility",
  description: "Maßgeschneiderte Fahrzeugüberführungslösungen für verschiedene Branchen - Autovermietungen, Leasing, Händler und mehr.",
  keywords: "Fahrzeugüberführung, Autovermietung, Leasing, Autohändler, Flottenmanagement",
};

// Branchenlösungen
const industrySolutions = [
  {
    id: "car-rental",
    title: "Autovermietungen",
    description: "Optimieren Sie Ihre Fahrzeuglogistik mit unserer spezialisierten Lösung für Autovermietungen.",
    imageUrl: "/images/solutions-car-rental.jpg",
    benefits: [
      "Flexible Fahrzeugüberführungen zwischen Stationen",
      "Reduzierung von Leerfahrten und Standzeiten",
      "Echtzeit-Tracking für Ihre Flotte",
      "Nahtlose Integration in Ihr Buchungssystem",
    ],
  },
  {
    id: "leasing",
    title: "Leasing-Unternehmen",
    description: "Steigern Sie die Effizienz Ihrer Fahrzeugübergaben und -rücknahmen mit unserer Leasing-Lösung.",
    imageUrl: "/images/solutions-leasing.jpg",
    benefits: [
      "Pünktliche Auslieferung und Abholung von Leasingfahrzeugen",
      "Detaillierte Zustandsdokumentation bei Übergabe",
      "Transparente Prozesse für Ihre Kunden",
      "Reduzierte Logistikkosten",
    ],
  },
  {
    id: "dealerships",
    title: "Autohändler",
    description: "Vereinfachen Sie den Fahrzeugtransport zwischen Standorten, Auktionen und Kunden.",
    imageUrl: "/images/solutions-dealership.jpg",
    benefits: [
      "Schneller Transport von Neufahrzeugen und Gebrauchtwagen",
      "Zuverlässige Auslieferung an Kunden",
      "Flexible Abholung von Fahrzeugen bei Auktionen",
      "Kosteneffiziente Standortüberführungen",
    ],
  },
  {
    id: "fleet",
    title: "Flottenmanagement",
    description: "Optimieren Sie die Logistik Ihrer Unternehmensflotte mit unserer spezialisierten Lösung.",
    imageUrl: "/images/solutions-fleet.jpg",
    benefits: [
      "Effiziente Fahrzeugrotation zwischen Standorten",
      "Zuverlässige Überführung zu Werkstätten und zurück",
      "Transparente Prozesse und Reporting",
      "Reduzierung von Ausfallzeiten",
    ],
  },
];

export default function SolutionsPage() {
  return (
    <div className="bg-black min-h-screen">
      {/* Hero Sektion */}
      <section className="relative h-[40vh] min-h-[300px] flex items-center justify-center text-center bg-black overflow-hidden">
        <Image
          src="/images/solutions-hero.jpg"
          alt="Maßgeschneiderte Lösungen für Geschäftskunden"
          fill
          className="object-cover opacity-30"
          sizes="100vw"
          quality={90}
        />
        <div className="relative z-10 p-8 md:p-12 bg-black/40 backdrop-blur-sm rounded-lg max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
            Maßgeschneiderte Lösungen für Ihre Branche
          </h1>
          <div className="relative inline-block mb-6 max-w-3xl mx-auto">
            <SparklesCore
              id="solutions-hero-sparkles"
              className="absolute inset-0 -inset-x-3 -inset-y-1"
              background="transparent"
              particleColor="#F8E7D1"
              particleDensity={20}
              minSize={0.3}
              maxSize={1}
              speed={1}
            />
            <p className="relative z-10 text-lg md:text-xl text-[#F8E7D1]">
              Entdecken Sie, wie YoungMobility die spezifischen Herausforderungen Ihrer Branche löst.
            </p>
          </div>
        </div>
      </section>

      {/* Branchenlösungen */}
      <section className="bg-black py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4 text-white">Unsere Branchenlösungen</h2>
            <div className="relative inline-block max-w-3xl mx-auto">
              <SparklesCore
                id="solutions-subtitle-sparkles"
                className="absolute inset-0 -inset-x-3 -inset-y-1"
                background="transparent"
                particleColor="#F8E7D1"
                particleDensity={20}
                minSize={0.3}
                maxSize={1}
                speed={1}
              />
              <p className="relative z-10 text-xl text-[#F8E7D1]">
                Wir verstehen die einzigartigen Anforderungen verschiedener Branchen und bieten maßgeschneiderte Lösungen.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {industrySolutions.map((solution) => (
              <div key={solution.id} className="bg-black rounded-xl shadow-2xl overflow-hidden flex flex-col h-full transition-all duration-300 relative">
                <GlowingEffect
                  disabled={false}
                  proximity={100}
                  spread={30}
                  blur={2}
                  className="rounded-xl"
                />
                <div className="relative h-64">
                  <Image
                    src={solution.imageUrl}
                    alt={solution.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
                <div className="p-6 flex-grow relative z-10">
                  <h3 className="text-2xl font-semibold mb-3 text-white">{solution.title}</h3>
                  <div className="relative mb-4">
                    <SparklesCore
                      id={`solution-${solution.id}-sparkles`}
                      className="absolute inset-0 -inset-x-2 -inset-y-1"
                      background="transparent"
                      particleColor="#F8E7D1"
                      particleDensity={12}
                      minSize={0.2}
                      maxSize={0.7}
                      speed={0.8}
                    />
                    <p className="relative z-10 text-[#F8E7D1]">{solution.description}</p>
                  </div>
                  <ul className="space-y-2 mb-6">
                    {solution.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-[#F8E7D1] mr-2">✓</span>
                        <span className="text-gray-300">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                  <GradientButton asChild className="mt-auto">
                    <Link href={`/solutions/${solution.id}`}>Mehr erfahren</Link>
                  </GradientButton>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Warum YoungMobility */}
      <section className="bg-black py-16 relative overflow-hidden">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-white">Warum YoungMobility?</h2>
            <div className="relative inline-block max-w-3xl mx-auto">
              <SparklesCore
                id="why-youngmobility-sparkles"
                className="absolute inset-0 -inset-x-3 -inset-y-1"
                background="transparent"
                particleColor="#F8E7D1"
                particleDensity={20}
                minSize={0.3}
                maxSize={1}
                speed={1}
              />
              <p className="relative z-10 text-xl text-[#F8E7D1]">
                Unsere Plattform bietet einzigartige Vorteile für alle Geschäftskunden, unabhängig von der Branche.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-900 p-8 rounded-xl shadow-2xl border border-gray-800 hover:border-gray-700 transition-all duration-300">
              <div className="text-4xl text-[#F8E7D1] mb-4">🔄</div>
              <h3 className="text-xl font-semibold mb-3 text-white">Flexibilität</h3>
              <p className="text-gray-300">
                Buchen Sie Überführungen nach Bedarf, ohne langfristige Verpflichtungen. Unsere Plattform passt sich Ihren Anforderungen an.
              </p>
            </div>
            <div className="bg-gray-900 p-8 rounded-xl shadow-2xl border border-gray-800 hover:border-gray-700 transition-all duration-300">
              <div className="text-4xl text-[#F8E7D1] mb-4">📊</div>
              <h3 className="text-xl font-semibold mb-3 text-white">Transparenz</h3>
              <p className="text-gray-300">
                Verfolgen Sie jede Überführung in Echtzeit und erhalten Sie detaillierte Berichte über alle Aktivitäten.
              </p>
            </div>
            <div className="bg-gray-900 p-8 rounded-xl shadow-2xl border border-gray-800 hover:border-gray-700 transition-all duration-300">
              <div className="text-4xl text-[#F8E7D1] mb-4">💰</div>
              <h3 className="text-xl font-semibold mb-3 text-white">Kosteneffizienz</h3>
              <p className="text-gray-300">
                Reduzieren Sie Ihre Logistikkosten durch optimierte Routen, bedarfsgerechte Buchungen und Vermeidung von Leerfahrten.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="bg-black py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-white">Bereit für maßgeschneiderte Lösungen?</h2>
          <div className="relative inline-block max-w-3xl mx-auto mb-8">
            <SparklesCore
              id="solutions-cta-sparkles"
              className="absolute inset-0 -inset-x-3 -inset-y-1"
              background="transparent"
              particleColor="#F8E7D1"
              particleDensity={20}
              minSize={0.3}
              maxSize={1}
              speed={1}
            />
            <p className="relative z-10 text-xl text-[#F8E7D1]">
              Kontaktieren Sie uns für eine individuelle Beratung oder registrieren Sie sich, um sofort loszulegen.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton asChild>
              <Link href="/register-client">Jetzt registrieren</Link>
            </GradientButton>
            <GradientButton variant="variant" asChild>
              <Link href="/contact">Kontakt aufnehmen</Link>
            </GradientButton>
          </div>
        </div>
      </section>
    </div>
  );
}
