import { GradientButton } from "@/components/ui/gradient-button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import { notFound } from "next/navigation";

// Typen für die Lösungsdaten
type SolutionFeature = {
  title: string;
  description: string;
  icon: string;
};

type CaseStudy = {
  company: string;
  industry: string;
  challenge: string;
  solution: string;
  results: string[];
  logo: string;
};

type SolutionData = {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  imageUrl: string;
  heroImageUrl: string;
  benefits: string[];
  features: SolutionFeature[];
  caseStudy?: CaseStudy;
  faq: { question: string; answer: string }[];
};

// Daten für die verschiedenen Branchenlösungen
const solutionsData: Record<string, SolutionData> = {
  "car-rental": {
    id: "car-rental",
    title: "Lösungen für Autovermietungen",
    description: "Optimieren Sie Ihre Fahrzeuglogistik mit unserer spezialisierten Lösung für Autovermietungen.",
    longDescription: "Autovermietungen stehen vor der Herausforderung, Fahrzeuge effizient zwischen verschiedenen Standorten zu bewegen, um die Verfügbarkeit zu maximieren und Leerfahrten zu minimieren. YoungMobility bietet eine maßgeschneiderte Lösung, die speziell auf die Bedürfnisse von Autovermietungen zugeschnitten ist.",
    imageUrl: "/images/solutions-car-rental.jpg",
    heroImageUrl: "/images/solutions-car-rental-hero.jpg",
    benefits: [
      "Flexible Fahrzeugüberführungen zwischen Stationen",
      "Reduzierung von Leerfahrten und Standzeiten",
      "Echtzeit-Tracking für Ihre Flotte",
      "Nahtlose Integration in Ihr Buchungssystem",
      "Detaillierte Zustandsdokumentation bei Übergabe",
      "Schnelle Reaktion auf Bedarfsspitzen",
    ],
    features: [
      {
        title: "Standortübergreifende Flottenverwaltung",
        description: "Verwalten Sie Ihre Fahrzeuge standortübergreifend und optimieren Sie die Verfügbarkeit an allen Stationen.",
        icon: "🚗",
      },
      {
        title: "Bedarfsorientierte Überführungen",
        description: "Reagieren Sie flexibel auf Nachfragespitzen und überführen Sie Fahrzeuge genau dorthin, wo sie benötigt werden.",
        icon: "📊",
      },
      {
        title: "Zustandsdokumentation",
        description: "Detaillierte Dokumentation des Fahrzeugzustands bei Abholung und Übergabe mit Fotos und Berichten.",
        icon: "📷",
      },
      {
        title: "API-Integration",
        description: "Nahtlose Integration in Ihr bestehendes Buchungs- und Flottenmanagementsystem über unsere API.",
        icon: "🔄",
      },
    ],
    caseStudy: {
      company: "EuroRent GmbH",
      industry: "Autovermietung",
      challenge: "EuroRent betreibt 25 Standorte in Deutschland und hatte Schwierigkeiten, die Fahrzeugverfügbarkeit an allen Standorten zu optimieren. Besonders an Wochenenden und zu Ferienzeiten kam es zu Engpässen an einigen Standorten, während an anderen Standorten Fahrzeuge ungenutzt blieben.",
      solution: "Mit YoungMobility implementierte EuroRent ein flexibles System für standortübergreifende Fahrzeugüberführungen. Die Plattform wurde in das bestehende Buchungssystem integriert und ermöglicht nun die automatische Planung von Überführungen basierend auf Verfügbarkeit und Nachfrageprognosen.",
      results: [
        "30% Reduzierung von Standzeiten",
        "15% Steigerung der Fahrzeugauslastung",
        "Verbesserung der Kundenzufriedenheit durch höhere Fahrzeugverfügbarkeit",
        "Reduzierung der Logistikkosten um 25%",
      ],
      logo: "/images/case-study-eurorent.png",
    },
    faq: [
      {
        question: "Wie schnell können Fahrzeuge zwischen Standorten überführt werden?",
        answer: "Die Überführungszeit hängt von der Entfernung und Verfügbarkeit der Fahrer ab. In der Regel können wir innerhalb von 24 Stunden Fahrer für Überführungen bereitstellen. Bei dringenden Anfragen ist auch eine schnellere Bearbeitung möglich.",
      },
      {
        question: "Wie wird der Zustand der Fahrzeuge dokumentiert?",
        answer: "Unsere Fahrer dokumentieren den Zustand der Fahrzeuge bei Abholung und Übergabe mit Fotos und einem detaillierten Bericht. Diese Dokumentation ist über die Plattform jederzeit einsehbar und kann bei Bedarf exportiert werden.",
      },
      {
        question: "Kann YoungMobility in unser bestehendes Buchungssystem integriert werden?",
        answer: "Ja, wir bieten eine API, die eine nahtlose Integration in Ihr bestehendes Buchungs- und Flottenmanagementsystem ermöglicht. Unsere Entwickler unterstützen Sie bei der Integration und stellen sicher, dass alle Daten korrekt synchronisiert werden.",
      },
      {
        question: "Wie werden die Kosten berechnet?",
        answer: "Die Kosten werden basierend auf der Entfernung, dem Fahrzeugtyp und der Dringlichkeit der Überführung berechnet. Wir bieten verschiedene Preismodelle an, darunter Pay-per-Use und Volumenpakete für regelmäßige Überführungen.",
      },
    ],
  },
  "leasing": {
    id: "leasing",
    title: "Lösungen für Leasing-Unternehmen",
    description: "Steigern Sie die Effizienz Ihrer Fahrzeugübergaben und -rücknahmen mit unserer Leasing-Lösung.",
    longDescription: "Leasing-Unternehmen stehen vor der Herausforderung, Fahrzeuge pünktlich und effizient an Kunden zu übergeben und nach Ablauf des Leasingvertrags wieder abzuholen. YoungMobility bietet eine spezialisierte Lösung, die den gesamten Prozess der Fahrzeugübergabe und -rücknahme optimiert.",
    imageUrl: "/images/solutions-leasing.jpg",
    heroImageUrl: "/images/solutions-leasing-hero.jpg",
    benefits: [
      "Pünktliche Auslieferung und Abholung von Leasingfahrzeugen",
      "Detaillierte Zustandsdokumentation bei Übergabe und Rücknahme",
      "Transparente Prozesse für Ihre Kunden",
      "Reduzierte Logistikkosten",
      "Flexible Terminplanung",
      "Professionelle Fahrzeugübergabe",
    ],
    features: [
      {
        title: "Terminbasierte Überführungen",
        description: "Planen Sie Fahrzeugübergaben und -rücknahmen termingenau und bieten Sie Ihren Kunden flexible Zeitfenster.",
        icon: "📅",
      },
      {
        title: "Zustandsdokumentation",
        description: "Umfassende Dokumentation des Fahrzeugzustands bei Übergabe und Rücknahme mit Fotos und detaillierten Berichten.",
        icon: "📷",
      },
      {
        title: "Kundenportal",
        description: "Transparente Prozesse für Ihre Kunden durch ein optionales Kundenportal zur Terminvereinbarung und Statusverfolgung.",
        icon: "👥",
      },
      {
        title: "Flottenmanagement",
        description: "Optimieren Sie die Logistik Ihrer Leasingflotte und reduzieren Sie Standzeiten zwischen Verträgen.",
        icon: "🚗",
      },
    ],
    faq: [
      {
        question: "Wie wird die Qualität der Fahrzeugübergabe sichergestellt?",
        answer: "Unsere Fahrer werden speziell für die professionelle Fahrzeugübergabe geschult. Sie erklären dem Kunden alle wichtigen Funktionen des Fahrzeugs und dokumentieren den Zustand detailliert. Die Übergabe erfolgt nach einem standardisierten Protokoll.",
      },
      {
        question: "Können Kunden den Übergabetermin selbst wählen?",
        answer: "Ja, über unser optionales Kundenportal können Ihre Kunden aus verfügbaren Zeitfenstern wählen oder Terminwünsche angeben. Alternativ können Termine auch direkt über Ihr System geplant werden.",
      },
      {
        question: "Wie werden Schäden bei der Fahrzeugrücknahme dokumentiert?",
        answer: "Bei der Rücknahme wird der Fahrzeugzustand umfassend mit Fotos und einem detaillierten Bericht dokumentiert. Diese Dokumentation wird Ihnen sofort zur Verfügung gestellt und kann für die weitere Bearbeitung verwendet werden.",
      },
      {
        question: "Ist eine Integration in unser Leasingmanagementsystem möglich?",
        answer: "Ja, wir bieten eine API für die Integration in Ihr Leasingmanagementsystem. Dadurch können Überführungsaufträge direkt aus Ihrem System erstellt und verfolgt werden.",
      },
    ],
  },
  "dealerships": {
    id: "dealerships",
    title: "Lösungen für Autohändler",
    description: "Vereinfachen Sie den Fahrzeugtransport zwischen Standorten, Auktionen und Kunden.",
    longDescription: "Autohändler benötigen flexible und zuverlässige Lösungen für den Transport von Fahrzeugen zwischen verschiedenen Standorten, Auktionen und Kunden. YoungMobility bietet eine spezialisierte Lösung, die den gesamten Prozess der Fahrzeuglogistik für Autohändler optimiert.",
    imageUrl: "/images/solutions-dealership.jpg",
    heroImageUrl: "/images/solutions-dealership-hero.jpg",
    benefits: [
      "Schneller Transport von Neufahrzeugen und Gebrauchtwagen",
      "Zuverlässige Auslieferung an Kunden",
      "Flexible Abholung von Fahrzeugen bei Auktionen",
      "Kosteneffiziente Standortüberführungen",
      "Professionelle Fahrzeugübergabe an Kunden",
      "Reduzierung von Logistikkosten",
    ],
    features: [
      {
        title: "Fahrzeugauslieferung",
        description: "Professionelle Auslieferung von Fahrzeugen an Ihre Kunden mit detaillierter Einweisung und Dokumentation.",
        icon: "🚚",
      },
      {
        title: "Auktionsservice",
        description: "Flexible Abholung von Fahrzeugen bei Auktionen und Transport zu Ihrem Standort oder direkt zum Kunden.",
        icon: "🔨",
      },
      {
        title: "Standortüberführungen",
        description: "Effiziente Überführung von Fahrzeugen zwischen verschiedenen Standorten zur Optimierung Ihres Fahrzeugangebots.",
        icon: "🔄",
      },
      {
        title: "Zustandsdokumentation",
        description: "Detaillierte Dokumentation des Fahrzeugzustands bei Abholung und Übergabe mit Fotos und Berichten.",
        icon: "📷",
      },
    ],
    faq: [
      {
        question: "Wie schnell können Fahrzeuge von Auktionen abgeholt werden?",
        answer: "In der Regel können wir innerhalb von 24-48 Stunden nach Zuschlag Fahrer für die Abholung bereitstellen. Bei dringenden Anfragen ist auch eine schnellere Bearbeitung möglich.",
      },
      {
        question: "Können Fahrzeuge direkt von der Auktion zum Kunden geliefert werden?",
        answer: "Ja, wir bieten einen direkten Transport von der Auktion zum Kunden an. Dies spart Zeit und reduziert die Logistikkosten, da das Fahrzeug nicht erst zu Ihrem Standort transportiert werden muss.",
      },
      {
        question: "Wie wird die Qualität der Fahrzeugübergabe an Kunden sichergestellt?",
        answer: "Unsere Fahrer werden speziell für die professionelle Fahrzeugübergabe geschult. Sie erklären dem Kunden alle wichtigen Funktionen des Fahrzeugs und dokumentieren die Übergabe detailliert.",
      },
      {
        question: "Können auch Sonderfahrzeuge oder Oldtimer transportiert werden?",
        answer: "Ja, wir haben spezialisierte Fahrer für den Transport von Sonderfahrzeugen und Oldtimern. Diese Fahrer verfügen über besondere Erfahrung im Umgang mit wertvollen oder empfindlichen Fahrzeugen.",
      },
    ],
  },
  "fleet": {
    id: "fleet",
    title: "Lösungen für Flottenmanagement",
    description: "Optimieren Sie die Logistik Ihrer Unternehmensflotte mit unserer spezialisierten Lösung.",
    longDescription: "Unternehmen mit eigenen Fahrzeugflotten stehen vor der Herausforderung, ihre Fahrzeuge effizient zu verwalten und zu bewegen. YoungMobility bietet eine spezialisierte Lösung für das Flottenmanagement, die den gesamten Prozess der Fahrzeuglogistik optimiert.",
    imageUrl: "/images/solutions-fleet.jpg",
    heroImageUrl: "/images/solutions-fleet-hero.jpg",
    benefits: [
      "Effiziente Fahrzeugrotation zwischen Standorten",
      "Zuverlässige Überführung zu Werkstätten und zurück",
      "Transparente Prozesse und Reporting",
      "Reduzierung von Ausfallzeiten",
      "Optimierung der Flottengröße",
      "Kostenreduktion durch effiziente Logistik",
    ],
    features: [
      {
        title: "Standortübergreifendes Flottenmanagement",
        description: "Verwalten Sie Ihre Fahrzeugflotte standortübergreifend und optimieren Sie die Verfügbarkeit an allen Standorten.",
        icon: "🏢",
      },
      {
        title: "Werkstattservice",
        description: "Zuverlässige Überführung von Fahrzeugen zu Werkstätten und zurück, inklusive Terminkoordination.",
        icon: "🔧",
      },
      {
        title: "Fahrzeugrotation",
        description: "Optimieren Sie die Nutzung Ihrer Fahrzeuge durch regelmäßige Rotation zwischen verschiedenen Standorten oder Abteilungen.",
        icon: "🔄",
      },
      {
        title: "Reporting und Analyse",
        description: "Detaillierte Berichte und Analysen zur Optimierung Ihrer Flottenlogistik und Identifizierung von Einsparpotentialen.",
        icon: "📊",
      },
    ],
    faq: [
      {
        question: "Wie kann YoungMobility bei der Optimierung unserer Flottengröße helfen?",
        answer: "Durch detaillierte Analysen der Fahrzeugnutzung und -bewegungen können wir Ihnen helfen, Ihre Flottengröße zu optimieren. Wir identifizieren Fahrzeuge, die wenig genutzt werden, und schlagen Maßnahmen zur Effizienzsteigerung vor.",
      },
      {
        question: "Können Werkstatttermine über die Plattform koordiniert werden?",
        answer: "Ja, wir bieten eine Terminkoordination für Werkstattbesuche an. Wir überführen Ihre Fahrzeuge pünktlich zur Werkstatt und holen sie nach Abschluss der Arbeiten wieder ab.",
      },
      {
        question: "Wie werden die Kosten für Flottenüberführungen berechnet?",
        answer: "Die Kosten werden basierend auf der Entfernung, dem Fahrzeugtyp und der Häufigkeit der Überführungen berechnet. Wir bieten verschiedene Preismodelle an, darunter Pay-per-Use und Volumenpakete für regelmäßige Überführungen.",
      },
      {
        question: "Ist eine Integration in unser bestehendes Flottenmanagementsystem möglich?",
        answer: "Ja, wir bieten eine API für die Integration in Ihr Flottenmanagementsystem. Dadurch können Überführungsaufträge direkt aus Ihrem System erstellt und verfolgt werden.",
      },
    ],
  },
};

// Generiere Metadaten für die Seite basierend auf dem Slug
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const solution = solutionsData[params.slug];

  if (!solution) {
    return {
      title: "Lösung nicht gefunden | YoungMobility",
      description: "Die gesuchte Lösung wurde nicht gefunden.",
    };
  }

  return {
    title: `${solution.title} | YoungMobility`,
    description: solution.description,
    keywords: `Fahrzeugüberführung, ${solution.title}, YoungMobility, Fahrzeuglogistik`,
    openGraph: {
      title: solution.title,
      description: solution.description,
      images: [
        {
          url: solution.imageUrl,
          width: 1200,
          height: 630,
          alt: solution.title,
        },
      ],
    },
  };
}

// Generiere statische Pfade für alle Lösungen
export async function generateStaticParams() {
  return Object.keys(solutionsData).map((slug) => ({
    slug,
  }));
}

export default function SolutionPage({ params }: { params: { slug: string } }) {
  const solution = solutionsData[params.slug];

  // Wenn die Lösung nicht gefunden wurde, zeige 404
  if (!solution) {
    notFound();
  }

  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[50vh] min-h-[400px] flex items-center justify-center text-center">
        <Image
          src={solution.heroImageUrl}
          alt={solution.title}
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="relative z-10 p-8 md:p-12 bg-black/40 backdrop-blur-sm rounded-lg max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
            {solution.title}
          </h1>
          <p className="text-lg md:text-xl text-gray-100 mb-6 max-w-3xl mx-auto">
            {solution.description}
          </p>
        </div>
      </section>

      {/* Übersicht */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Übersicht</h2>
          <p className="text-lg text-gray-700 mb-8">
            {solution.longDescription}
          </p>

          <div className="bg-gray-50 p-8 rounded-xl mb-8">
            <h3 className="text-xl font-semibold mb-4 text-gray-900">Ihre Vorteile</h3>
            <ul className="space-y-2">
              {solution.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-accent mr-2">✓</span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Unsere Lösung im Detail</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {solution.features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-md">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-3 text-gray-900">{feature.title}</h3>
                <p className="text-gray-700">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Case Study (falls vorhanden) */}
      {solution.caseStudy && (
        <section className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white p-8 rounded-xl shadow-lg">
            <div className="flex flex-col md:flex-row items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Fallstudie: {solution.caseStudy.company}</h2>
              {solution.caseStudy.logo && (
                <div className="w-32 h-16 relative mt-4 md:mt-0">
                  <Image
                    src={solution.caseStudy.logo}
                    alt={solution.caseStudy.company}
                    fill
                    className="object-contain"
                  />
                </div>
              )}
            </div>

            <div className="space-y-4 mb-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Herausforderung</h3>
                <p className="text-gray-700">{solution.caseStudy.challenge}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Lösung</h3>
                <p className="text-gray-700">{solution.caseStudy.solution}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Ergebnisse</h3>
                <ul className="space-y-2">
                  {solution.caseStudy.results.map((result, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-accent mr-2">✓</span>
                      <span>{result}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* FAQ */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Häufig gestellte Fragen</h2>

          <div className="max-w-4xl mx-auto space-y-6">
            {solution.faq.map((item, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-md">
                <h3 className="text-xl font-semibold mb-3 text-gray-900">{item.question}</h3>
                <p className="text-gray-700">{item.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="container mx-auto px-4 text-center py-8">
        <h2 className="text-3xl font-bold mb-6 text-gray-900">Bereit, Ihre Fahrzeuglogistik zu optimieren?</h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Kontaktieren Sie uns für eine individuelle Beratung oder registrieren Sie sich, um sofort loszulegen.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <GradientButton asChild className="bg-accent hover:bg-accent/90 text-white">
            <Link href="/register-client">Jetzt registrieren</Link>
          </GradientButton>
          <GradientButton asChild variant="variant" className="border-accent text-accent hover:bg-accent/10">
            <Link href="/contact">Kontakt aufnehmen</Link>
          </GradientButton>
        </div>
      </section>
    </div>
  );
}
