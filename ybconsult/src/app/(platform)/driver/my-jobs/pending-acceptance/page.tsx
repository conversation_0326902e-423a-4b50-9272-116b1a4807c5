"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Calendar, Clock, Truck, CheckCircle, X, AlertTriangle } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import FeedbackDialog from "@/components/driver/FeedbackDialog";

interface Assignment {
  id: string;
  orderId: string;
  status: string;
  assignedAt: string;
  order: {
    id: string;
    orderReference: string;
    title: string;
    vehicleType: string;
    vehicleMake?: string;
    vehicleModel?: string;
    pickupAddress: string;
    pickupCity: string;
    pickupDate: string;
    deliveryAddress: string;
    deliveryCity: string;
    deliveryDate: string;
    estimatedPrice?: number;
    finalPrice?: number;
    currency: string;
    specialInstructions?: string;
    client: {
      companyName: string;
    };
  };
}

export default function PendingAcceptancePage() {
  const queryClient = useQueryClient();
  const [declineDialogOpen, setDeclineDialogOpen] = useState(false);
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<"rejection" | "assignment">("assignment");
  const [selectedAssignment, setSelectedAssignment] = useState<Assignment | null>(null);
  const [declineReason, setDeclineReason] = useState("");

  // Abrufen der ausstehenden Zuweisungen
  const { data: pendingAssignments, isLoading } = useQuery({
    queryKey: ["driver-pending-assignments"],
    queryFn: async () => {
      const response = await fetch("/api/driver/assignments/pending");
      if (!response.ok) {
        throw new Error("Fehler beim Laden der ausstehenden Aufträge");
      }
      return response.json();
    },
  });

  // Mutation für die Annahme eines Auftrags
  const acceptMutation = useMutation({
    mutationFn: async (assignmentId: string) => {
      const response = await fetch(`/api/driver/assignments/${assignmentId}/accept`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler bei der Auftragsannahme");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Auftrag erfolgreich angenommen");
      queryClient.invalidateQueries({ queryKey: ["driver-pending-assignments"] });
      queryClient.invalidateQueries({ queryKey: ["driver-active-assignments"] });
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });

  // Mutation für die Ablehnung eines Auftrags
  const declineMutation = useMutation({
    mutationFn: async ({ assignmentId, reason }: { assignmentId: string; reason: string }) => {
      const response = await fetch(`/api/driver/assignments/${assignmentId}/decline`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ reason }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler bei der Auftragsablehnung");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Auftrag abgelehnt");
      setDeclineDialogOpen(false);
      setSelectedAssignment(null);
      setDeclineReason("");
      queryClient.invalidateQueries({ queryKey: ["driver-pending-assignments"] });
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });

  const handleAccept = (assignment: Assignment) => {
    acceptMutation.mutate(assignment.id);
    // Show feedback dialog after accepting
    setSelectedAssignment(assignment);
    setFeedbackType("assignment");
    setFeedbackDialogOpen(true);
  };

  const handleDeclineClick = (assignment: Assignment) => {
    setSelectedAssignment(assignment);
    setDeclineDialogOpen(true);
  };

  const handleDeclineSubmit = () => {
    if (!selectedAssignment) return;

    declineMutation.mutate({
      assignmentId: selectedAssignment.id,
      reason: declineReason,
    });

    // Show feedback dialog after declining
    setFeedbackType("rejection");
    setDeclineDialogOpen(false);
    setFeedbackDialogOpen(true);
  };

  // Rendert eine Auftragskarte
  const renderAssignmentCard = (assignment: Assignment) => {
    const { order } = assignment;

    return (
      <Card key={assignment.id} className="overflow-hidden transition-all hover:shadow-md">
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-lg">{order.title}</CardTitle>
              <CardDescription>
                {order.vehicleMake} {order.vehicleModel} • {order.vehicleType}
              </CardDescription>
            </div>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
              Bestätigung ausstehend
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-3">
              <div className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Abholung</div>
                  <div className="text-sm">{order.pickupCity}</div>
                  <div className="text-xs text-muted-foreground">{order.pickupAddress}</div>
                </div>
              </div>
              <div className="flex items-start">
                <Calendar className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Abholtermin</div>
                  <div className="text-sm">
                    {format(new Date(order.pickupDate), 'PPP', { locale: de })}
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-start">
                <MapPin className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Lieferung</div>
                  <div className="text-sm">{order.deliveryCity}</div>
                  <div className="text-xs text-muted-foreground">{order.deliveryAddress}</div>
                </div>
              </div>
              <div className="flex items-start">
                <Calendar className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                <div>
                  <div className="font-medium">Liefertermin</div>
                  <div className="text-sm">
                    {format(new Date(order.deliveryDate), 'PPP', { locale: de })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 mb-2">
            <div className="flex-1">
              <div className="text-sm text-muted-foreground">Auftraggeber:</div>
              <div>{order.client.companyName}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Vergütung:</div>
              <div className="font-medium">
                {(order.finalPrice || order.estimatedPrice || 0).toFixed(2)} {order.currency}
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Zugewiesen am:</div>
              <div>{format(new Date(assignment.assignedAt), 'dd.MM.yyyy', { locale: de })}</div>
            </div>
          </div>

          {order.specialInstructions && (
            <div className="mt-3 p-3 bg-muted rounded-md">
              <div className="text-sm font-medium">Besondere Anweisungen:</div>
              <div className="text-sm mt-1">{order.specialInstructions}</div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-end gap-2 pt-2">
          <Button
            variant="outline"
            className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
            onClick={() => handleDeclineClick(assignment)}
            disabled={declineMutation.isPending}
          >
            <X className="mr-2 h-4 w-4" />
            Ablehnen
          </Button>
          <Button
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
            onClick={() => handleAccept(assignment)}
            disabled={acceptMutation.isPending}
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Auftrag annehmen
          </Button>
        </CardFooter>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="container py-6">
        <h1 className="text-2xl font-bold mb-6">Ausstehende Aufträge</h1>
        <div className="space-y-4">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-2">Ausstehende Aufträge</h1>
      <p className="text-muted-foreground mb-6">
        Hier sehen Sie Aufträge, die Ihnen zugewiesen wurden und auf Ihre Bestätigung warten.
      </p>

      {pendingAssignments && pendingAssignments.length > 0 ? (
        <div className="space-y-4">
          {pendingAssignments.map(renderAssignmentCard)}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-lg font-medium">Keine ausstehenden Aufträge</p>
            <p className="text-muted-foreground mb-4">
              Sie haben derzeit keine Aufträge, die auf Ihre Bestätigung warten.
            </p>
            <Button asChild>
              <Link href="/driver/jobs">Verfügbare Aufträge ansehen</Link>
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Dialog für die Ablehnungsbegründung */}
      <Dialog open={declineDialogOpen} onOpenChange={setDeclineDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Auftrag ablehnen</DialogTitle>
            <DialogDescription>
              Bitte geben Sie einen Grund für die Ablehnung an. Dies hilft uns, die Auftragsvergabe zu verbessern.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Grund für die Ablehnung (optional)"
              value={declineReason}
              onChange={(e) => setDeclineReason(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeclineDialogOpen(false)}
              disabled={declineMutation.isPending}
            >
              Abbrechen
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeclineSubmit}
              disabled={declineMutation.isPending}
            >
              {declineMutation.isPending ? "Wird abgelehnt..." : "Auftrag ablehnen"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Feedback Dialog */}
      {selectedAssignment && (
        <FeedbackDialog
          isOpen={feedbackDialogOpen}
          onClose={() => setFeedbackDialogOpen(false)}
          orderId={selectedAssignment.orderId}
          orderTitle={selectedAssignment.order.title}
          type={feedbackType}
        />
      )}
    </div>
  );
}
