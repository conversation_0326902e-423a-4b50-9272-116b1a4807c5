"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { GradientButton } from "@/components/ui/gradient-button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { MapPin, Calendar, TrendingUp, Truck, Clock, ArrowRight, CheckCircle2, Camera, Upload } from "lucide-react";

// Mock data for active jobs
const mockActiveJobs = [
  {
    id: "job1",
    title: "Transport BMW X5 from Berlin to Munich",
    vehicleType: "SUV",
    vehicleMake: "BMW",
    vehicleModel: "X5",
    pickupLocation: "Berlin",
    pickupAddress: "BMW Niederlassung Berlin, Kaiserdamm 90, 14057 Berlin",
    pickupDate: "2025-05-25",
    deliveryLocation: "Munich",
    deliveryAddress: "BMW Welt, Am Olympiapark 1, 80809 München",
    deliveryDate: "2025-05-26",
    distance: 504,
    paymentAmount: 350,
    status: "ASSIGNED", // ASSIGNED, EN_ROUTE_TO_PICKUP, PICKED_UP, EN_ROUTE_TO_DELIVERY, DELIVERED
    clientCompany: "BMW Group",
    assignedAt: "2025-05-23T15:30:00Z",
  },
  {
    id: "job2",
    title: "Deliver Mercedes E-Class to Hamburg",
    vehicleType: "SEDAN",
    vehicleMake: "Mercedes",
    vehicleModel: "E-Class",
    pickupLocation: "Frankfurt",
    pickupAddress: "Mercedes-Benz Niederlassung Frankfurt, Hanauer Landstraße 222, 60314 Frankfurt",
    pickupDate: "2025-05-26",
    deliveryLocation: "Hamburg",
    deliveryAddress: "Mercedes-Benz Niederlassung Hamburg, Süderstraße 282, 20537 Hamburg",
    deliveryDate: "2025-05-27",
    distance: 393,
    paymentAmount: 280,
    status: "EN_ROUTE_TO_PICKUP",
    clientCompany: "Daimler AG",
    assignedAt: "2025-05-23T12:15:00Z",
    enRouteToPickupAt: "2025-05-24T08:30:00Z",
  },
];

// Mock data for completed jobs
const mockCompletedJobs = [
  {
    id: "job3",
    title: "Transport Audi A4 from Hamburg to Berlin",
    vehicleType: "SEDAN",
    vehicleMake: "Audi",
    vehicleModel: "A4",
    pickupLocation: "Hamburg",
    deliveryLocation: "Berlin",
    deliveryDate: "2025-05-20",
    distance: 289,
    paymentAmount: 240,
    status: "COMPLETED",
    clientCompany: "Audi AG",
    completedAt: "2025-05-20T16:45:00Z",
  },
  {
    id: "job4",
    title: "Deliver Porsche Cayenne to Munich",
    vehicleType: "SUV",
    vehicleMake: "Porsche",
    vehicleModel: "Cayenne",
    pickupLocation: "Stuttgart",
    deliveryLocation: "Munich",
    deliveryDate: "2025-05-18",
    distance: 220,
    paymentAmount: 320,
    status: "COMPLETED",
    clientCompany: "Porsche AG",
    completedAt: "2025-05-18T14:20:00Z",
  },
];

export default function MyJobs() {
  const { data: session } = useSession();
  const [activeJobs, setActiveJobs] = useState([]);
  const [completedJobs, setCompletedJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedJob, setSelectedJob] = useState(null);
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [photoType, setPhotoType] = useState(""); // "pickup" or "delivery"
  const [photoFile, setPhotoFile] = useState(null);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);

  useEffect(() => {
    // Simulate API call to fetch jobs
    const timer = setTimeout(() => {
      setActiveJobs(mockActiveJobs);
      setCompletedJobs(mockCompletedJobs);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleStatusUpdate = (jobId, newStatus) => {
    // Find the job to update
    const jobIndex = activeJobs.findIndex(job => job.id === jobId);
    if (jobIndex === -1) return;

    // Create a copy of the job
    const updatedJob = { ...activeJobs[jobIndex] };

    // Update the status
    updatedJob.status = newStatus;

    // Add timestamp based on the new status
    const now = new Date().toISOString();
    switch (newStatus) {
      case "EN_ROUTE_TO_PICKUP":
        updatedJob.enRouteToPickupAt = now;
        break;
      case "PICKED_UP":
        updatedJob.pickedUpAt = now;
        break;
      case "EN_ROUTE_TO_DELIVERY":
        updatedJob.enRouteToDeliveryAt = now;
        break;
      case "DELIVERED":
        updatedJob.deliveredAt = now;
        break;
      case "COMPLETED":
        updatedJob.completedAt = now;
        // Move to completed jobs
        setCompletedJobs(prev => [updatedJob, ...prev]);
        // Remove from active jobs
        setActiveJobs(prev => prev.filter(job => job.id !== jobId));
        toast.success("Job marked as completed!", {
          description: "The job has been moved to your completed jobs list.",
        });
        return;
    }

    // Update the active jobs array
    const updatedJobs = [...activeJobs];
    updatedJobs[jobIndex] = updatedJob;
    setActiveJobs(updatedJobs);

    toast.success(`Job status updated to ${newStatus.replace(/_/g, " ").toLowerCase()}!`);
  };

  const handlePhotoUpload = () => {
    if (!photoFile) {
      toast.error("Please select a photo to upload");
      return;
    }

    setUploadingPhoto(true);

    // Simulate API call to upload photo
    setTimeout(() => {
      setUploadingPhoto(false);
      setPhotoDialogOpen(false);
      setPhotoFile(null);

      toast.success("Photo uploaded successfully!", {
        description: `${photoType === "pickup" ? "Pickup" : "Delivery"} photo has been uploaded and attached to the job.`,
      });
    }, 1500);
  };

  const getStatusBadge = (status) => {
    switch(status) {
      case "ASSIGNED":
        return <Badge variant="outline">Assigned</Badge>;
      case "EN_ROUTE_TO_PICKUP":
        return <Badge variant="secondary">En Route to Pickup</Badge>;
      case "PICKED_UP":
        return <Badge variant="secondary">Picked Up</Badge>;
      case "EN_ROUTE_TO_DELIVERY":
        return <Badge variant="secondary">En Route to Delivery</Badge>;
      case "DELIVERED":
        return <Badge>Delivered</Badge>;
      case "COMPLETED":
        return <Badge className="bg-green-600">Completed</Badge>;
      default:
        return null;
    }
  };

  const getNextActionButton = (job) => {
    switch(job.status) {
      case "ASSIGNED":
        return (
          <Button
            onClick={() => handleStatusUpdate(job.id, "EN_ROUTE_TO_PICKUP")}
            className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
          >
            Mark as En Route to Pickup
          </Button>
        );
      case "EN_ROUTE_TO_PICKUP":
        return (
          <div className="flex w-full gap-2">
            <GradientButton
              onClick={() => {
                setSelectedJob(job);
                setPhotoType("pickup");
                setPhotoDialogOpen(true);
              }}
              variant="variant"
              className="flex-1"
            >
              <Camera className="mr-2 h-4 w-4" />
              Upload Pickup Photo
            </GradientButton>
            <GradientButton
              onClick={() => handleStatusUpdate(job.id, "PICKED_UP")}
              className="flex-1"
            >
              Mark as Picked Up
            </GradientButton>
          </div>
        );
      case "PICKED_UP":
        return (
          <GradientButton
            onClick={() => handleStatusUpdate(job.id, "EN_ROUTE_TO_DELIVERY")}
            className="w-full"
          >
            Mark as En Route to Delivery
          </GradientButton>
        );
      case "EN_ROUTE_TO_DELIVERY":
        return (
          <div className="flex w-full gap-2">
            <GradientButton
              onClick={() => {
                setSelectedJob(job);
                setPhotoType("delivery");
                setPhotoDialogOpen(true);
              }}
              variant="variant"
              className="flex-1"
            >
              <Camera className="mr-2 h-4 w-4" />
              Upload Delivery Photo
            </GradientButton>
            <GradientButton
              onClick={() => handleStatusUpdate(job.id, "DELIVERED")}
              className="flex-1"
            >
              Mark as Delivered
            </GradientButton>
          </div>
        );
      case "DELIVERED":
        return (
          <GradientButton
            onClick={() => handleStatusUpdate(job.id, "COMPLETED")}
            className="w-full"
          >
            <CheckCircle2 className="mr-2 h-4 w-4" />
            Mark as Completed
          </GradientButton>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">My Jobs</h1>
      </div>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active">Active Jobs</TabsTrigger>
          <TabsTrigger value="completed">Completed Jobs</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="mt-6 space-y-4">
          {loading ? (
            // Loading skeletons
            Array(2).fill(0).map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))
          ) : activeJobs.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-lg font-medium">No active jobs</p>
                <p className="text-muted-foreground">You don't have any active jobs at the moment.</p>
                <Button
                  asChild
                  className="mt-4"
                >
                  <Link href="/driver/jobs">Find New Jobs</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            // Active job listings
            activeJobs.map((job) => (
              <Card key={job.id} className="overflow-hidden transition-all hover:shadow-md">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{job.title}</CardTitle>
                      <CardDescription>
                        {job.vehicleMake} {job.vehicleModel} • {job.distance} km
                      </CardDescription>
                    </div>
                    {getStatusBadge(job.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Pickup:</span> {job.pickupLocation}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-blue-500" />
                        <span>{new Date(job.pickupDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-green-500" />
                        <span className="font-medium">Delivery:</span> {job.deliveryLocation}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-green-500" />
                        <span>{new Date(job.deliveryDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-amber-500" />
                      <span className="font-medium">Payment:</span>
                      <span className="text-lg font-bold">€{job.paymentAmount}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Client: {job.clientCompany}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-gray-50">
                  {getNextActionButton(job)}
                </CardFooter>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-6 space-y-4">
          {loading ? (
            // Loading skeletons
            Array(2).fill(0).map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))
          ) : completedJobs.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-lg font-medium">No completed jobs</p>
                <p className="text-muted-foreground">You haven't completed any jobs yet.</p>
              </CardContent>
            </Card>
          ) : (
            // Completed job listings
            completedJobs.map((job) => (
              <Card key={job.id} className="overflow-hidden transition-all hover:shadow-md">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{job.title}</CardTitle>
                      <CardDescription>
                        {job.vehicleMake} {job.vehicleModel} • {job.distance} km
                      </CardDescription>
                    </div>
                    {getStatusBadge(job.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Pickup:</span> {job.pickupLocation}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-green-500" />
                        <span className="font-medium">Delivery:</span> {job.deliveryLocation}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-green-500" />
                        <span>Completed: {new Date(job.completedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-amber-500" />
                      <span className="font-medium">Payment:</span>
                      <span className="text-lg font-bold">€{job.paymentAmount}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Client: {job.clientCompany}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Photo Upload Dialog */}
      <Dialog open={photoDialogOpen} onOpenChange={setPhotoDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload {photoType === "pickup" ? "Pickup" : "Delivery"} Photo</DialogTitle>
            <DialogDescription>
              Take a clear photo of the vehicle at {photoType === "pickup" ? "pickup" : "delivery"} for documentation.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="photo">Photo</Label>
              <div className="grid w-full items-center gap-1.5">
                <Input
                  id="photo"
                  type="file"
                  accept="image/*"
                  onChange={(e) => setPhotoFile(e.target.files[0])}
                />
                {photoFile && (
                  <p className="text-sm text-muted-foreground">
                    Selected: {photoFile.name} ({Math.round(photoFile.size / 1024)} KB)
                  </p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPhotoDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handlePhotoUpload}
              disabled={uploadingPhoto || !photoFile}
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
            >
              {uploadingPhoto ? "Uploading..." : "Upload Photo"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
