"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Calendar, TrendingUp, Truck, Clock, ArrowRight, Filter, SortAsc } from "lucide-react";

// Mock data for available jobs
const mockJobs = [
  {
    id: "job1",
    title: "Transport BMW X5 from Berlin to Munich",
    vehicleType: "SUV",
    pickupLocation: "Berlin",
    pickupDate: "2025-05-25",
    deliveryLocation: "Munich",
    deliveryDate: "2025-05-26",
    distance: 504,
    estimatedPay: 350,
    urgency: "STANDARD",
    postedAt: "2025-05-22T10:30:00Z",
  },
  {
    id: "job2",
    title: "Deliver Mercedes E-Class to Hamburg",
    vehicleType: "SEDAN",
    pickupLocation: "Frankfurt",
    pickupDate: "2025-05-26",
    deliveryLocation: "Hamburg",
    deliveryDate: "2025-05-27",
    distance: 393,
    estimatedPay: 280,
    urgency: "URGENT",
    postedAt: "2025-05-22T14:15:00Z",
  },
  {
    id: "job3",
    title: "Transport Audi Q7 from Munich to Berlin",
    vehicleType: "SUV",
    pickupLocation: "Munich",
    pickupDate: "2025-05-27",
    deliveryLocation: "Berlin",
    deliveryDate: "2025-05-28",
    distance: 504,
    estimatedPay: 360,
    urgency: "STANDARD",
    postedAt: "2025-05-23T09:45:00Z",
  },
  {
    id: "job4",
    title: "Urgent: Porsche 911 to Stuttgart",
    vehicleType: "SPORTS",
    pickupLocation: "Cologne",
    pickupDate: "2025-05-24",
    deliveryLocation: "Stuttgart",
    deliveryDate: "2025-05-24",
    distance: 288,
    estimatedPay: 450,
    urgency: "EMERGENCY",
    postedAt: "2025-05-23T16:20:00Z",
  },
  {
    id: "job5",
    title: "VW Transporter to Leipzig",
    vehicleType: "VAN",
    pickupLocation: "Dresden",
    pickupDate: "2025-05-28",
    deliveryLocation: "Leipzig",
    deliveryDate: "2025-05-28",
    distance: 119,
    estimatedPay: 180,
    urgency: "STANDARD",
    postedAt: "2025-05-23T11:10:00Z",
  },
];

export default function AvailableJobs() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(true);
  const [jobs, setJobs] = useState([]);
  const [filteredJobs, setFilteredJobs] = useState([]);
  
  // Filter states
  const [locationFilter, setLocationFilter] = useState("");
  const [vehicleTypeFilter, setVehicleTypeFilter] = useState("");
  const [distanceRange, setDistanceRange] = useState([0, 1000]);
  const [payRange, setPayRange] = useState([0, 500]);
  const [urgencyFilter, setUrgencyFilter] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  useEffect(() => {
    // Simulate API call to fetch jobs
    const timer = setTimeout(() => {
      setJobs(mockJobs);
      setFilteredJobs(mockJobs);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Apply filters
    let result = [...jobs];
    
    if (locationFilter) {
      result = result.filter(job => 
        job.pickupLocation.toLowerCase().includes(locationFilter.toLowerCase()) || 
        job.deliveryLocation.toLowerCase().includes(locationFilter.toLowerCase())
      );
    }
    
    if (vehicleTypeFilter) {
      result = result.filter(job => job.vehicleType === vehicleTypeFilter);
    }
    
    if (urgencyFilter) {
      result = result.filter(job => job.urgency === urgencyFilter);
    }
    
    // Apply distance range filter
    result = result.filter(job => 
      job.distance >= distanceRange[0] && job.distance <= distanceRange[1]
    );
    
    // Apply pay range filter
    result = result.filter(job => 
      job.estimatedPay >= payRange[0] && job.estimatedPay <= payRange[1]
    );
    
    // Apply sorting
    if (sortBy === "newest") {
      result.sort((a, b) => new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime());
    } else if (sortBy === "highest-pay") {
      result.sort((a, b) => b.estimatedPay - a.estimatedPay);
    } else if (sortBy === "closest") {
      result.sort((a, b) => a.distance - b.distance);
    }
    
    setFilteredJobs(result);
  }, [jobs, locationFilter, vehicleTypeFilter, distanceRange, payRange, urgencyFilter, sortBy]);

  const getUrgencyBadge = (urgency) => {
    switch(urgency) {
      case "STANDARD":
        return <Badge variant="outline">Standard</Badge>;
      case "URGENT":
        return <Badge variant="secondary">Urgent</Badge>;
      case "EMERGENCY":
        return <Badge variant="destructive">Emergency</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Available Jobs</h1>
        <div className="flex items-center gap-2">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="highest-pay">Highest Pay</SelectItem>
              <SelectItem value="closest">Closest Distance</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        {/* Filters */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="City or region"
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vehicleType">Vehicle Type</Label>
              <Select value={vehicleTypeFilter} onValueChange={setVehicleTypeFilter}>
                <SelectTrigger id="vehicleType">
                  <SelectValue placeholder="All vehicle types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  <SelectItem value="SEDAN">Sedan</SelectItem>
                  <SelectItem value="SUV">SUV</SelectItem>
                  <SelectItem value="VAN">Van</SelectItem>
                  <SelectItem value="TRUCK">Truck</SelectItem>
                  <SelectItem value="LUXURY">Luxury</SelectItem>
                  <SelectItem value="SPORTS">Sports</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Distance Range (km)</Label>
              <div className="pt-6">
                <Slider
                  defaultValue={[0, 1000]}
                  max={1000}
                  step={10}
                  value={distanceRange}
                  onValueChange={setDistanceRange}
                />
                <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                  <span>{distanceRange[0]} km</span>
                  <span>{distanceRange[1]} km</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Pay Range (€)</Label>
              <div className="pt-6">
                <Slider
                  defaultValue={[0, 500]}
                  max={500}
                  step={10}
                  value={payRange}
                  onValueChange={setPayRange}
                />
                <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                  <span>€{payRange[0]}</span>
                  <span>€{payRange[1]}</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="urgency">Urgency</Label>
              <Select value={urgencyFilter} onValueChange={setUrgencyFilter}>
                <SelectTrigger id="urgency">
                  <SelectValue placeholder="Any urgency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  <SelectItem value="STANDARD">Standard</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                  <SelectItem value="EMERGENCY">Emergency</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => {
                setLocationFilter("");
                setVehicleTypeFilter("");
                setDistanceRange([0, 1000]);
                setPayRange([0, 500]);
                setUrgencyFilter("");
              }}
            >
              Reset Filters
            </Button>
          </CardContent>
        </Card>

        {/* Job Listings */}
        <div className="md:col-span-3 space-y-4">
          {/* Job Volume Indicator */}
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Job Volume</h3>
                  <p className="text-sm text-muted-foreground">
                    {filteredJobs.length} jobs match your criteria
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">High demand for SUV transfers</p>
                  <p className="text-xs text-muted-foreground">3 urgent jobs available</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {loading ? (
            // Loading skeletons
            Array(3).fill(0).map((_, i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))
          ) : filteredJobs.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-lg font-medium">No jobs match your filters</p>
                <p className="text-muted-foreground">Try adjusting your filter criteria</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => {
                    setLocationFilter("");
                    setVehicleTypeFilter("");
                    setDistanceRange([0, 1000]);
                    setPayRange([0, 500]);
                    setUrgencyFilter("");
                  }}
                >
                  Reset Filters
                </Button>
              </CardContent>
            </Card>
          ) : (
            // Job listings
            filteredJobs.map((job) => (
              <Card key={job.id} className="overflow-hidden transition-all hover:shadow-md">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{job.title}</CardTitle>
                      <CardDescription>
                        {job.vehicleType} • {job.distance} km • Posted {new Date(job.postedAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    {getUrgencyBadge(job.urgency)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Pickup:</span> {job.pickupLocation}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-blue-500" />
                        <span>{new Date(job.pickupDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="h-4 w-4 text-green-500" />
                        <span className="font-medium">Delivery:</span> {job.deliveryLocation}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-green-500" />
                        <span>{new Date(job.deliveryDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-amber-500" />
                      <span className="font-medium">Estimated Pay:</span>
                      <span className="text-lg font-bold">€{job.estimatedPay}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-gray-50">
                  <Button asChild className="w-full">
                    <Link href={`/driver/jobs/${job.id}`}>
                      View Details & Bid
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
