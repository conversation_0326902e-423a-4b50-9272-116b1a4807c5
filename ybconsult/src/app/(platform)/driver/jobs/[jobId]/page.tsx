"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { MapPin, Calendar, TrendingUp, Truck, Clock, ArrowRight, Info, AlertCircle, CheckCircle2 } from "lucide-react";

// Mock job data (in a real app, this would come from an API)
const mockJobs = {
  job1: {
    id: "job1",
    title: "Transport BMW X5 from Berlin to Munich",
    description: "We need a reliable driver to transport a BMW X5 (2023) from our Berlin dealership to our Munich location. The vehicle is in excellent condition and needs to be delivered safely and on time.",
    vehicleType: "SUV",
    vehicleMake: "BMW",
    vehicleModel: "X5",
    vehicleYear: 2023,
    pickupLocation: "Berlin",
    pickupAddress: "BMW Niederlassung Berlin, Kaiserdamm 90, 14057 Berlin",
    pickupDate: "2025-05-25",
    pickupTimeWindow: "9:00-12:00",
    deliveryLocation: "Munich",
    deliveryAddress: "BMW Welt, Am Olympiapark 1, 80809 München",
    deliveryDate: "2025-05-26",
    deliveryTimeWindow: "13:00-17:00",
    distance: 504,
    estimatedDuration: 300, // minutes
    estimatedPay: 350,
    currency: "EUR",
    urgency: "STANDARD",
    specialInstructions: "Please contact the service manager upon arrival at both locations. Vehicle keys will be provided at pickup.",
    postedAt: "2025-05-22T10:30:00Z",
    clientCompany: "BMW Group",
  },
  job2: {
    id: "job2",
    title: "Deliver Mercedes E-Class to Hamburg",
    description: "Transport a Mercedes E-Class from Frankfurt to Hamburg. The vehicle is a company car that needs to be delivered to our Hamburg office.",
    vehicleType: "SEDAN",
    vehicleMake: "Mercedes",
    vehicleModel: "E-Class",
    vehicleYear: 2024,
    pickupLocation: "Frankfurt",
    pickupAddress: "Mercedes-Benz Niederlassung Frankfurt, Hanauer Landstraße 222, 60314 Frankfurt",
    pickupDate: "2025-05-26",
    pickupTimeWindow: "10:00-14:00",
    deliveryLocation: "Hamburg",
    deliveryAddress: "Mercedes-Benz Niederlassung Hamburg, Süderstraße 282, 20537 Hamburg",
    deliveryDate: "2025-05-27",
    deliveryTimeWindow: "9:00-17:00",
    distance: 393,
    estimatedDuration: 240, // minutes
    estimatedPay: 280,
    currency: "EUR",
    urgency: "URGENT",
    specialInstructions: "Vehicle has a full tank of fuel. Please ensure it is delivered with at least half a tank.",
    postedAt: "2025-05-22T14:15:00Z",
    clientCompany: "Daimler AG",
  },
  // Add more mock jobs as needed
};

export default function JobDetail({ params }: { params: { jobId: string } }) {
  const { jobId } = params;
  const router = useRouter();
  const { data: session } = useSession();
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bidAmount, setBidAmount] = useState("");
  const [bidMessage, setBidMessage] = useState("");
  const [bidDialogOpen, setBidDialogOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [bidSuccess, setBidSuccess] = useState(false);

  useEffect(() => {
    // Simulate API call to fetch job details
    const timer = setTimeout(() => {
      const jobData = mockJobs[jobId];
      if (jobData) {
        setJob(jobData);
      }
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [jobId]);

  const handleBidSubmit = async () => {
    setSubmitting(true);

    // Simulate API call to submit bid
    setTimeout(() => {
      setSubmitting(false);
      setBidSuccess(true);
      setBidDialogOpen(false);
      toast.success("Your bid has been submitted successfully!", {
        description: "You will be notified when the client responds to your bid.",
      });
    }, 1500);
  };

  const getUrgencyBadge = (urgency) => {
    switch(urgency) {
      case "STANDARD":
        return <Badge variant="outline">Standard</Badge>;
      case "URGENT":
        return <Badge variant="secondary">Urgent</Badge>;
      case "EMERGENCY":
        return <Badge variant="destructive">Emergency</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-1/2" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-24 w-full" />
            <div className="grid gap-6 md:grid-cols-2">
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="flex h-[60vh] flex-col items-center justify-center space-y-4">
        <AlertCircle className="h-16 w-16 text-red-500" />
        <h1 className="text-2xl font-bold">Job Not Found</h1>
        <p className="text-muted-foreground">The job you're looking for doesn't exist or has been removed.</p>
        <Button onClick={() => router.push("/driver/jobs")}>
          Back to Available Jobs
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Job Details</h1>
        <Button onClick={() => router.push("/driver/jobs")} variant="outline">
          Back to Jobs
        </Button>
      </div>

      <Card className="overflow-hidden">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">{job.title}</CardTitle>
              <CardDescription>
                {job.vehicleType} • {job.distance} km • Posted {new Date(job.postedAt).toLocaleDateString()}
              </CardDescription>
            </div>
            {getUrgencyBadge(job.urgency)}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Job Description */}
          <div>
            <h3 className="mb-2 font-semibold">Description</h3>
            <p>{job.description}</p>
          </div>

          <Separator />

          {/* Vehicle Details */}
          <div>
            <h3 className="mb-2 font-semibold">Vehicle Details</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Type:</span> {job.vehicleType}
              </div>
              <div className="flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Make/Model:</span> {job.vehicleMake} {job.vehicleModel}
              </div>
              {job.vehicleYear && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  <span className="font-medium">Year:</span> {job.vehicleYear}
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Pickup & Delivery */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Pickup */}
            <Card>
              <CardHeader className="bg-blue-50 pb-2">
                <CardTitle className="text-lg">Pickup Details</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <MapPin className="mt-0.5 h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium">{job.pickupLocation}</p>
                      <p className="text-sm text-muted-foreground">{job.pickupAddress}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium">{new Date(job.pickupDate).toLocaleDateString()}</p>
                      {job.pickupTimeWindow && (
                        <p className="text-sm text-muted-foreground">Between {job.pickupTimeWindow}</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Delivery */}
            <Card>
              <CardHeader className="bg-green-50 pb-2">
                <CardTitle className="text-lg">Delivery Details</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <MapPin className="mt-0.5 h-4 w-4 text-green-500" />
                    <div>
                      <p className="font-medium">{job.deliveryLocation}</p>
                      <p className="text-sm text-muted-foreground">{job.deliveryAddress}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="font-medium">{new Date(job.deliveryDate).toLocaleDateString()}</p>
                      {job.deliveryTimeWindow && (
                        <p className="text-sm text-muted-foreground">Between {job.deliveryTimeWindow}</p>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Details */}
          <div className="grid gap-6 md:grid-cols-3">
            <div>
              <h3 className="mb-2 font-semibold">Distance</h3>
              <p className="text-2xl font-bold">{job.distance} km</p>
            </div>
            <div>
              <h3 className="mb-2 font-semibold">Estimated Duration</h3>
              <p className="text-2xl font-bold">{Math.floor(job.estimatedDuration / 60)}h {job.estimatedDuration % 60}m</p>
            </div>
            <div>
              <h3 className="mb-2 font-semibold">Estimated Pay</h3>
              <p className="text-2xl font-bold">€{job.estimatedPay}</p>
            </div>
          </div>

          {/* Special Instructions */}
          {job.specialInstructions && (
            <>
              <Separator />
              <div>
                <h3 className="mb-2 font-semibold">Special Instructions</h3>
                <p>{job.specialInstructions}</p>
              </div>
            </>
          )}

          {/* Client Info */}
          <Separator />
          <div>
            <h3 className="mb-2 font-semibold">Client</h3>
            <p>{job.clientCompany}</p>
          </div>
        </CardContent>
        <CardFooter className="bg-gray-50 flex justify-between">
          <GradientButton onClick={() => router.push("/driver/jobs")} variant="variant">
            Back to Jobs
          </GradientButton>

          {bidSuccess ? (
            <GradientButton disabled className="bg-green-600 hover:bg-green-700">
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Bid Submitted
            </GradientButton>
          ) : (
            <Dialog open={bidDialogOpen} onOpenChange={setBidDialogOpen}>
              <DialogTrigger asChild>
                <GradientButton>
                  Bid on This Job
                </GradientButton>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Submit Your Bid</DialogTitle>
                  <DialogDescription>
                    Express your interest in this job. You can optionally include a bid amount and a message to the client.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="bid-amount">Bid Amount (€) (Optional)</Label>
                    <Input
                      id="bid-amount"
                      type="number"
                      placeholder="Enter your bid amount"
                      value={bidAmount}
                      onChange={(e) => setBidAmount(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      Leave empty to express interest without a specific bid amount.
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bid-message">Message (Optional)</Label>
                    <Textarea
                      id="bid-message"
                      placeholder="Add a message to the client explaining why you're a good fit for this job"
                      value={bidMessage}
                      onChange={(e) => setBidMessage(e.target.value)}
                      rows={4}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setBidDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleBidSubmit}
                    disabled={submitting}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                  >
                    {submitting ? "Submitting..." : "Submit Bid"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
