import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { DriverNavigation } from "@/components/navigation/driver-navigation";

export default async function DriverLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);

  // Redirect if not logged in or not a driver
  if (!session) {
    redirect("/login?callbackUrl=/driver/dashboard");
  }

  if (session.user.role !== "DRIVER") {
    redirect("/");
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <DriverNavigation />
      <main className="container mx-auto px-4 py-8">{children}</main>
    </div>
  );
}
