"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { GradientButton } from "@/components/ui/gradient-button";
import { Skeleton } from "@/components/ui/skeleton";
import { MapPin, Calendar, TrendingUp, Truck } from "lucide-react";

interface DashboardData {
  availableJobs: number;
  activeAssignments: number;
  recentEarnings: number;
  nearbyJobs: {
    count: number;
    locations: string[];
  };
}

export default function DriverDashboard() {
  const { data: session } = useSession();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real implementation, this would be an API call
    // For now, we'll simulate loading data
    const timer = setTimeout(() => {
      setDashboardData({
        availableJobs: 12,
        activeAssignments: 2,
        recentEarnings: 1250.75,
        nearbyJobs: {
          count: 5,
          locations: ["Berlin", "Hamburg", "Munich"],
        },
      });
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Driver Dashboard</h1>
        <div className="flex items-center gap-2">
          <GradientButton variant="variant" asChild>
            <Link href="/driver/profile">View Profile</Link>
          </GradientButton>
          <GradientButton asChild>
            <Link href="/driver/jobs">Find Jobs</Link>
          </GradientButton>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Available Jobs Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Available Jobs
            </CardTitle>
            <CardDescription className="text-blue-100">
              Jobs matching your profile
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">{dashboardData?.availableJobs}</p>
                <p className="text-sm text-muted-foreground">
                  {dashboardData?.nearbyJobs.count} nearby in{" "}
                  {dashboardData?.nearbyJobs.locations.join(", ")}
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/driver/jobs">Browse Jobs</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Active Assignments Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Active Assignments
            </CardTitle>
            <CardDescription className="text-green-100">
              Your current jobs
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">{dashboardData?.activeAssignments}</p>
                <p className="text-sm text-muted-foreground">
                  {dashboardData?.activeAssignments > 0
                    ? "In progress"
                    : "No active assignments"}
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/driver/my-jobs/active">View My Jobs</Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Earnings Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-amber-500 to-orange-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Earnings
            </CardTitle>
            <CardDescription className="text-amber-100">
              Last 30 days
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">
                  €{dashboardData?.recentEarnings.toFixed(2)}
                </p>
                <p className="text-sm text-muted-foreground">
                  From completed assignments
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/driver/earnings">View Earnings</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and actions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button asChild variant="outline" className="h-auto flex-col items-start gap-1 p-4">
              <Link href="/driver/jobs">
                <div className="flex w-full flex-row items-center justify-between">
                  <span>Find New Jobs</span>
                  <Truck className="h-5 w-5" />
                </div>
                <p className="text-xs text-muted-foreground">
                  Browse available jobs matching your profile
                </p>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto flex-col items-start gap-1 p-4">
              <Link href="/driver/my-jobs/active">
                <div className="flex w-full flex-row items-center justify-between">
                  <span>Manage Active Jobs</span>
                  <Calendar className="h-5 w-5" />
                </div>
                <p className="text-xs text-muted-foreground">
                  Update status and manage your current assignments
                </p>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto flex-col items-start gap-1 p-4">
              <Link href="/driver/earnings">
                <div className="flex w-full flex-row items-center justify-between">
                  <span>View Earnings</span>
                  <TrendingUp className="h-5 w-5" />
                </div>
                <p className="text-xs text-muted-foreground">
                  Check your payment history and earnings
                </p>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto flex-col items-start gap-1 p-4">
              <Link href="/driver/profile">
                <div className="flex w-full flex-row items-center justify-between">
                  <span>Update Profile</span>
                  <MapPin className="h-5 w-5" />
                </div>
                <p className="text-xs text-muted-foreground">
                  Update your profile and preferences
                </p>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
