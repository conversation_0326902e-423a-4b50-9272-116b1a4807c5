"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Calendar, TrendingUp, DollarSign, CreditCard, Clock, CheckCircle2, AlertCircle } from "lucide-react";

// Mock data for earnings summary
const mockEarningsSummary = {
  totalEarnings: 3250.75,
  pendingPayments: 630.00,
  paidAmount: 2620.75,
  currentMonth: 1250.75,
  previousMonth: 2000.00,
};

// Mock data for payment history
const mockPaymentHistory = [
  {
    id: "payment1",
    jobId: "job3",
    jobTitle: "Transport Audi A4 from Hamburg to Berlin",
    amount: 240.00,
    status: "PAID",
    processedAt: "2025-05-21T14:30:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment2",
    jobId: "job4",
    jobTitle: "Deliver Porsche Cayenne to Munich",
    amount: 320.00,
    status: "PAID",
    processedAt: "2025-05-19T10:15:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment3",
    jobId: "job5",
    jobTitle: "Transport VW Golf from Berlin to Dresden",
    amount: 180.00,
    status: "PROCESSING",
    processedAt: null,
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment4",
    jobId: "job6",
    jobTitle: "Deliver Audi Q5 to Frankfurt",
    amount: 290.00,
    status: "PENDING",
    processedAt: null,
    paymentMethod: null,
  },
  {
    id: "payment5",
    jobId: "job7",
    jobTitle: "Transport BMW 3 Series from Munich to Stuttgart",
    amount: 210.00,
    status: "PAID",
    processedAt: "2025-05-15T16:45:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment6",
    jobId: "job8",
    jobTitle: "Deliver Mercedes GLC to Hamburg",
    amount: 350.00,
    status: "PAID",
    processedAt: "2025-05-10T09:20:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment7",
    jobId: "job9",
    jobTitle: "Transport Porsche Macan from Frankfurt to Berlin",
    amount: 380.00,
    status: "PAID",
    processedAt: "2025-05-05T11:30:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment8",
    jobId: "job10",
    jobTitle: "Deliver Audi A6 to Munich",
    amount: 270.00,
    status: "PAID",
    processedAt: "2025-04-28T14:10:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment9",
    jobId: "job11",
    jobTitle: "Transport BMW X3 from Hamburg to Dresden",
    amount: 310.00,
    status: "PAID",
    processedAt: "2025-04-22T10:45:00Z",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "payment10",
    jobId: "job12",
    jobTitle: "Deliver VW Tiguan to Berlin",
    amount: 240.00,
    status: "PAID",
    processedAt: "2025-04-18T15:30:00Z",
    paymentMethod: "Bank Transfer",
  },
];

export default function DriverEarnings() {
  const { data: session } = useSession();
  const [earningsSummary, setEarningsSummary] = useState(null);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");

  useEffect(() => {
    // Simulate API call to fetch earnings data
    const timer = setTimeout(() => {
      setEarningsSummary(mockEarningsSummary);
      setPaymentHistory(mockPaymentHistory);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const filteredPayments = () => {
    if (activeTab === "all") {
      return paymentHistory;
    } else if (activeTab === "pending") {
      return paymentHistory.filter(payment => payment.status === "PENDING" || payment.status === "PROCESSING");
    } else if (activeTab === "paid") {
      return paymentHistory.filter(payment => payment.status === "PAID");
    }
    return paymentHistory;
  };

  const getStatusBadge = (status) => {
    switch(status) {
      case "PAID":
        return <Badge className="bg-green-600">Paid</Badge>;
      case "PROCESSING":
        return <Badge variant="secondary">Processing</Badge>;
      case "PENDING":
        return <Badge variant="outline">Pending</Badge>;
      case "FAILED":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Earnings & Payments</h1>
      </div>

      {/* Earnings Summary Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Earnings Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Total Earnings
            </CardTitle>
            <CardDescription className="text-blue-100">
              All time
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">€{earningsSummary?.totalEarnings.toFixed(2)}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Payments Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-amber-500 to-orange-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Pending Payments
            </CardTitle>
            <CardDescription className="text-amber-100">
              To be processed
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">€{earningsSummary?.pendingPayments.toFixed(2)}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Paid Amount Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5" />
              Paid Amount
            </CardTitle>
            <CardDescription className="text-green-100">
              Already received
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">€{earningsSummary?.paidAmount.toFixed(2)}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Current Month Card */}
        <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg">
          <CardHeader className="bg-gradient-to-r from-purple-500 to-violet-600 text-white">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Current Month
            </CardTitle>
            <CardDescription className="text-purple-100">
              {new Date().toLocaleString('default', { month: 'long' })} {new Date().getFullYear()}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            {loading ? (
              <Skeleton className="h-16 w-full" />
            ) : (
              <div className="text-center">
                <p className="text-4xl font-bold">€{earningsSummary?.currentMonth.toFixed(2)}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
          <CardDescription>
            Detailed breakdown of your payments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" onValueChange={setActiveTab}>
            <TabsList className="mb-4 grid w-full grid-cols-3">
              <TabsTrigger value="all">All Payments</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="paid">Paid</TabsTrigger>
            </TabsList>
            
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : filteredPayments().length === 0 ? (
              <div className="py-6 text-center">
                <p className="text-lg font-medium">No payments found</p>
                <p className="text-muted-foreground">There are no payments in this category.</p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Job</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Payment Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPayments().map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">{payment.jobTitle}</TableCell>
                        <TableCell>€{payment.amount.toFixed(2)}</TableCell>
                        <TableCell>{getStatusBadge(payment.status)}</TableCell>
                        <TableCell>
                          {payment.processedAt 
                            ? new Date(payment.processedAt).toLocaleDateString() 
                            : payment.status === "PROCESSING" 
                              ? "Processing..." 
                              : "Pending"}
                        </TableCell>
                        <TableCell>{payment.paymentMethod || "—"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </Tabs>
        </CardContent>
      </Card>

      {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
          <CardDescription>
            Your payment details and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h3 className="font-medium">Payment Method</h3>
                <p className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-blue-500" />
                  Bank Transfer
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">Payment Schedule</h3>
                <p className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  Weekly (Processed every Monday)
                </p>
              </div>
            </div>
            <div className="rounded-md bg-blue-50 p-4">
              <p className="flex items-start gap-2 text-sm text-blue-800">
                <AlertCircle className="mt-0.5 h-4 w-4 text-blue-500" />
                <span>
                  Payments are typically processed within 3-5 business days after job completion. 
                  For any payment inquiries, please contact our support team.
                </span>
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="w-full">
            Update Payment Information
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
