"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { Users, Package, LayoutDashboard, Settings, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { data: session } = useSession();
  
  // Überprüfen, ob der Benutzer ein Admin ist
  if (session?.user?.role !== "ADMIN") {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold mb-4"><PERSON>ug<PERSON> verweigert</h1>
        <p className="mb-6"><PERSON>e haben keine Berechtigung, auf diesen Bereich zuzugreifen.</p>
        <Button asChild>
          <Link href="/">Zurück zur Startseite</Link>
        </Button>
      </div>
    );
  }
  
  const navigation = [
    {
      name: "Dashboard",
      href: "/admin",
      icon: LayoutDashboard,
      current: pathname === "/admin",
    },
    {
      name: "Benutzer",
      href: "/admin/users",
      icon: Users,
      current: pathname.startsWith("/admin/users"),
    },
    {
      name: "Aufträge",
      href: "/admin/orders",
      icon: Package,
      current: pathname.startsWith("/admin/orders"),
    },
    {
      name: "Einstellungen",
      href: "/admin/settings",
      icon: Settings,
      current: pathname.startsWith("/admin/settings"),
    },
  ];
  
  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <div className="w-64 bg-card border-r">
        <div className="flex flex-col h-full">
          <div className="p-4">
            <h1 className="text-xl font-bold">Admin-Bereich</h1>
            <p className="text-sm text-muted-foreground">YoungMobility</p>
          </div>
          <Separator />
          <nav className="flex-1 p-4 space-y-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                  item.current
                    ? "bg-primary text-primary-foreground"
                    : "text-foreground hover:bg-muted"
                }`}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </nav>
          <div className="p-4">
            <Separator className="mb-4" />
            <Button asChild variant="outline" className="w-full justify-start">
              <Link href="/auth/signout">
                <LogOut className="mr-3 h-5 w-5" />
                Abmelden
              </Link>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col">
        <main className="flex-1 p-6 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
