"use client";

import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, CheckCircle2, Ban, AlertTriangle } from "lucide-react";
import { GradientButton } from "@/components/ui/gradient-button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface AdminUserVerifyPageProps {
  params: {
    userId: string;
  };
}

export default function AdminUserVerifyPage({ params }: AdminUserVerifyPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = params.userId;
  const action = searchParams.get("action") || "approve";
  const [reason, setReason] = useState("");

  // Benutzerdaten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["admin-user", userId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Benutzerdaten");
      }
      return response.json();
    },
  });

  // Mutation für die Benutzerverifizierung
  const verifyUserMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          reason: action === "reject" ? reason : undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler bei der Benutzerverifizierung");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success(
        action === "approve"
          ? "Benutzer erfolgreich freigegeben"
          : "Benutzer erfolgreich abgelehnt"
      );
      router.push(`/admin/users/${userId}`);
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });

  // Formular absenden
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (action === "reject" && !reason.trim()) {
      toast.error("Bitte geben Sie einen Grund für die Ablehnung an");
      return;
    }

    verifyUserMutation.mutate();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <GradientButton variant="variant" className="w-10 h-10 p-0">
              <ArrowLeft className="h-4 w-4" />
            </GradientButton>
            <Skeleton className="h-6 w-1/3" />
          </div>
          <Skeleton className="h-4 w-1/4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-32 w-full" />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  if (error || !data?.user) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>Benutzerverifizierung</CardTitle>
          </div>
          <CardDescription>Fehler beim Laden der Benutzerdaten</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <Link href={`/admin/users/${userId}`}>Zurück zu Benutzerdetails</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const user = data.user;

  // Überprüfen, ob der Benutzer auf Verifizierung wartet
  if (user.status !== "PENDING_ADMIN_APPROVAL") {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>Benutzerverifizierung</CardTitle>
          </div>
          <CardDescription>Benutzer wartet nicht auf Verifizierung</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-4 bg-yellow-50 text-yellow-800 rounded-md border border-yellow-200">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <p>Dieser Benutzer wartet nicht auf Verifizierung. Der aktuelle Status ist: {user.status}</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <Link href={`/admin/users/${userId}`}>Zurück zu Benutzerdetails</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  const isClient = user.role === "BUSINESS_CLIENT";
  const isDriver = user.role === "DRIVER";
  const userName = isClient && user.clientProfile
    ? `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`
    : isDriver && user.driverProfile
    ? `${user.driverProfile.firstName} ${user.driverProfile.lastName}`
    : user.email;

  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button type="button" variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>
              {action === "approve" ? "Benutzer freigeben" : "Benutzer ablehnen"}
            </CardTitle>
          </div>
          <CardDescription>
            {action === "approve"
              ? `Bestätigen Sie die Freigabe von ${userName}`
              : `Geben Sie einen Grund für die Ablehnung von ${userName} an`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {action === "approve" ? (
            <div className="flex items-center gap-2 p-4 bg-green-50 text-green-800 rounded-md border border-green-200">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <p>
                Sie sind dabei, den Benutzer <strong>{userName}</strong> freizugeben.
                Der Benutzer wird benachrichtigt und kann die Plattform dann vollständig nutzen.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-4 bg-red-50 text-red-800 rounded-md border border-red-200">
                <Ban className="h-5 w-5 text-red-500" />
                <p>
                  Sie sind dabei, den Benutzer <strong>{userName}</strong> abzulehnen.
                  Der Benutzer wird benachrichtigt und kann die Plattform nicht nutzen.
                </p>
              </div>

              <div className="space-y-2">
                <label htmlFor="reason" className="font-medium">
                  Grund für die Ablehnung
                </label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Geben Sie einen Grund für die Ablehnung an..."
                  required={action === "reject"}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <GradientButton variant="variant" asChild>
            <Link href={`/admin/users/${userId}`}>Abbrechen</Link>
          </GradientButton>
          <GradientButton
            type="submit"
            disabled={action === "reject" && !reason.trim() || verifyUserMutation.isPending}
          >
            {verifyUserMutation.isPending
              ? "Wird verarbeitet..."
              : action === "approve"
              ? "Benutzer freigeben"
              : "Benutzer ablehnen"}
          </GradientButton>
        </CardFooter>
      </form>
    </Card>
  );
}
