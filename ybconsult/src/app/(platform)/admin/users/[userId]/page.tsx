"use client";

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { 
  ArrowLeft, 
  CheckCircle2, 
  Clock, 
  AlertTriangle, 
  Ban, 
  Trash2,
  User,
  Building,
  Car,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  Shield
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RatingDisplay } from "@/components/ratings/rating-display";

interface AdminUserDetailPageProps {
  params: {
    userId: string;
  };
}

export default function AdminUserDetailPage({ params }: AdminUserDetailPageProps) {
  const router = useRouter();
  const userId = params.userId;
  
  // Benutzerdaten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["admin-user", userId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Benutzerdaten");
      }
      return response.json();
    },
  });
  
  // Status-Badge rendern
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING_EMAIL_VERIFICATION":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>E-Mail-Verifizierung ausstehend</span>
          </Badge>
        );
      case "PENDING_ADMIN_APPROVAL":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertTriangle className="h-3 w-3" />
            <span>Admin-Freigabe ausstehend</span>
          </Badge>
        );
      case "ACTIVE":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Aktiv</span>
          </Badge>
        );
      case "SUSPENDED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200">
            <Ban className="h-3 w-3" />
            <span>Gesperrt</span>
          </Badge>
        );
      case "DELETED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-gray-50 text-gray-700 border-gray-200">
            <Trash2 className="h-3 w-3" />
            <span>Gelöscht</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  // Rolle formatieren
  const formatRole = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "Administrator";
      case "BUSINESS_CLIENT":
        return "Geschäftskunde";
      case "DRIVER":
        return "Fahrer";
      default:
        return role;
    }
  };
  
  // Dokumenttyp formatieren
  const formatDocumentType = (type: string) => {
    switch (type) {
      case "DRIVING_LICENSE":
        return "Führerschein";
      case "BUSINESS_REGISTRATION":
        return "Gewerbeanmeldung";
      case "INSURANCE":
        return "Versicherung";
      case "ID_CARD":
        return "Personalausweis";
      case "OTHER":
        return "Sonstiges";
      default:
        return type;
    }
  };
  
  // Dokumentstatus formatieren
  const formatDocumentStatus = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Ausstehend</span>
          </Badge>
        );
      case "VERIFIED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Verifiziert</span>
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200">
            <Ban className="h-3 w-3" />
            <span>Abgelehnt</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Skeleton className="h-6 w-1/3" />
          </div>
          <Skeleton className="h-4 w-1/4" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (error || !data?.user) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href="/admin/users">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>Benutzerdetails</CardTitle>
          </div>
          <CardDescription>Fehler beim Laden der Benutzerdaten</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <Link href="/admin/users">Zurück zur Benutzerliste</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  const user = data.user;
  const isClient = user.role === "BUSINESS_CLIENT";
  const isDriver = user.role === "DRIVER";
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/users">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <CardTitle>
            {isClient && user.clientProfile
              ? `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`
              : isDriver && user.driverProfile
              ? `${user.driverProfile.firstName} ${user.driverProfile.lastName}`
              : user.email}
          </CardTitle>
        </div>
        <CardDescription>Benutzerdetails und Verwaltung</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Benutzerübersicht */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">E-Mail:</span>
              <span>{user.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Rolle:</span>
              <span>{formatRole(user.role)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Status:</span>
              {renderStatusBadge(user.status)}
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Registriert am:</span>
              <span>{new Date(user.createdAt).toLocaleDateString("de-DE")}</span>
            </div>
            {user.emailVerified && (
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">E-Mail verifiziert am:</span>
                <span>{new Date(user.emailVerified).toLocaleDateString("de-DE")}</span>
              </div>
            )}
            {user.lastLoginAt && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">Letzter Login:</span>
                <span>{new Date(user.lastLoginAt).toLocaleDateString("de-DE")}</span>
              </div>
            )}
          </div>
          
          <div className="space-y-4">
            {/* Aktionen */}
            <div className="space-y-2">
              <h3 className="font-semibold">Aktionen</h3>
              <div className="flex flex-wrap gap-2">
                {user.status === "PENDING_ADMIN_APPROVAL" && (
                  <>
                    <Button asChild variant="default" className="bg-green-600 hover:bg-green-700">
                      <Link href={`/admin/users/${userId}/verify?action=approve`}>
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                        Freigeben
                      </Link>
                    </Button>
                    <Button asChild variant="destructive">
                      <Link href={`/admin/users/${userId}/verify?action=reject`}>
                        <Ban className="h-4 w-4 mr-2" />
                        Ablehnen
                      </Link>
                    </Button>
                  </>
                )}
                
                {user.status === "ACTIVE" && (
                  <Button asChild variant="destructive">
                    <Link href={`/admin/users/${userId}/status?action=suspend`}>
                      <Ban className="h-4 w-4 mr-2" />
                      Sperren
                    </Link>
                  </Button>
                )}
                
                {user.status === "SUSPENDED" && (
                  <Button asChild variant="default" className="bg-green-600 hover:bg-green-700">
                    <Link href={`/admin/users/${userId}/status?action=activate`}>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Aktivieren
                    </Link>
                  </Button>
                )}
                
                {user.status !== "DELETED" && (
                  <Button asChild variant="destructive">
                    <Link href={`/admin/users/${userId}/status?action=delete`}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Löschen
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <Separator />
        
        {/* Tabs für verschiedene Benutzertypen */}
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">Profil</TabsTrigger>
            <TabsTrigger value="ratings">Bewertungen</TabsTrigger>
            {isDriver && <TabsTrigger value="documents">Dokumente</TabsTrigger>}
          </TabsList>
          
          {/* Profildetails */}
          <TabsContent value="profile" className="space-y-4 pt-4">
            {isClient && user.clientProfile && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Unternehmensprofil
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p><span className="font-medium">Unternehmen:</span> {user.clientProfile.companyName}</p>
                    <p><span className="font-medium">Ansprechpartner:</span> {user.clientProfile.contactPersonName}</p>
                    <p><span className="font-medium">Telefon:</span> {user.clientProfile.phoneNumber || "Nicht angegeben"}</p>
                    <p><span className="font-medium">USt-ID:</span> {user.clientProfile.vatId || "Nicht angegeben"}</p>
                  </div>
                  <div className="space-y-2">
                    <p><span className="font-medium">Adresse:</span> {user.clientProfile.addressLine1}</p>
                    {user.clientProfile.addressLine2 && (
                      <p>{user.clientProfile.addressLine2}</p>
                    )}
                    <p>
                      {user.clientProfile.postalCode} {user.clientProfile.city}
                    </p>
                    <p>{user.clientProfile.country}</p>
                  </div>
                </div>
              </div>
            )}
            
            {isDriver && user.driverProfile && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Fahrerprofil
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <p><span className="font-medium">Name:</span> {user.driverProfile.firstName} {user.driverProfile.lastName}</p>
                    <p><span className="font-medium">Telefon:</span> {user.driverProfile.phoneNumber}</p>
                    {user.driverProfile.dateOfBirth && (
                      <p><span className="font-medium">Geburtsdatum:</span> {new Date(user.driverProfile.dateOfBirth).toLocaleDateString("de-DE")}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <p><span className="font-medium">Adresse:</span> {user.driverProfile.addressLine1}</p>
                    {user.driverProfile.addressLine2 && (
                      <p>{user.driverProfile.addressLine2}</p>
                    )}
                    <p>
                      {user.driverProfile.postalCode} {user.driverProfile.city}
                    </p>
                    <p>{user.driverProfile.country}</p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
          
          {/* Bewertungen */}
          <TabsContent value="ratings" className="pt-4">
            <RatingDisplay userId={userId} />
          </TabsContent>
          
          {/* Dokumente (nur für Fahrer) */}
          {isDriver && (
            <TabsContent value="documents" className="space-y-4 pt-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Dokumente
              </h3>
              
              {user.driverProfile?.documents?.length === 0 ? (
                <p className="text-muted-foreground">Keine Dokumente vorhanden</p>
              ) : (
                <div className="border rounded-md">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="px-4 py-2 text-left">Typ</th>
                        <th className="px-4 py-2 text-left">Status</th>
                        <th className="px-4 py-2 text-left">Hochgeladen am</th>
                        <th className="px-4 py-2 text-right">Aktionen</th>
                      </tr>
                    </thead>
                    <tbody>
                      {user.driverProfile?.documents?.map((doc: any) => (
                        <tr key={doc.id} className="border-b">
                          <td className="px-4 py-2">{formatDocumentType(doc.documentType)}</td>
                          <td className="px-4 py-2">{formatDocumentStatus(doc.verificationStatus)}</td>
                          <td className="px-4 py-2">{new Date(doc.uploadedAt).toLocaleDateString("de-DE")}</td>
                          <td className="px-4 py-2 text-right">
                            <Button asChild size="sm" variant="outline">
                              <Link href={`/admin/users/${userId}/documents/${doc.id}`}>
                                <Eye className="h-4 w-4 mr-1" />
                                Anzeigen
                              </Link>
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild>
          <Link href="/admin/users">Zurück zur Benutzerliste</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
