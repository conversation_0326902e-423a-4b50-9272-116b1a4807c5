"use client";

import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>t, CheckCircle2, Ban, Trash2, <PERSON><PERSON><PERSON><PERSON>gle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface AdminUserStatusPageProps {
  params: {
    userId: string;
  };
}

export default function AdminUserStatusPage({ params }: AdminUserStatusPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userId = params.userId;
  const action = searchParams.get("action") || "activate";
  const [reason, setReason] = useState("");
  
  // Status basierend auf der Aktion bestimmen
  const getStatusFromAction = (action: string) => {
    switch (action) {
      case "activate":
        return "ACTIVE";
      case "suspend":
        return "SUSPENDED";
      case "delete":
        return "DELETED";
      default:
        return "ACTIVE";
    }
  };
  
  const status = getStatusFromAction(action);
  
  // Benutzerdaten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["admin-user", userId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Benutzerdaten");
      }
      return response.json();
    },
  });
  
  // Mutation für die Statusänderung
  const changeStatusMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status,
          reason: action !== "activate" ? reason : undefined,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler bei der Statusänderung");
      }
      
      return response.json();
    },
    onSuccess: () => {
      let successMessage = "";
      switch (action) {
        case "activate":
          successMessage = "Benutzer erfolgreich aktiviert";
          break;
        case "suspend":
          successMessage = "Benutzer erfolgreich gesperrt";
          break;
        case "delete":
          successMessage = "Benutzer erfolgreich gelöscht";
          break;
      }
      
      toast.success(successMessage);
      router.push(`/admin/users/${userId}`);
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });
  
  // Formular absenden
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (action !== "activate" && !reason.trim()) {
      toast.error("Bitte geben Sie einen Grund an");
      return;
    }
    
    changeStatusMutation.mutate();
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2 mb-2">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Skeleton className="h-6 w-1/3" />
          </div>
          <Skeleton className="h-4 w-1/4" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-32 w-full" />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }
  
  if (error || !data?.user) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>Benutzerstatus ändern</CardTitle>
          </div>
          <CardDescription>Fehler beim Laden der Benutzerdaten</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <Link href={`/admin/users/${userId}`}>Zurück zu Benutzerdetails</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  const user = data.user;
  
  // Überprüfen, ob der Benutzer bereits den gewünschten Status hat
  if (user.status === status) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>Benutzerstatus ändern</CardTitle>
          </div>
          <CardDescription>Status bereits gesetzt</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-4 bg-yellow-50 text-yellow-800 rounded-md border border-yellow-200">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <p>Dieser Benutzer hat bereits den Status "{status}".</p>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <Link href={`/admin/users/${userId}`}>Zurück zu Benutzerdetails</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  const isClient = user.role === "BUSINESS_CLIENT";
  const isDriver = user.role === "DRIVER";
  const userName = isClient && user.clientProfile
    ? `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`
    : isDriver && user.driverProfile
    ? `${user.driverProfile.firstName} ${user.driverProfile.lastName}`
    : user.email;
  
  // Titel und Beschreibung basierend auf der Aktion
  let title = "";
  let description = "";
  let icon = null;
  let bgColor = "";
  let buttonColor = "";
  
  switch (action) {
    case "activate":
      title = "Benutzer aktivieren";
      description = `Bestätigen Sie die Aktivierung von ${userName}`;
      icon = <CheckCircle2 className="h-5 w-5 text-green-500" />;
      bgColor = "bg-green-50 text-green-800 border-green-200";
      buttonColor = "bg-green-600 hover:bg-green-700";
      break;
    case "suspend":
      title = "Benutzer sperren";
      description = `Geben Sie einen Grund für die Sperrung von ${userName} an`;
      icon = <Ban className="h-5 w-5 text-red-500" />;
      bgColor = "bg-red-50 text-red-800 border-red-200";
      buttonColor = "";
      break;
    case "delete":
      title = "Benutzer löschen";
      description = `Geben Sie einen Grund für die Löschung von ${userName} an`;
      icon = <Trash2 className="h-5 w-5 text-red-500" />;
      bgColor = "bg-red-50 text-red-800 border-red-200";
      buttonColor = "";
      break;
  }
  
  return (
    <Card>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button type="button" variant="outline" size="icon" asChild>
              <Link href={`/admin/users/${userId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <CardTitle>{title}</CardTitle>
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className={`flex items-center gap-2 p-4 rounded-md border ${bgColor}`}>
              {icon}
              <p>
                Sie sind dabei, den Status des Benutzers <strong>{userName}</strong> auf "{status}" zu ändern.
                Der Benutzer wird benachrichtigt.
              </p>
            </div>
            
            {action !== "activate" && (
              <div className="space-y-2">
                <label htmlFor="reason" className="font-medium">
                  Grund für die {action === "suspend" ? "Sperrung" : "Löschung"}
                </label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder={`Geben Sie einen Grund für die ${action === "suspend" ? "Sperrung" : "Löschung"} an...`}
                  required
                  className="min-h-[100px]"
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" asChild>
            <Link href={`/admin/users/${userId}`}>Abbrechen</Link>
          </Button>
          <Button
            type="submit"
            variant={action === "activate" ? "default" : "destructive"}
            disabled={(action !== "activate" && !reason.trim()) || changeStatusMutation.isPending}
            className={action === "activate" ? buttonColor : ""}
          >
            {changeStatusMutation.isPending
              ? "Wird verarbeitet..."
              : action === "activate"
              ? "Benutzer aktivieren"
              : action === "suspend"
              ? "Benutzer sperren"
              : "Benutzer löschen"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
