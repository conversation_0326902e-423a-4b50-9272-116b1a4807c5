"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Check,
  X,
  Search,
  Filter,
  Eye,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Ban,
  Trash2
} from "lucide-react";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";

export default function AdminUsersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Filter-Parameter aus der URL
  const currentPage = parseInt(searchParams.get("page") || "1");
  const currentRole = searchParams.get("role") || "";
  const currentStatus = searchParams.get("status") || "";
  const currentSearch = searchParams.get("search") || "";

  // Lokale Filter-Zustände
  const [role, setRole] = useState(currentRole);
  const [status, setStatus] = useState(currentStatus);
  const [search, setSearch] = useState(currentSearch);

  // Benutzer abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["admin-users", currentPage, currentRole, currentStatus, currentSearch],
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append("page", currentPage.toString());
      if (currentRole) params.append("role", currentRole);
      if (currentStatus) params.append("status", currentStatus);
      if (currentSearch) params.append("search", currentSearch);

      const response = await fetch(`/api/admin/users?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Benutzer");
      }
      return response.json();
    },
  });

  // Filter anwenden
  const applyFilters = () => {
    const params = new URLSearchParams();
    params.append("page", "1"); // Bei Filteränderung zurück zur ersten Seite
    if (role) params.append("role", role);
    if (status) params.append("status", status);
    if (search) params.append("search", search);

    router.push(`/admin/users?${params.toString()}`);
  };

  // Filter zurücksetzen
  const resetFilters = () => {
    setRole("");
    setStatus("");
    setSearch("");
    router.push("/admin/users");
  };

  // Status-Badge rendern
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING_EMAIL_VERIFICATION":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>E-Mail-Verifizierung ausstehend</span>
          </Badge>
        );
      case "PENDING_ADMIN_APPROVAL":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertTriangle className="h-3 w-3" />
            <span>Admin-Freigabe ausstehend</span>
          </Badge>
        );
      case "ACTIVE":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Aktiv</span>
          </Badge>
        );
      case "SUSPENDED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200">
            <Ban className="h-3 w-3" />
            <span>Gesperrt</span>
          </Badge>
        );
      case "DELETED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-gray-50 text-gray-700 border-gray-200">
            <Trash2 className="h-3 w-3" />
            <span>Gelöscht</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Rolle formatieren
  const formatRole = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "Administrator";
      case "BUSINESS_CLIENT":
        return "Geschäftskunde";
      case "DRIVER":
        return "Fahrer";
      default:
        return role;
    }
  };

  // Benutzernamen formatieren
  const formatUserName = (user: any) => {
    if (user.role === "BUSINESS_CLIENT" && user.clientProfile) {
      return `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`;
    } else if (user.role === "DRIVER" && user.driverProfile) {
      return `${user.driverProfile.firstName} ${user.driverProfile.lastName}`;
    }
    return user.email;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Benutzer</CardTitle>
          <CardDescription>Verwalten Sie alle Benutzer der Plattform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Benutzer</CardTitle>
          <CardDescription>Fehler beim Laden der Benutzer</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.</p>
        </CardContent>
      </Card>
    );
  }

  const users = data?.users || [];
  const pagination = data?.pagination || { page: 1, totalPages: 1, total: 0 };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Benutzer</CardTitle>
        <CardDescription>Verwalten Sie alle Benutzer der Plattform</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Suche nach Name, E-Mail..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Rolle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Alle Rollen</SelectItem>
                <SelectItem value="ADMIN">Administrator</SelectItem>
                <SelectItem value="BUSINESS_CLIENT">Geschäftskunde</SelectItem>
                <SelectItem value="DRIVER">Fahrer</SelectItem>
              </SelectContent>
            </Select>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Alle Status</SelectItem>
                <SelectItem value="PENDING_EMAIL_VERIFICATION">E-Mail-Verifizierung ausstehend</SelectItem>
                <SelectItem value="PENDING_ADMIN_APPROVAL">Admin-Freigabe ausstehend</SelectItem>
                <SelectItem value="ACTIVE">Aktiv</SelectItem>
                <SelectItem value="SUSPENDED">Gesperrt</SelectItem>
                <SelectItem value="DELETED">Gelöscht</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={applyFilters} className="gap-2">
              <Filter className="h-4 w-4" />
              Filtern
            </Button>
            <Button variant="outline" onClick={resetFilters}>
              Zurücksetzen
            </Button>
          </div>
        </div>

        {/* Benutzertabelle */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>E-Mail</TableHead>
                <TableHead>Rolle</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Registriert am</TableHead>
                <TableHead className="text-right">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Keine Benutzer gefunden
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user: any) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{formatUserName(user)}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{formatRole(user.role)}</TableCell>
                    <TableCell>{renderStatusBadge(user.status)}</TableCell>
                    <TableCell>
                      {new Date(user.createdAt).toLocaleDateString("de-DE")}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button asChild size="sm" variant="outline">
                          <Link href={`/admin/users/${user.id}`}>
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Link>
                        </Button>
                        {user.status === "PENDING_ADMIN_APPROVAL" && (
                          <>
                            <Button asChild size="sm" variant="default" className="bg-green-600 hover:bg-green-700">
                              <Link href={`/admin/users/${user.id}/verify?action=approve`}>
                                <Check className="h-4 w-4 mr-1" />
                                Freigeben
                              </Link>
                            </Button>
                            <Button asChild size="sm" variant="destructive">
                              <Link href={`/admin/users/${user.id}/verify?action=reject`}>
                                <X className="h-4 w-4 mr-1" />
                                Ablehnen
                              </Link>
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Paginierung */}
        {pagination.totalPages > 1 && (
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href={`/admin/users?page=${Math.max(1, currentPage - 1)}${currentRole ? `&role=${currentRole}` : ""}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                  aria-disabled={currentPage <= 1}
                  className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>

              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                .filter(page => {
                  // Zeige nur die ersten 2, letzten 2 und aktuellen Seiten +/- 1
                  return (
                    page <= 2 ||
                    page > pagination.totalPages - 2 ||
                    Math.abs(page - currentPage) <= 1
                  );
                })
                .map((page, index, array) => {
                  // Füge Ellipsis hinzu, wenn Seiten übersprungen werden
                  const showEllipsis = index > 0 && array[index - 1] !== page - 1;

                  return (
                    <div key={page} className="flex items-center">
                      {showEllipsis && (
                        <span className="px-2">...</span>
                      )}
                      <PaginationItem>
                        <PaginationLink
                          href={`/admin/users?page=${page}${currentRole ? `&role=${currentRole}` : ""}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    </div>
                  );
                })}

              <PaginationItem>
                <PaginationNext
                  href={`/admin/users?page=${Math.min(pagination.totalPages, currentPage + 1)}${currentRole ? `&role=${currentRole}` : ""}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                  aria-disabled={currentPage >= pagination.totalPages}
                  className={currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </CardContent>
    </Card>
  );
}
