"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import Link from "next/link";
import { ArrowLeft, CheckCircle, MapPin, Star, User, Calendar, Clock, Truck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format } from "date-fns";
import { de } from "date-fns/locale";

// Typen für die API-Antworten
interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  averageRating: number;
  completedTransfers: number;
  profileImageUrl?: string;
  transportationTypes?: string[];
  qualifications?: string[];
  distanceToPickup?: number; // In km, falls verfügbar
}

interface Bid {
  id: string;
  amount: number;
  currency: string;
  message?: string;
  status: string;
  createdAt: string;
  driver: Driver;
}

interface Order {
  id: string;
  orderReference: string;
  title: string;
  status: string;
  vehicleType: string;
  vehicleMake?: string;
  vehicleModel?: string;
  pickupAddress: string;
  pickupCity: string;
  pickupDate: string;
  deliveryAddress: string;
  deliveryCity: string;
  deliveryDate: string;
  estimatedPrice?: number;
  currency: string;
  bids: Bid[];
}

export default function AssignDriverPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const orderId = params.orderId as string;
  const [selectedDriverId, setSelectedDriverId] = useState<string | null>(null);

  // Auftragsdaten abrufen
  const { data: order, isLoading: isLoadingOrder } = useQuery({
    queryKey: ["order", orderId],
    queryFn: async () => {
      const response = await fetch(`/api/admin/orders/${orderId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden des Auftrags");
      }
      return response.json();
    },
  });

  // Mutation für die Fahrerzuweisung
  const assignDriverMutation = useMutation({
    mutationFn: async (driverId: string) => {
      const response = await fetch(`/api/admin/orders/${orderId}/assign`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ driverId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler bei der Fahrerzuweisung");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Fahrer erfolgreich zugewiesen");
      queryClient.invalidateQueries({ queryKey: ["order", orderId] });
      router.push(`/admin/orders/${orderId}`);
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });

  const handleAssignDriver = () => {
    if (!selectedDriverId) {
      toast.error("Bitte wählen Sie einen Fahrer aus");
      return;
    }

    assignDriverMutation.mutate(selectedDriverId);
  };

  // Rendert die Fahrerkarte mit Auswahlmöglichkeit
  const renderDriverCard = (bid: Bid) => {
    const { driver } = bid;
    const isSelected = selectedDriverId === driver.id;

    return (
      <Card 
        key={bid.id} 
        className={`overflow-hidden transition-all ${isSelected ? 'ring-2 ring-blue-500 shadow-md' : 'hover:shadow-md'}`}
        onClick={() => setSelectedDriverId(driver.id)}
      >
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src={driver.profileImageUrl} />
                <AvatarFallback>{`${driver.firstName.charAt(0)}${driver.lastName.charAt(0)}`}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-lg">{driver.firstName} {driver.lastName}</CardTitle>
                <div className="flex items-center mt-1">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  <span>{driver.averageRating.toFixed(1)}</span>
                  <span className="mx-2">•</span>
                  <span>{driver.completedTransfers} abgeschlossene Fahrten</span>
                </div>
              </div>
            </div>
            {isSelected && (
              <CheckCircle className="h-5 w-5 text-blue-500" />
            )}
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid grid-cols-2 gap-3">
            {bid.amount && (
              <div>
                <div className="text-sm text-muted-foreground">Angebot:</div>
                <div className="font-medium">{bid.amount} {bid.currency}</div>
              </div>
            )}
            {driver.distanceToPickup !== undefined && (
              <div>
                <div className="text-sm text-muted-foreground">Entfernung zum Abholort:</div>
                <div className="font-medium">{driver.distanceToPickup} km</div>
              </div>
            )}
            {driver.qualifications && driver.qualifications.length > 0 && (
              <div className="col-span-2">
                <div className="text-sm text-muted-foreground">Qualifikationen:</div>
                <div className="flex flex-wrap gap-1 mt-1">
                  {driver.qualifications.map((qual) => (
                    <Badge key={qual} variant="outline">{qual}</Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
          {bid.message && (
            <div className="mt-3">
              <div className="text-sm text-muted-foreground">Nachricht:</div>
              <div className="text-sm mt-1 italic">"{bid.message}"</div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (isLoadingOrder) {
    return (
      <div className="container py-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück zur Auftragsliste
            </Link>
          </Button>
        </div>
        <div className="space-y-6">
          <Skeleton className="h-12 w-3/4" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container py-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück zur Auftragsliste
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold">Auftrag nicht gefunden</h2>
              <p className="text-muted-foreground mt-2">Der angeforderte Auftrag konnte nicht gefunden werden.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/admin/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Zurück zur Auftragsliste
          </Link>
        </Button>
      </div>

      {/* Auftragsdetails */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Fahrerzuweisung für Auftrag #{order.orderReference}</CardTitle>
          <CardDescription>Wählen Sie einen Fahrer aus der Liste der Interessenten aus</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Auftragsdetails</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <Truck className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{order.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {order.vehicleMake} {order.vehicleModel} • {order.vehicleType}
                    </div>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Abholung: {order.pickupCity}</div>
                    <div className="text-sm text-muted-foreground">{order.pickupAddress}</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Lieferung: {order.deliveryCity}</div>
                    <div className="text-sm text-muted-foreground">{order.deliveryAddress}</div>
                  </div>
                </div>
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Abholtermin</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.pickupDate), 'PPP', { locale: de })}
                    </div>
                  </div>
                </div>
                <div className="flex items-start">
                  <Calendar className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Liefertermin</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.deliveryDate), 'PPP', { locale: de })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2">Preisdetails</h3>
              <div className="space-y-3">
                {order.estimatedPrice && (
                  <div>
                    <div className="text-sm text-muted-foreground">Geschätzter Preis:</div>
                    <div className="font-medium">{order.estimatedPrice} {order.currency}</div>
                  </div>
                )}
                <div>
                  <div className="text-sm text-muted-foreground">Status:</div>
                  <Badge>{order.status}</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fahrerliste */}
      <div className="mb-4">
        <h2 className="text-xl font-semibold">Interessierte Fahrer ({order.bids?.length || 0})</h2>
        <p className="text-muted-foreground">Wählen Sie einen Fahrer aus, um ihn diesem Auftrag zuzuweisen</p>
      </div>

      {order.bids && order.bids.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {order.bids.map(renderDriverCard)}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <p>Keine Gebote oder Interessensbekundungen für diesen Auftrag vorhanden.</p>
          </CardContent>
        </Card>
      )}

      {/* Aktionsbuttons */}
      <div className="flex justify-end mt-6">
        <Button 
          variant="outline" 
          className="mr-2"
          onClick={() => router.push(`/admin/orders/${orderId}`)}
        >
          Abbrechen
        </Button>
        <Button 
          onClick={handleAssignDriver}
          disabled={!selectedDriverId || assignDriverMutation.isPending}
          className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
        >
          {assignDriverMutation.isPending ? "Wird zugewiesen..." : "Fahrer zuweisen"}
        </Button>
      </div>
    </div>
  );
}
