"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Search,
  Filter,
  Eye,
  Calendar,
  MapPin,
  Car,
  Clock,
  CheckCircle2,
  Truck,
  Package,
  Ban
} from "lucide-react";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";

export default function AdminOrdersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Filter-Parameter aus der URL
  const currentPage = parseInt(searchParams.get("page") || "1");
  const currentStatus = searchParams.get("status") || "";
  const currentSearch = searchParams.get("search") || "";

  // Lokale Filter-Zustände
  const [status, setStatus] = useState(currentStatus);
  const [search, setSearch] = useState(currentSearch);

  // Aufträge abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["admin-orders", currentPage, currentStatus, currentSearch],
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append("page", currentPage.toString());
      if (currentStatus) params.append("status", currentStatus);
      if (currentSearch) params.append("search", currentSearch);

      const response = await fetch(`/api/admin/orders?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Aufträge");
      }
      return response.json();
    },
  });

  // Filter anwenden
  const applyFilters = () => {
    const params = new URLSearchParams();
    params.append("page", "1"); // Bei Filteränderung zurück zur ersten Seite
    if (status) params.append("status", status);
    if (search) params.append("search", search);

    router.push(`/admin/orders?${params.toString()}`);
  };

  // Filter zurücksetzen
  const resetFilters = () => {
    setStatus("");
    setSearch("");
    router.push("/admin/orders");
  };

  // Status-Badge rendern
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "DRAFT":
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Entwurf</span>
          </Badge>
        );
      case "POSTED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200">
            <Package className="h-3 w-3" />
            <span>Veröffentlicht</span>
          </Badge>
        );
      case "ASSIGNED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-purple-50 text-purple-700 border-purple-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Zugewiesen</span>
          </Badge>
        );
      case "EN_ROUTE_TO_PICKUP":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-yellow-50 text-yellow-700 border-yellow-200">
            <Truck className="h-3 w-3" />
            <span>Unterwegs zur Abholung</span>
          </Badge>
        );
      case "PICKED_UP":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-orange-50 text-orange-700 border-orange-200">
            <Package className="h-3 w-3" />
            <span>Abgeholt</span>
          </Badge>
        );
      case "EN_ROUTE_TO_DELIVERY":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-indigo-50 text-indigo-700 border-indigo-200">
            <Truck className="h-3 w-3" />
            <span>Unterwegs zur Lieferung</span>
          </Badge>
        );
      case "DELIVERED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-teal-50 text-teal-700 border-teal-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Geliefert</span>
          </Badge>
        );
      case "COMPLETED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3" />
            <span>Abgeschlossen</span>
          </Badge>
        );
      case "CANCELLED":
        return (
          <Badge variant="outline" className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200">
            <Ban className="h-3 w-3" />
            <span>Storniert</span>
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Fahrzeugtyp formatieren
  const formatVehicleType = (type: string) => {
    switch (type) {
      case "SEDAN":
        return "Limousine";
      case "SUV":
        return "SUV";
      case "VAN":
        return "Van";
      case "TRUCK":
        return "LKW";
      case "LUXURY":
        return "Luxusfahrzeug";
      case "SPORTS":
        return "Sportwagen";
      case "OTHER":
        return "Sonstiges";
      default:
        return type;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Aufträge</CardTitle>
          <CardDescription>Verwalten Sie alle Aufträge der Plattform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Aufträge</CardTitle>
          <CardDescription>Fehler beim Laden der Aufträge</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.</p>
        </CardContent>
      </Card>
    );
  }

  const orders = data?.orders || [];
  const pagination = data?.pagination || { page: 1, totalPages: 1, total: 0 };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aufträge</CardTitle>
        <CardDescription>Verwalten Sie alle Aufträge der Plattform</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filter */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Suche nach ID, Kunde, Fahrer..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Alle Status</SelectItem>
                <SelectItem value="DRAFT">Entwurf</SelectItem>
                <SelectItem value="POSTED">Veröffentlicht</SelectItem>
                <SelectItem value="ASSIGNED">Zugewiesen</SelectItem>
                <SelectItem value="EN_ROUTE_TO_PICKUP">Unterwegs zur Abholung</SelectItem>
                <SelectItem value="PICKED_UP">Abgeholt</SelectItem>
                <SelectItem value="EN_ROUTE_TO_DELIVERY">Unterwegs zur Lieferung</SelectItem>
                <SelectItem value="DELIVERED">Geliefert</SelectItem>
                <SelectItem value="COMPLETED">Abgeschlossen</SelectItem>
                <SelectItem value="CANCELLED">Storniert</SelectItem>
              </SelectContent>
            </Select>
            <GradientButton onClick={applyFilters} className="gap-2">
              <Filter className="h-4 w-4" />
              Filtern
            </GradientButton>
            <GradientButton variant="variant" onClick={resetFilters}>
              Zurücksetzen
            </GradientButton>
          </div>
        </div>

        {/* Auftragstabelle */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Titel</TableHead>
                <TableHead>Kunde</TableHead>
                <TableHead>Fahrer</TableHead>
                <TableHead>Fahrzeug</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Erstellt am</TableHead>
                <TableHead className="text-right">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    Keine Aufträge gefunden
                  </TableCell>
                </TableRow>
              ) : (
                orders.map((order: any) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-medium">{order.id.substring(0, 8)}</TableCell>
                    <TableCell>{order.title}</TableCell>
                    <TableCell>
                      {order.clientProfile?.companyName || "Unbekannt"}
                    </TableCell>
                    <TableCell>
                      {order.assignment?.driver?.driverProfile
                        ? `${order.assignment.driver.driverProfile.firstName} ${order.assignment.driver.driverProfile.lastName}`
                        : "Nicht zugewiesen"}
                    </TableCell>
                    <TableCell>{formatVehicleType(order.vehicleType)}</TableCell>
                    <TableCell>{renderStatusBadge(order.status)}</TableCell>
                    <TableCell>
                      {new Date(order.createdAt).toLocaleDateString("de-DE")}
                    </TableCell>
                    <TableCell className="text-right">
                      <GradientButton variant="variant" asChild className="text-sm py-1.5 px-3 min-w-[80px]">
                        <Link href={`/admin/orders/${order.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          Details
                        </Link>
                      </GradientButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Paginierung */}
        {pagination.totalPages > 1 && (
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href={`/admin/orders?page=${Math.max(1, currentPage - 1)}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                  aria-disabled={currentPage <= 1}
                  className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>

              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                .filter(page => {
                  // Zeige nur die ersten 2, letzten 2 und aktuellen Seiten +/- 1
                  return (
                    page <= 2 ||
                    page > pagination.totalPages - 2 ||
                    Math.abs(page - currentPage) <= 1
                  );
                })
                .map((page, index, array) => {
                  // Füge Ellipsis hinzu, wenn Seiten übersprungen werden
                  const showEllipsis = index > 0 && array[index - 1] !== page - 1;

                  return (
                    <div key={page} className="flex items-center">
                      {showEllipsis && (
                        <span className="px-2">...</span>
                      )}
                      <PaginationItem>
                        <PaginationLink
                          href={`/admin/orders?page=${page}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    </div>
                  );
                })}

              <PaginationItem>
                <PaginationNext
                  href={`/admin/orders?page=${Math.min(pagination.totalPages, currentPage + 1)}${currentStatus ? `&status=${currentStatus}` : ""}${currentSearch ? `&search=${currentSearch}` : ""}`}
                  aria-disabled={currentPage >= pagination.totalPages}
                  className={currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </CardContent>
    </Card>
  );
}
