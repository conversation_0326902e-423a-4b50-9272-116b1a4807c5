"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Filter, RefreshCw } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";

// Types
interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  status: "OPEN" | "IN_PROGRESS" | "RESOLVED" | "CLOSED";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  createdAt: string;
  updatedAt: string;
  resolvedAt: string | null;
  user: {
    id: string;
    email: string;
    role: string;
    clientProfile?: {
      companyName: string;
      contactPersonName: string;
    };
    driverProfile?: {
      firstName: string;
      lastName: string;
    };
  };
  responses: Array<{
    id: string;
    message: string;
    isFromAdmin: boolean;
    createdAt: string;
    responder: {
      id: string;
      email: string;
      role: string;
    };
  }>;
}

interface PaginationInfo {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
}

// Helper functions
const statusLabels = {
  OPEN: "Offen",
  IN_PROGRESS: "In Bearbeitung",
  RESOLVED: "Gelöst",
  CLOSED: "Geschlossen"
};

const priorityLabels = {
  LOW: "Niedrig",
  MEDIUM: "Mittel",
  HIGH: "Hoch",
  CRITICAL: "Kritisch"
};

const statusColors = {
  OPEN: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  RESOLVED: "bg-green-100 text-green-800",
  CLOSED: "bg-gray-100 text-gray-800"
};

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  CRITICAL: "bg-red-100 text-red-800"
};

export default function AdminSupportTicketsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [priorityFilter, setPriorityFilter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch support tickets
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["supportTickets", currentPage, statusFilter, priorityFilter],
    queryFn: async () => {
      let url = `/api/support/tickets?page=${currentPage}&limit=10`;

      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }

      if (priorityFilter) {
        url += `&priority=${priorityFilter}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Fehler beim Laden der Support-Tickets");
      }

      return response.json();
    },
  });

  const tickets: SupportTicket[] = data?.tickets || [];
  const pagination: PaginationInfo = data?.pagination || { page: 1, limit: 10, totalCount: 0, totalPages: 1 };

  // Filter tickets by search term
  const filteredTickets = tickets.filter(ticket =>
    ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle ticket click
  const handleTicketClick = (ticketId: string) => {
    router.push(`/admin/support-tickets/${ticketId}`);
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter(null);
    setPriorityFilter(null);
    setCurrentPage(1);
  };

  // Get user display name
  const getUserDisplayName = (ticket: SupportTicket) => {
    if (ticket.user.clientProfile) {
      return `${ticket.user.clientProfile.contactPersonName} (${ticket.user.clientProfile.companyName})`;
    } else if (ticket.user.driverProfile) {
      return `${ticket.user.driverProfile.firstName} ${ticket.user.driverProfile.lastName}`;
    } else {
      return ticket.user.email;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold">Support-Tickets</h1>
          <p className="text-gray-500">Verwalten Sie Support-Anfragen von Benutzern</p>
        </div>
        <GradientButton onClick={() => refetch()} className="mt-4 md:mt-0">
          <RefreshCw className="h-4 w-4 mr-2" />
          Aktualisieren
        </GradientButton>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Filter</CardTitle>
          <CardDescription>Filtern Sie die Support-Tickets nach verschiedenen Kriterien</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Suchen..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={statusFilter || ""} onValueChange={(value) => setStatusFilter(value || null)}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Alle Status</SelectItem>
                <SelectItem value="OPEN">Offen</SelectItem>
                <SelectItem value="IN_PROGRESS">In Bearbeitung</SelectItem>
                <SelectItem value="RESOLVED">Gelöst</SelectItem>
                <SelectItem value="CLOSED">Geschlossen</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter || ""} onValueChange={(value) => setPriorityFilter(value || null)}>
              <SelectTrigger>
                <SelectValue placeholder="Priorität" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Alle Prioritäten</SelectItem>
                <SelectItem value="LOW">Niedrig</SelectItem>
                <SelectItem value="MEDIUM">Mittel</SelectItem>
                <SelectItem value="HIGH">Hoch</SelectItem>
                <SelectItem value="CRITICAL">Kritisch</SelectItem>
              </SelectContent>
            </Select>

            <GradientButton variant="variant" onClick={resetFilters}>
              <Filter className="h-4 w-4 mr-2" />
              Filter zurücksetzen
            </GradientButton>
          </div>
        </CardContent>
      </Card>

      {/* Tickets list */}
      <Card>
        <CardHeader>
          <CardTitle>Support-Tickets</CardTitle>
          <CardDescription>
            {pagination.totalCount} Tickets gefunden
            {searchTerm && ` (gefiltert: ${filteredTickets.length})`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Tickets werden geladen...</p>
            </div>
          ) : isError ? (
            <div className="text-center py-8 text-red-500">
              <p>Fehler beim Laden der Tickets. Bitte versuchen Sie es später erneut.</p>
            </div>
          ) : filteredTickets.length === 0 ? (
            <div className="text-center py-8">
              <p>Keine Support-Tickets gefunden.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Betreff</th>
                    <th className="text-left py-3 px-4">Benutzer</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Priorität</th>
                    <th className="text-left py-3 px-4">Erstellt am</th>
                    <th className="text-left py-3 px-4">Antworten</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTickets.map((ticket) => (
                    <tr
                      key={ticket.id}
                      className="border-b hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleTicketClick(ticket.id)}
                    >
                      <td className="py-3 px-4">{ticket.subject}</td>
                      <td className="py-3 px-4">{getUserDisplayName(ticket)}</td>
                      <td className="py-3 px-4">
                        <Badge className={statusColors[ticket.status]}>
                          {statusLabels[ticket.status]}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={priorityColors[ticket.priority]}>
                          {priorityLabels[ticket.priority]}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        {format(new Date(ticket.createdAt), "dd.MM.yyyy HH:mm", { locale: de })}
                      </td>
                      <td className="py-3 px-4">
                        {ticket.responses.length}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-between items-center mt-6">
              <div>
                Seite {pagination.page} von {pagination.totalPages}
              </div>
              <div className="flex gap-2">
                <GradientButton
                  variant="variant"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={pagination.page === 1}
                >
                  Zurück
                </GradientButton>
                <GradientButton
                  variant="variant"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Weiter
                </GradientButton>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
