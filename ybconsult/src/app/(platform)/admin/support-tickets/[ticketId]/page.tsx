"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { GradientButton } from "@/components/ui/gradient-button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Send, Loader2, User, UserCheck } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { toast } from "sonner";

// Types
interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  status: "OPEN" | "IN_PROGRESS" | "RESOLVED" | "CLOSED";
  priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  createdAt: string;
  updatedAt: string;
  resolvedAt: string | null;
  user: {
    id: string;
    email: string;
    role: string;
    clientProfile?: {
      companyName: string;
      contactPersonName: string;
    };
    driverProfile?: {
      firstName: string;
      lastName: string;
    };
  };
  responses: Array<{
    id: string;
    message: string;
    isFromAdmin: boolean;
    createdAt: string;
    responder: {
      id: string;
      email: string;
      role: string;
    };
  }>;
}

// Helper functions
const statusLabels = {
  OPEN: "Offen",
  IN_PROGRESS: "In Bearbeitung",
  RESOLVED: "Gelöst",
  CLOSED: "Geschlossen"
};

const priorityLabels = {
  LOW: "Niedrig",
  MEDIUM: "Mittel",
  HIGH: "Hoch",
  CRITICAL: "Kritisch"
};

const statusColors = {
  OPEN: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  RESOLVED: "bg-green-100 text-green-800",
  CLOSED: "bg-gray-100 text-gray-800"
};

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  CRITICAL: "bg-red-100 text-red-800"
};

export default function AdminSupportTicketDetailPage({ params }: { params: { ticketId: string } }) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [responseMessage, setResponseMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch ticket details
  const { data: ticket, isLoading, isError } = useQuery<SupportTicket>({
    queryKey: ["supportTicket", params.ticketId],
    queryFn: async () => {
      const response = await fetch(`/api/support/tickets/${params.ticketId}`);

      if (!response.ok) {
        throw new Error("Fehler beim Laden des Support-Tickets");
      }

      return response.json();
    },
  });

  // Update ticket status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async (status: string) => {
      const response = await fetch(`/api/support/tickets/${params.ticketId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Fehler beim Aktualisieren des Status");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["supportTicket", params.ticketId] });
      toast.success("Status erfolgreich aktualisiert");
    },
    onError: (error) => {
      console.error("Fehler beim Aktualisieren des Status:", error);
      toast.error("Fehler beim Aktualisieren des Status");
    },
  });

  // Update ticket priority mutation
  const updatePriorityMutation = useMutation({
    mutationFn: async (priority: string) => {
      const response = await fetch(`/api/support/tickets/${params.ticketId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ priority }),
      });

      if (!response.ok) {
        throw new Error("Fehler beim Aktualisieren der Priorität");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["supportTicket", params.ticketId] });
      toast.success("Priorität erfolgreich aktualisiert");
    },
    onError: (error) => {
      console.error("Fehler beim Aktualisieren der Priorität:", error);
      toast.error("Fehler beim Aktualisieren der Priorität");
    },
  });

  // Add response mutation
  const addResponseMutation = useMutation({
    mutationFn: async (message: string) => {
      const response = await fetch(`/api/support/tickets/${params.ticketId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error("Fehler beim Hinzufügen der Antwort");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["supportTicket", params.ticketId] });
      setResponseMessage("");
      toast.success("Antwort erfolgreich hinzugefügt");
    },
    onError: (error) => {
      console.error("Fehler beim Hinzufügen der Antwort:", error);
      toast.error("Fehler beim Hinzufügen der Antwort");
    },
  });

  // Handle status change
  const handleStatusChange = (status: string) => {
    updateStatusMutation.mutate(status);
  };

  // Handle priority change
  const handlePriorityChange = (priority: string) => {
    updatePriorityMutation.mutate(priority);
  };

  // Handle response submission
  const handleSubmitResponse = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!responseMessage.trim()) {
      toast.error("Bitte geben Sie eine Antwort ein");
      return;
    }

    setIsSubmitting(true);

    try {
      await addResponseMutation.mutateAsync(responseMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get user display name
  const getUserDisplayName = (ticket: SupportTicket) => {
    if (ticket.user.clientProfile) {
      return `${ticket.user.clientProfile.contactPersonName} (${ticket.user.clientProfile.companyName})`;
    } else if (ticket.user.driverProfile) {
      return `${ticket.user.driverProfile.firstName} ${ticket.user.driverProfile.lastName}`;
    } else {
      return ticket.user.email;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto" />
        <p className="mt-4">Ticket wird geladen...</p>
      </div>
    );
  }

  if (isError || !ticket) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p className="text-red-500">Fehler beim Laden des Tickets. Bitte versuchen Sie es später erneut.</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Zurück
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Button onClick={() => router.back()} variant="outline" className="mb-6">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Zurück zur Übersicht
      </Button>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Ticket details */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{ticket.subject}</CardTitle>
                  <CardDescription>
                    Ticket-ID: {ticket.id}
                  </CardDescription>
                </div>
                <Badge className={statusColors[ticket.status]}>
                  {statusLabels[ticket.status]}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Nachricht</h3>
                  <div className="bg-gray-50 p-4 rounded-md whitespace-pre-wrap">
                    {ticket.message}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-4">Konversationsverlauf</h3>

                  {ticket.responses.length === 0 ? (
                    <p className="text-gray-500 italic">Noch keine Antworten</p>
                  ) : (
                    <div className="space-y-4">
                      {ticket.responses.map((response) => (
                        <div
                          key={response.id}
                          className={`p-4 rounded-md ${
                            response.isFromAdmin
                              ? "bg-blue-50 ml-8"
                              : "bg-gray-50 mr-8"
                          }`}
                        >
                          <div className="flex items-center mb-2">
                            {response.isFromAdmin ? (
                              <UserCheck className="h-4 w-4 mr-2 text-blue-500" />
                            ) : (
                              <User className="h-4 w-4 mr-2 text-gray-500" />
                            )}
                            <span className="font-medium">
                              {response.isFromAdmin ? "Support-Team" : getUserDisplayName(ticket)}
                            </span>
                            <span className="text-xs text-gray-500 ml-2">
                              {format(new Date(response.createdAt), "dd.MM.yyyy HH:mm", { locale: de })}
                            </span>
                          </div>
                          <div className="whitespace-pre-wrap">
                            {response.message}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Response form */}
                <form onSubmit={handleSubmitResponse}>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Antwort hinzufügen</h3>
                  <Textarea
                    value={responseMessage}
                    onChange={(e) => setResponseMessage(e.target.value)}
                    placeholder="Geben Sie Ihre Antwort ein..."
                    rows={4}
                    className="mb-4"
                  />
                  <Button type="submit" disabled={isSubmitting || !responseMessage.trim()}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Wird gesendet...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Antwort senden
                      </>
                    )}
                  </Button>
                </form>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Ticket info and actions */}
        <div>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Ticket-Informationen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Benutzer</h3>
                  <p>{getUserDisplayName(ticket)}</p>
                  <p className="text-sm text-gray-500">{ticket.user.email}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Erstellt am</h3>
                  <p>{format(new Date(ticket.createdAt), "dd.MM.yyyy HH:mm", { locale: de })}</p>
                </div>

                {ticket.resolvedAt && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Gelöst am</h3>
                    <p>{format(new Date(ticket.resolvedAt), "dd.MM.yyyy HH:mm", { locale: de })}</p>
                  </div>
                )}

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Priorität</h3>
                  <Select
                    defaultValue={ticket.priority}
                    onValueChange={handlePriorityChange}
                    disabled={updatePriorityMutation.isPending}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">{priorityLabels.LOW}</SelectItem>
                      <SelectItem value="MEDIUM">{priorityLabels.MEDIUM}</SelectItem>
                      <SelectItem value="HIGH">{priorityLabels.HIGH}</SelectItem>
                      <SelectItem value="CRITICAL">{priorityLabels.CRITICAL}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <Select
                    defaultValue={ticket.status}
                    onValueChange={handleStatusChange}
                    disabled={updateStatusMutation.isPending}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="OPEN">{statusLabels.OPEN}</SelectItem>
                      <SelectItem value="IN_PROGRESS">{statusLabels.IN_PROGRESS}</SelectItem>
                      <SelectItem value="RESOLVED">{statusLabels.RESOLVED}</SelectItem>
                      <SelectItem value="CLOSED">{statusLabels.CLOSED}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Schnelle Aktionen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleStatusChange("RESOLVED")}
                  disabled={ticket.status === "RESOLVED" || updateStatusMutation.isPending}
                >
                  Als gelöst markieren
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleStatusChange("CLOSED")}
                  disabled={ticket.status === "CLOSED" || updateStatusMutation.isPending}
                >
                  Ticket schließen
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
