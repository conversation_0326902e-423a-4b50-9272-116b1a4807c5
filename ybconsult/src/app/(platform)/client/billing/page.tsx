"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { queryKeys } from "@/lib/queryKeys";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon, Download, FileText, Search } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

// Hilfsfunktion für die Statusanzeige
const getStatusBadge = (status: string) => {
  switch (status) {
    case "PAID":
      return <Badge className="bg-green-500 hover:bg-green-600">Bezahlt</Badge>;
    case "UNPAID":
      return <Badge variant="outline">Unbezahlt</Badge>;
    case "OVERDUE":
      return <Badge variant="destructive">Überfällig</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

/**
 * Komponente für die Rechnungsübersicht des Geschäftskunden.
 * Zeigt eine Liste aller Rechnungen mit Filtermöglichkeiten und Download-Option.
 */
export default function BillingPage() {
  // Filter-Status
  const [filters, setFilters] = useState({
    status: "",
    search: "",
    dateFrom: undefined as Date | undefined,
    dateTo: undefined as Date | undefined,
  });
  
  // Paginierung
  const [page, setPage] = useState(1);
  const limit = 10;
  
  // Daten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: queryKeys.client.billing(),
    queryFn: async () => {
      // URL-Parameter für die API-Anfrage erstellen
      const params = new URLSearchParams();
      if (filters.status) params.append("status", filters.status);
      if (filters.search) params.append("search", filters.search);
      if (filters.dateFrom) params.append("dateFrom", filters.dateFrom.toISOString());
      if (filters.dateTo) params.append("dateTo", filters.dateTo.toISOString());
      params.append("page", page.toString());
      params.append("limit", limit.toString());
      
      const response = await fetch(`/api/client/billing?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Rechnungsdaten");
      }
      return response.json();
    },
  });
  
  // Filter anwenden
  const applyFilters = () => {
    setPage(1); // Bei Filteränderung zurück zur ersten Seite
  };
  
  // Filter zurücksetzen
  const resetFilters = () => {
    setFilters({
      status: "",
      search: "",
      dateFrom: undefined,
      dateTo: undefined,
    });
    setPage(1);
  };
  
  // Seitenwechsel
  const changePage = (newPage: number) => {
    setPage(newPage);
  };
  
  // Rechnung herunterladen
  const downloadInvoice = async (invoiceId: string) => {
    try {
      const response = await fetch(`/api/client/billing/${invoiceId}/download`);
      if (!response.ok) {
        throw new Error("Fehler beim Herunterladen der Rechnung");
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Rechnung_${invoiceId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
    } catch (error) {
      console.error("Fehler beim Herunterladen:", error);
      // Hier könnte eine Toast-Benachrichtigung angezeigt werden
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Rechnungen</h1>
        <p className="text-muted-foreground mt-1">
          Verwalten Sie Ihre Rechnungen und Zahlungen
        </p>
      </div>

      {/* Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Filter</CardTitle>
          <CardDescription>
            Filtern Sie Ihre Rechnungen nach verschiedenen Kriterien
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({ ...filters, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Alle Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Alle Status</SelectItem>
                  <SelectItem value="PAID">Bezahlt</SelectItem>
                  <SelectItem value="UNPAID">Unbezahlt</SelectItem>
                  <SelectItem value="OVERDUE">Überfällig</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="dateFrom">Von Datum</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateFrom"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateFrom ? (
                      format(filters.dateFrom, "PPP", { locale: de })
                    ) : (
                      <span>Datum auswählen</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateFrom}
                    onSelect={(date) => setFilters({ ...filters, dateFrom: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="dateTo">Bis Datum</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateTo"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateTo ? (
                      format(filters.dateTo, "PPP", { locale: de })
                    ) : (
                      <span>Datum auswählen</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateTo}
                    onSelect={(date) => setFilters({ ...filters, dateTo: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="search">Suche</Label>
              <div className="flex">
                <Input
                  id="search"
                  placeholder="Rechnungsnr., Auftragsnr., ..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="rounded-r-none"
                />
                <Button 
                  type="button" 
                  variant="default" 
                  onClick={applyFilters}
                  className="rounded-l-none"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rechnungsliste */}
      <Card>
        <CardHeader className="pb-0">
          <CardTitle>Rechnungen</CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Fehler beim Laden der Daten. Bitte versuchen Sie es später erneut.</p>
            </div>
          ) : data?.invoices?.length > 0 ? (
            <>
              <div className="rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Rechnungsnr.</TableHead>
                      <TableHead>Auftragsnr.</TableHead>
                      <TableHead>Datum</TableHead>
                      <TableHead>Fälligkeitsdatum</TableHead>
                      <TableHead>Betrag</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.invoices.map((invoice: any) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-medium">{invoice.invoiceNumber}</TableCell>
                        <TableCell>{invoice.order.orderReference}</TableCell>
                        <TableCell>{format(new Date(invoice.createdAt), "dd.MM.yyyy")}</TableCell>
                        <TableCell>{format(new Date(invoice.dueDate), "dd.MM.yyyy")}</TableCell>
                        <TableCell>{invoice.amount} €</TableCell>
                        <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => downloadInvoice(invoice.id)}
                            className="flex items-center"
                          >
                            <Download className="mr-2 h-4 w-4" />
                            PDF
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {/* Paginierung */}
              {data.pagination && data.pagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-muted-foreground">
                    Zeige {(page - 1) * limit + 1} bis {Math.min(page * limit, data.pagination.total)} von {data.pagination.total} Einträgen
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => changePage(page - 1)}
                      disabled={page <= 1}
                    >
                      Zurück
                    </Button>
                    {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1)
                      .filter(p => Math.abs(p - page) < 2 || p === 1 || p === data.pagination.totalPages)
                      .map((p, i, arr) => (
                        <React.Fragment key={p}>
                          {i > 0 && arr[i - 1] !== p - 1 && (
                            <Button variant="outline" size="sm" disabled>
                              ...
                            </Button>
                          )}
                          <Button
                            variant={p === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => changePage(p)}
                          >
                            {p}
                          </Button>
                        </React.Fragment>
                      ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => changePage(page + 1)}
                      disabled={page >= data.pagination.totalPages}
                    >
                      Weiter
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Keine Rechnungen gefunden</h3>
              <p className="text-muted-foreground mb-6">
                Es wurden keine Rechnungen gefunden, die Ihren Filterkriterien entsprechen.
              </p>
              <Button variant="outline" onClick={resetFilters}>
                Filter zurücksetzen
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
