"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createOrderSchema, CreateOrderInput, VehicleType } from "@/lib/validators/order.schemas";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon, Loader2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

/**
 * Komponente für die Auftragserstellung.
 * Implementiert einen mehrstufigen Wizard-Flow mit Validierung.
 */
export default function CreateOrderPage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null);
  const totalSteps = 4;

  // React Hook Form mit Zod-Validierung
  const form = useForm<CreateOrderInput>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      vehicle: {
        vehicleType: VehicleType.CAR,
      },
      pickup: {
        country: "Deutschland",
      },
      delivery: {
        country: "Deutschland",
      },
      pickupDate: {
        from: new Date(),
        to: new Date(new Date().setDate(new Date().getDate() + 1)),
      },
      deliveryDate: {
        from: new Date(new Date().setDate(new Date().getDate() + 2)),
        to: new Date(new Date().setDate(new Date().getDate() + 3)),
      },
    },
  });

  // Mutation für die Auftragserstellung
  const createOrderMutation = useMutation({
    mutationFn: async (data: CreateOrderInput) => {
      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler beim Erstellen des Auftrags");
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast.success("Auftrag erfolgreich erstellt!");
      router.push(`/client/orders/${data.id}`);
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });

  // Funktion zum Berechnen des geschätzten Preises
  const calculateEstimatedPrice = async () => {
    try {
      const formData = form.getValues();
      const response = await fetch("/api/orders/calculate-price", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Fehler bei der Preisberechnung");
      }

      const data = await response.json();
      setEstimatedPrice(data.estimatedPrice);
    } catch (error) {
      toast.error("Fehler bei der Preisberechnung. Bitte versuchen Sie es später erneut.");
    }
  };

  // Funktion zum Fortfahren zum nächsten Schritt
  const nextStep = async () => {
    let isValid = false;

    switch (step) {
      case 1:
        isValid = await form.trigger("vehicle");
        break;
      case 2:
        isValid = await form.trigger(["pickup", "pickupDate"]);
        break;
      case 3:
        isValid = await form.trigger(["delivery", "deliveryDate"]);
        if (isValid) {
          // Berechne den geschätzten Preis vor dem letzten Schritt
          await calculateEstimatedPrice();
        }
        break;
      default:
        isValid = true;
    }

    if (isValid && step < totalSteps) {
      setStep(step + 1);
    }
  };

  // Funktion zum Zurückkehren zum vorherigen Schritt
  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Funktion zum Absenden des Formulars
  const onSubmit = (data: CreateOrderInput) => {
    createOrderMutation.mutate(data);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Neuen Transportauftrag erstellen</h1>
        <p className="text-muted-foreground mt-2">
          Bitte füllen Sie alle erforderlichen Informationen aus, um einen neuen Fahrzeugtransport zu beauftragen.
        </p>
      </div>

      {/* Fortschrittsanzeige */}
      <div className="mb-8">
        <div className="flex justify-between">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium",
                  step > index + 1
                    ? "bg-primary text-primary-foreground"
                    : step === index + 1
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted text-muted-foreground"
                )}
              >
                {step > index + 1 ? <CheckCircle className="h-5 w-5" /> : index + 1}
              </div>
              <span className="text-xs mt-2 text-muted-foreground">
                {index === 0
                  ? "Fahrzeug"
                  : index === 1
                  ? "Abholung"
                  : index === 2
                  ? "Lieferung"
                  : "Bestätigung"}
              </span>
            </div>
          ))}
        </div>
        <div className="mt-2 h-2 bg-muted rounded-full">
          <div
            className="h-2 bg-primary rounded-full transition-all"
            style={{ width: `${((step - 1) / (totalSteps - 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)}>
        {/* Schritt 1: Fahrzeugdaten */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Fahrzeugdaten</CardTitle>
              <CardDescription>
                Geben Sie die Details des zu transportierenden Fahrzeugs an.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vin">Fahrzeug-Identifikationsnummer (VIN) *</Label>
                  <Input
                    id="vin"
                    {...form.register("vehicle.vin")}
                    placeholder="z.B. WVWZZZ1JZXW000001"
                  />
                  {form.formState.errors.vehicle?.vin && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.vehicle.vin.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="vehicleType">Fahrzeugtyp *</Label>
                  <Select
                    defaultValue={form.getValues("vehicle.vehicleType")}
                    onValueChange={(value) => form.setValue("vehicle.vehicleType", value as VehicleType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Fahrzeugtyp auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={VehicleType.CAR}>PKW</SelectItem>
                      <SelectItem value={VehicleType.VAN}>Transporter</SelectItem>
                      <SelectItem value={VehicleType.SUV}>SUV</SelectItem>
                      <SelectItem value={VehicleType.TRUCK}>LKW</SelectItem>
                      <SelectItem value={VehicleType.MOTORCYCLE}>Motorrad</SelectItem>
                      <SelectItem value={VehicleType.LUXURY}>Luxusfahrzeug</SelectItem>
                      <SelectItem value={VehicleType.VINTAGE}>Oldtimer</SelectItem>
                      <SelectItem value={VehicleType.OTHER}>Sonstiges</SelectItem>
                    </SelectContent>
                  </Select>
                  {form.formState.errors.vehicle?.vehicleType && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.vehicle.vehicleType.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="make">Marke *</Label>
                  <Input
                    id="make"
                    {...form.register("vehicle.make")}
                    placeholder="z.B. Volkswagen"
                  />
                  {form.formState.errors.vehicle?.make && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.vehicle.make.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="model">Modell *</Label>
                  <Input
                    id="model"
                    {...form.register("vehicle.model")}
                    placeholder="z.B. Golf"
                  />
                  {form.formState.errors.vehicle?.model && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.vehicle.model.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="year">Baujahr</Label>
                  <Input
                    id="year"
                    type="number"
                    {...form.register("vehicle.year", { valueAsNumber: true })}
                    placeholder="z.B. 2020"
                  />
                  {form.formState.errors.vehicle?.year && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.vehicle.year.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="color">Farbe</Label>
                  <Input
                    id="color"
                    {...form.register("vehicle.color")}
                    placeholder="z.B. Schwarz"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="licensePlate">Kennzeichen</Label>
                  <Input
                    id="licensePlate"
                    {...form.register("vehicle.licensePlate")}
                    placeholder="z.B. B-AB 1234"
                  />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">Anmerkungen zum Fahrzeug</Label>
                  <Textarea
                    id="notes"
                    {...form.register("vehicle.notes")}
                    placeholder="Besonderheiten oder zusätzliche Informationen zum Fahrzeug"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button type="button" onClick={nextStep}>
                Weiter <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Schritt 2: Abholungsdaten */}
        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>Abholungsdaten</CardTitle>
              <CardDescription>
                Geben Sie an, wo und wann das Fahrzeug abgeholt werden soll.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="pickupAddressLine1">Straße, Hausnummer *</Label>
                  <Input
                    id="pickupAddressLine1"
                    {...form.register("pickup.addressLine1")}
                    placeholder="z.B. Musterstraße 123"
                  />
                  {form.formState.errors.pickup?.addressLine1 && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.pickup.addressLine1.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="pickupAddressLine2">Adresszusatz</Label>
                  <Input
                    id="pickupAddressLine2"
                    {...form.register("pickup.addressLine2")}
                    placeholder="z.B. Hinterhof, Eingang B"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pickupPostalCode">Postleitzahl *</Label>
                  <Input
                    id="pickupPostalCode"
                    {...form.register("pickup.postalCode")}
                    placeholder="z.B. 10115"
                  />
                  {form.formState.errors.pickup?.postalCode && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.pickup.postalCode.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pickupCity">Stadt *</Label>
                  <Input
                    id="pickupCity"
                    {...form.register("pickup.city")}
                    placeholder="z.B. Berlin"
                  />
                  {form.formState.errors.pickup?.city && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.pickup.city.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pickupCountry">Land *</Label>
                  <Input
                    id="pickupCountry"
                    {...form.register("pickup.country")}
                    placeholder="z.B. Deutschland"
                  />
                  {form.formState.errors.pickup?.country && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.pickup.country.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pickupContactName">Kontaktperson</Label>
                  <Input
                    id="pickupContactName"
                    {...form.register("pickup.contactName")}
                    placeholder="z.B. Max Mustermann"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="pickupContactPhone">Telefonnummer</Label>
                  <Input
                    id="pickupContactPhone"
                    {...form.register("pickup.contactPhone")}
                    placeholder="z.B. +49 123 4567890"
                  />
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <Label>Gewünschter Abholzeitraum *</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pickupDateFrom">Von</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !form.getValues("pickupDate.from") && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {form.getValues("pickupDate.from") ? (
                            format(form.getValues("pickupDate.from"), "PPP", { locale: de })
                          ) : (
                            <span>Datum auswählen</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={form.getValues("pickupDate.from")}
                          onSelect={(date) => date && form.setValue("pickupDate.from", date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.pickupDate?.from && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.pickupDate.from.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pickupDateTo">Bis</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !form.getValues("pickupDate.to") && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {form.getValues("pickupDate.to") ? (
                            format(form.getValues("pickupDate.to"), "PPP", { locale: de })
                          ) : (
                            <span>Datum auswählen</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={form.getValues("pickupDate.to")}
                          onSelect={(date) => date && form.setValue("pickupDate.to", date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.pickupDate?.to && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.pickupDate.to.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Zurück
              </Button>
              <Button type="button" onClick={nextStep}>
                Weiter <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Schritt 3: Lieferungsdaten */}
        {step === 3 && (
          <Card>
            <CardHeader>
              <CardTitle>Lieferungsdaten</CardTitle>
              <CardDescription>
                Geben Sie an, wo und wann das Fahrzeug geliefert werden soll.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="deliveryAddressLine1">Straße, Hausnummer *</Label>
                  <Input
                    id="deliveryAddressLine1"
                    {...form.register("delivery.addressLine1")}
                    placeholder="z.B. Musterstraße 123"
                  />
                  {form.formState.errors.delivery?.addressLine1 && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.delivery.addressLine1.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="deliveryAddressLine2">Adresszusatz</Label>
                  <Input
                    id="deliveryAddressLine2"
                    {...form.register("delivery.addressLine2")}
                    placeholder="z.B. Hinterhof, Eingang B"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryPostalCode">Postleitzahl *</Label>
                  <Input
                    id="deliveryPostalCode"
                    {...form.register("delivery.postalCode")}
                    placeholder="z.B. 10115"
                  />
                  {form.formState.errors.delivery?.postalCode && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.delivery.postalCode.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryCity">Stadt *</Label>
                  <Input
                    id="deliveryCity"
                    {...form.register("delivery.city")}
                    placeholder="z.B. Berlin"
                  />
                  {form.formState.errors.delivery?.city && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.delivery.city.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryCountry">Land *</Label>
                  <Input
                    id="deliveryCountry"
                    {...form.register("delivery.country")}
                    placeholder="z.B. Deutschland"
                  />
                  {form.formState.errors.delivery?.country && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.delivery.country.message}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryContactName">Kontaktperson</Label>
                  <Input
                    id="deliveryContactName"
                    {...form.register("delivery.contactName")}
                    placeholder="z.B. Max Mustermann"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="deliveryContactPhone">Telefonnummer</Label>
                  <Input
                    id="deliveryContactPhone"
                    {...form.register("delivery.contactPhone")}
                    placeholder="z.B. +49 123 4567890"
                  />
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <Label>Gewünschter Lieferzeitraum *</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="deliveryDateFrom">Von</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !form.getValues("deliveryDate.from") && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {form.getValues("deliveryDate.from") ? (
                            format(form.getValues("deliveryDate.from"), "PPP", { locale: de })
                          ) : (
                            <span>Datum auswählen</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={form.getValues("deliveryDate.from")}
                          onSelect={(date) => date && form.setValue("deliveryDate.from", date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.deliveryDate?.from && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.deliveryDate.from.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deliveryDateTo">Bis</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !form.getValues("deliveryDate.to") && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {form.getValues("deliveryDate.to") ? (
                            format(form.getValues("deliveryDate.to"), "PPP", { locale: de })
                          ) : (
                            <span>Datum auswählen</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={form.getValues("deliveryDate.to")}
                          onSelect={(date) => date && form.setValue("deliveryDate.to", date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {form.formState.errors.deliveryDate?.to && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.deliveryDate.to.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <Label htmlFor="specialInstructions">Besondere Anweisungen</Label>
                <Textarea
                  id="specialInstructions"
                  {...form.register("specialInstructions")}
                  placeholder="Besonderheiten oder zusätzliche Informationen zum Transport"
                  rows={3}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Zurück
              </Button>
              <Button type="button" onClick={nextStep}>
                Weiter <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Schritt 4: Zusammenfassung und Bestätigung */}
        {step === 4 && (
          <Card>
            <CardHeader>
              <CardTitle>Zusammenfassung und Bestätigung</CardTitle>
              <CardDescription>
                Überprüfen Sie Ihre Angaben und bestätigen Sie den Auftrag.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Preisanzeige */}
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Preisübersicht</h3>
                {estimatedPrice ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Grundpreis</span>
                      <span>{(estimatedPrice * 0.7).toFixed(2)} €</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Entfernungszuschlag</span>
                      <span>{(estimatedPrice * 0.2).toFixed(2)} €</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Fahrzeugtyp-Zuschlag</span>
                      <span>{(estimatedPrice * 0.1).toFixed(2)} €</span>
                    </div>
                    <div className="flex justify-between font-bold pt-2 border-t">
                      <span>Geschätzter Gesamtpreis</span>
                      <span>{estimatedPrice.toFixed(2)} €</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                    <p className="mt-2">Berechne Preis...</p>
                  </div>
                )}
              </div>

              {/* Zusammenfassung der Auftragsdaten */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Fahrzeugdaten</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">Fahrzeugtyp:</div>
                    <div>{form.getValues("vehicle.vehicleType")}</div>
                    <div className="font-medium">Marke:</div>
                    <div>{form.getValues("vehicle.make")}</div>
                    <div className="font-medium">Modell:</div>
                    <div>{form.getValues("vehicle.model")}</div>
                    <div className="font-medium">VIN:</div>
                    <div>{form.getValues("vehicle.vin")}</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Abholung</h3>
                    <div className="space-y-1 text-sm">
                      <p>{form.getValues("pickup.addressLine1")}</p>
                      {form.getValues("pickup.addressLine2") && <p>{form.getValues("pickup.addressLine2")}</p>}
                      <p>{form.getValues("pickup.postalCode")} {form.getValues("pickup.city")}</p>
                      <p>{form.getValues("pickup.country")}</p>
                      <p className="mt-2">
                        <span className="font-medium">Zeitraum: </span>
                        {form.getValues("pickupDate.from") && format(form.getValues("pickupDate.from"), "dd.MM.yyyy")} - {form.getValues("pickupDate.to") && format(form.getValues("pickupDate.to"), "dd.MM.yyyy")}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Lieferung</h3>
                    <div className="space-y-1 text-sm">
                      <p>{form.getValues("delivery.addressLine1")}</p>
                      {form.getValues("delivery.addressLine2") && <p>{form.getValues("delivery.addressLine2")}</p>}
                      <p>{form.getValues("delivery.postalCode")} {form.getValues("delivery.city")}</p>
                      <p>{form.getValues("delivery.country")}</p>
                      <p className="mt-2">
                        <span className="font-medium">Zeitraum: </span>
                        {form.getValues("deliveryDate.from") && format(form.getValues("deliveryDate.from"), "dd.MM.yyyy")} - {form.getValues("deliveryDate.to") && format(form.getValues("deliveryDate.to"), "dd.MM.yyyy")}
                      </p>
                    </div>
                  </div>
                </div>

                {form.getValues("specialInstructions") && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">Besondere Anweisungen</h3>
                    <p className="text-sm">{form.getValues("specialInstructions")}</p>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={prevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" /> Zurück
              </Button>
              <Button
                type="submit"
                disabled={createOrderMutation.isPending || !estimatedPrice}
              >
                {createOrderMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Wird bearbeitet...
                  </>
                ) : (
                  "Auftrag verbindlich erstellen"
                )}
              </Button>
            </CardFooter>
          </Card>
        )}
      </form>
    </div>
  );
}
