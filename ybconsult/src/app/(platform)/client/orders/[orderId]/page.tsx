"use client";

import { useQuery } from "@tanstack/react-query";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { queryKeys } from "@/lib/queryKeys";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Calendar, Clock, MapPin, Package, Truck, User, AlertTriangle, CheckCircle2 } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { de } from "date-fns/locale";

// Hilfsfunktion für die Statusanzeige
const getStatusBadge = (status: string) => {
  switch (status) {
    case "DRAFT":
      return <Badge variant="outline">Entwurf</Badge>;
    case "POSTED":
      return <Badge variant="secondary">Ausgeschrieben</Badge>;
    case "DRIVER_ASSIGNED":
      return <Badge variant="secondary">Fahrer zugewiesen</Badge>;
    case "IN_TRANSIT":
      return <Badge className="bg-blue-500 hover:bg-blue-600">In Transport</Badge>;
    case "COMPLETED":
      return <Badge className="bg-green-500 hover:bg-green-600">Abgeschlossen</Badge>;
    case "CANCELLED":
      return <Badge variant="destructive">Storniert</Badge>;
    case "ISSUE_REPORTED":
      return <Badge variant="destructive">Problem gemeldet</Badge>;
    case "PENDING_CLIENT_CONFIRMATION":
      return <Badge variant="warning">Bestätigung erforderlich</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

// Hilfsfunktion für die Statusbeschreibung
const getStatusDescription = (status: string) => {
  switch (status) {
    case "DRAFT":
      return "Dieser Auftrag ist noch ein Entwurf und wurde noch nicht veröffentlicht.";
    case "POSTED":
      return "Ihr Auftrag wurde veröffentlicht und ist für Fahrer sichtbar. Warten Sie auf Angebote.";
    case "DRIVER_ASSIGNED":
      return "Ein Fahrer wurde Ihrem Auftrag zugewiesen und wird das Fahrzeug abholen.";
    case "IN_TRANSIT":
      return "Ihr Fahrzeug ist unterwegs zum Zielort.";
    case "COMPLETED":
      return "Der Transport wurde erfolgreich abgeschlossen.";
    case "CANCELLED":
      return "Dieser Auftrag wurde storniert.";
    case "ISSUE_REPORTED":
      return "Es wurde ein Problem mit diesem Auftrag gemeldet. Bitte kontaktieren Sie den Support.";
    case "PENDING_CLIENT_CONFIRMATION":
      return "Bitte bestätigen Sie die Änderungen oder den Abschluss dieses Auftrags.";
    default:
      return "Status unbekannt.";
  }
};

/**
 * Komponente für die Detailansicht eines Auftrags.
 * Zeigt alle Informationen zu einem Auftrag und ermöglicht die Verfolgung des Status.
 */
export default function OrderDetailPage() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.orderId as string;

  // Auftragsdaten abrufen
  const { data: order, isLoading, error } = useQuery({
    queryKey: queryKeys.orders.detail(orderId),
    queryFn: async () => {
      const response = await fetch(`/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Auftragsdaten");
      }
      return response.json();
    },
    refetchInterval: 30000, // Alle 30 Sekunden aktualisieren für Echtzeit-Tracking
  });

  // Ereignisse zum Auftrag abrufen
  const { data: events, isLoading: eventsLoading } = useQuery({
    queryKey: ["orders", orderId, "events"],
    queryFn: async () => {
      const response = await fetch(`/api/orders/${orderId}/events`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Ereignisse");
      }
      return response.json();
    },
    enabled: !!order, // Nur laden, wenn der Auftrag geladen wurde
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/client/orders">
              <ArrowLeft className="mr-2 h-4 w-4" /> Zurück zur Übersicht
            </Link>
          </Button>
        </div>
        <Skeleton className="h-12 w-3/4" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/client/orders">
              <ArrowLeft className="mr-2 h-4 w-4" /> Zurück zur Übersicht
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6 flex flex-col items-center justify-center min-h-[300px]">
            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
            <h2 className="text-xl font-bold mb-2">Fehler beim Laden des Auftrags</h2>
            <p className="text-muted-foreground mb-6 text-center">
              Der angeforderte Auftrag konnte nicht geladen werden. Bitte versuchen Sie es später erneut.
            </p>
            <Button asChild>
              <Link href="/client/orders">Zurück zur Auftragsübersicht</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/client/orders">
              <ArrowLeft className="mr-2 h-4 w-4" /> Zurück zur Übersicht
            </Link>
          </Button>
          {getStatusBadge(order.status)}
        </div>
        <div className="flex gap-2">
          {order.status === "DRAFT" && (
            <Button>Auftrag veröffentlichen</Button>
          )}
          {order.status === "POSTED" && (
            <Button variant="outline">Auftrag bearbeiten</Button>
          )}
          {order.status === "PENDING_CLIENT_CONFIRMATION" && (
            <Button>Bestätigen</Button>
          )}
        </div>
      </div>

      <div>
        <h1 className="text-3xl font-bold">Auftrag {order.orderReference}</h1>
        <p className="text-muted-foreground mt-1">
          Erstellt am {format(new Date(order.createdAt), "dd. MMMM yyyy", { locale: de })}
        </p>
      </div>

      {/* Status-Karte */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Aktueller Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className="text-xl font-semibold">{order.status === "COMPLETED" ? "Abgeschlossen" : "Status: " + order.status}</div>
                {getStatusBadge(order.status)}
              </div>
              <p className="text-muted-foreground">{getStatusDescription(order.status)}</p>
              
              {/* Statusverlauf als Zeitleiste */}
              <div className="mt-6 space-y-4">
                <div className="relative pl-6 border-l-2 border-primary pb-4">
                  <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-primary"></div>
                  <div className="font-medium">Auftrag erstellt</div>
                  <div className="text-sm text-muted-foreground">
                    {format(new Date(order.createdAt), "dd.MM.yyyy HH:mm", { locale: de })}
                  </div>
                </div>
                
                {order.status !== "DRAFT" && (
                  <div className="relative pl-6 border-l-2 border-primary pb-4">
                    <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-primary"></div>
                    <div className="font-medium">Auftrag veröffentlicht</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.createdAt), "dd.MM.yyyy HH:mm", { locale: de })}
                    </div>
                  </div>
                )}
                
                {order.assignment && (
                  <div className="relative pl-6 border-l-2 border-primary pb-4">
                    <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-primary"></div>
                    <div className="font-medium">Fahrer zugewiesen</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.assignment.assignedAt), "dd.MM.yyyy HH:mm", { locale: de })}
                    </div>
                  </div>
                )}
                
                {order.assignment?.vehiclePickedUpAt && (
                  <div className="relative pl-6 border-l-2 border-primary pb-4">
                    <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-primary"></div>
                    <div className="font-medium">Fahrzeug abgeholt</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.assignment.vehiclePickedUpAt), "dd.MM.yyyy HH:mm", { locale: de })}
                    </div>
                  </div>
                )}
                
                {order.assignment?.vehicleDeliveredAt && (
                  <div className="relative pl-6 border-l-2 border-primary pb-4">
                    <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-primary"></div>
                    <div className="font-medium">Fahrzeug geliefert</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(order.assignment.vehicleDeliveredAt), "dd.MM.yyyy HH:mm", { locale: de })}
                    </div>
                  </div>
                )}
                
                {order.status === "COMPLETED" && (
                  <div className="relative pl-6">
                    <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-green-500"></div>
                    <div className="font-medium">Auftrag abgeschlossen</div>
                    <div className="text-sm text-muted-foreground">
                      {order.assignment?.completedAt 
                        ? format(new Date(order.assignment.completedAt), "dd.MM.yyyy HH:mm", { locale: de })
                        : format(new Date(order.updatedAt), "dd.MM.yyyy HH:mm", { locale: de })}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Fahrer-Informationen, falls zugewiesen */}
            {order.assignment && order.assignment.driver && (
              <div className="w-full md:w-64 bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2 flex items-center">
                  <User className="mr-2 h-4 w-4" /> Zugewiesener Fahrer
                </h3>
                <div className="space-y-2">
                  <div>
                    <div className="text-sm text-muted-foreground">Name:</div>
                    <div>{order.assignment.driver.firstName} {order.assignment.driver.lastName}</div>
                  </div>
                  {order.assignment.driver.averageRating && (
                    <div>
                      <div className="text-sm text-muted-foreground">Bewertung:</div>
                      <div className="flex items-center">
                        {order.assignment.driver.averageRating.toFixed(1)} / 5.0
                        <span className="text-yellow-500 ml-1">★</span>
                      </div>
                    </div>
                  )}
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full">
                      Fahrer kontaktieren
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tabs für Auftragsdetails */}
      <Tabs defaultValue="details">
        <TabsList className="mb-6">
          <TabsTrigger value="details">Auftragsdetails</TabsTrigger>
          <TabsTrigger value="vehicle">Fahrzeug</TabsTrigger>
          <TabsTrigger value="history">Verlauf</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Abholungsdaten */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <MapPin className="mr-2 h-5 w-5 text-primary" />
                  Abholung
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Adresse:</div>
                  <div>{order.pickupAddressLine1}</div>
                  {order.pickupAddressLine2 && <div>{order.pickupAddressLine2}</div>}
                  <div>{order.pickupPostalCode} {order.pickupCity}</div>
                  <div>{order.pickupCountry}</div>
                </div>
                
                {(order.pickupContactName || order.pickupContactPhone) && (
                  <div>
                    <div className="text-sm text-muted-foreground">Kontakt:</div>
                    {order.pickupContactName && <div>{order.pickupContactName}</div>}
                    {order.pickupContactPhone && <div>{order.pickupContactPhone}</div>}
                  </div>
                )}
                
                <div>
                  <div className="text-sm text-muted-foreground">Zeitraum:</div>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {format(new Date(order.pickupDateFrom), "dd.MM.yyyy", { locale: de })} - {format(new Date(order.pickupDateTo), "dd.MM.yyyy", { locale: de })}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Lieferungsdaten */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center">
                  <MapPin className="mr-2 h-5 w-5 text-primary" />
                  Lieferung
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Adresse:</div>
                  <div>{order.deliveryAddressLine1}</div>
                  {order.deliveryAddressLine2 && <div>{order.deliveryAddressLine2}</div>}
                  <div>{order.deliveryPostalCode} {order.deliveryCity}</div>
                  <div>{order.deliveryCountry}</div>
                </div>
                
                {(order.deliveryContactName || order.deliveryContactPhone) && (
                  <div>
                    <div className="text-sm text-muted-foreground">Kontakt:</div>
                    {order.deliveryContactName && <div>{order.deliveryContactName}</div>}
                    {order.deliveryContactPhone && <div>{order.deliveryContactPhone}</div>}
                  </div>
                )}
                
                <div>
                  <div className="text-sm text-muted-foreground">Zeitraum:</div>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {format(new Date(order.deliveryDateFrom), "dd.MM.yyyy", { locale: de })} - {format(new Date(order.deliveryDateTo), "dd.MM.yyyy", { locale: de })}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Preisdetails */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Preisdetails</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Geschätzter Preis:</span>
                    <span>{order.estimatedPrice} €</span>
                  </div>
                  {order.finalPrice && (
                    <div className="flex justify-between font-bold">
                      <span>Endpreis:</span>
                      <span>{order.finalPrice} €</span>
                    </div>
                  )}
                  <div className="text-sm text-muted-foreground pt-2">
                    Alle Preise inkl. MwSt.
                  </div>
                </div>
              </CardContent>
              {order.status === "COMPLETED" && !order.invoice && (
                <CardFooter>
                  <Button variant="outline" className="w-full">
                    Rechnung anfordern
                  </Button>
                </CardFooter>
              )}
              {order.invoice && (
                <CardFooter>
                  <Button className="w-full">
                    Rechnung herunterladen
                  </Button>
                </CardFooter>
              )}
            </Card>
            
            {/* Zusätzliche Informationen */}
            {order.specialInstructions && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Besondere Anweisungen</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{order.specialInstructions}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="vehicle">
          {order.vehicle ? (
            <Card>
              <CardHeader>
                <CardTitle>Fahrzeugdetails</CardTitle>
                <CardDescription>
                  Informationen zum transportierten Fahrzeug
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <div className="text-sm text-muted-foreground">Fahrzeugtyp:</div>
                      <div className="font-medium">{order.vehicle.vehicleType}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Marke & Modell:</div>
                      <div className="font-medium">{order.vehicle.make} {order.vehicle.model}</div>
                    </div>
                    {order.vehicle.year && (
                      <div>
                        <div className="text-sm text-muted-foreground">Baujahr:</div>
                        <div>{order.vehicle.year}</div>
                      </div>
                    )}
                    {order.vehicle.color && (
                      <div>
                        <div className="text-sm text-muted-foreground">Farbe:</div>
                        <div>{order.vehicle.color}</div>
                      </div>
                    )}
                  </div>
                  <div className="space-y-4">
                    <div>
                      <div className="text-sm text-muted-foreground">Fahrzeug-Identifikationsnummer (VIN):</div>
                      <div className="font-medium">{order.vehicle.vin}</div>
                    </div>
                    {order.vehicle.licensePlate && (
                      <div>
                        <div className="text-sm text-muted-foreground">Kennzeichen:</div>
                        <div>{order.vehicle.licensePlate}</div>
                      </div>
                    )}
                    {order.vehicle.notes && (
                      <div>
                        <div className="text-sm text-muted-foreground">Anmerkungen:</div>
                        <div>{order.vehicle.notes}</div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6 flex flex-col items-center justify-center min-h-[200px]">
                <Package className="h-12 w-12 text-muted-foreground mb-4" />
                <h2 className="text-xl font-bold mb-2">Keine Fahrzeugdaten</h2>
                <p className="text-muted-foreground text-center">
                  Für diesen Auftrag wurden keine Fahrzeugdaten hinterlegt.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Auftragsverlauf</CardTitle>
              <CardDescription>
                Chronologische Übersicht aller Ereignisse zu diesem Auftrag
              </CardDescription>
            </CardHeader>
            <CardContent>
              {eventsLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : events && events.length > 0 ? (
                <div className="space-y-4">
                  {events.map((event: any) => (
                    <div key={event.id} className="flex items-start gap-4 pb-4 border-b last:border-0">
                      <div className="rounded-full bg-muted p-2 mt-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="font-medium">{event.description}</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(event.eventTimestamp), "dd.MM.yyyy HH:mm", { locale: de })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Keine Ereignisse vorhanden</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
