"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { queryKeys } from "@/lib/queryKeys";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Filter, Plus, Search, Truck, Package, ArrowRight } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

// Hilfsfunktion für die Statusanzeige
const getStatusBadge = (status: string) => {
  switch (status) {
    case "DRAFT":
      return <Badge variant="outline">Entwurf</Badge>;
    case "POSTED":
      return <Badge variant="secondary">Ausgeschrieben</Badge>;
    case "DRIVER_ASSIGNED":
      return <Badge variant="secondary">Fahrer zugewiesen</Badge>;
    case "IN_TRANSIT":
      return <Badge className="bg-blue-500 hover:bg-blue-600">In Transport</Badge>;
    case "COMPLETED":
      return <Badge className="bg-green-500 hover:bg-green-600">Abgeschlossen</Badge>;
    case "CANCELLED":
      return <Badge variant="destructive">Storniert</Badge>;
    case "ISSUE_REPORTED":
      return <Badge variant="destructive">Problem gemeldet</Badge>;
    case "PENDING_CLIENT_CONFIRMATION":
      return <Badge variant="warning">Bestätigung erforderlich</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

/**
 * Komponente für die Auftragsübersicht des Geschäftskunden.
 * Zeigt eine Liste aller Aufträge mit Filtermöglichkeiten.
 */
export default function OrdersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Filter-Status aus URL-Parametern oder Standardwerte
  const [filters, setFilters] = useState({
    status: searchParams.get("status") || "",
    search: searchParams.get("search") || "",
    dateFrom: searchParams.get("dateFrom") ? new Date(searchParams.get("dateFrom") as string) : undefined,
    dateTo: searchParams.get("dateTo") ? new Date(searchParams.get("dateTo") as string) : undefined,
  });
  
  // Paginierung
  const [page, setPage] = useState(parseInt(searchParams.get("page") || "1"));
  const limit = 10;
  
  // Daten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: queryKeys.client.orders({ ...filters, page, limit }),
    queryFn: async () => {
      // URL-Parameter für die API-Anfrage erstellen
      const params = new URLSearchParams();
      if (filters.status) params.append("status", filters.status);
      if (filters.search) params.append("search", filters.search);
      if (filters.dateFrom) params.append("dateFrom", filters.dateFrom.toISOString());
      if (filters.dateTo) params.append("dateTo", filters.dateTo.toISOString());
      params.append("page", page.toString());
      params.append("limit", limit.toString());
      
      const response = await fetch(`/api/orders?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Auftragsdaten");
      }
      return response.json();
    },
  });
  
  // Filter anwenden und URL aktualisieren
  const applyFilters = () => {
    const params = new URLSearchParams();
    if (filters.status) params.append("status", filters.status);
    if (filters.search) params.append("search", filters.search);
    if (filters.dateFrom) params.append("dateFrom", filters.dateFrom.toISOString());
    if (filters.dateTo) params.append("dateTo", filters.dateTo.toISOString());
    params.append("page", "1"); // Bei Filteränderung zurück zur ersten Seite
    
    router.push(`/client/orders?${params.toString()}`);
    setPage(1);
  };
  
  // Filter zurücksetzen
  const resetFilters = () => {
    setFilters({
      status: "",
      search: "",
      dateFrom: undefined,
      dateTo: undefined,
    });
    router.push("/client/orders");
    setPage(1);
  };
  
  // Seitenwechsel
  const changePage = (newPage: number) => {
    setPage(newPage);
    
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", newPage.toString());
    
    router.push(`/client/orders?${params.toString()}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Meine Aufträge</h1>
          <p className="text-muted-foreground mt-1">
            Verwalten Sie Ihre Fahrzeugtransportaufträge
          </p>
        </div>
        <Button asChild>
          <Link href="/client/orders/create">
            <Plus className="mr-2 h-4 w-4" /> Neuen Auftrag erstellen
          </Link>
        </Button>
      </div>

      {/* Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filter
          </CardTitle>
          <CardDescription>
            Filtern Sie Ihre Aufträge nach verschiedenen Kriterien
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({ ...filters, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Alle Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Alle Status</SelectItem>
                  <SelectItem value="active">Aktive Aufträge</SelectItem>
                  <SelectItem value="completed">Abgeschlossene Aufträge</SelectItem>
                  <SelectItem value="pending">Ausstehende Aktionen</SelectItem>
                  <SelectItem value="POSTED">Ausgeschrieben</SelectItem>
                  <SelectItem value="DRIVER_ASSIGNED">Fahrer zugewiesen</SelectItem>
                  <SelectItem value="IN_TRANSIT">In Transport</SelectItem>
                  <SelectItem value="COMPLETED">Abgeschlossen</SelectItem>
                  <SelectItem value="CANCELLED">Storniert</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="dateFrom">Von Datum</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateFrom"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateFrom ? (
                      format(filters.dateFrom, "PPP", { locale: de })
                    ) : (
                      <span>Datum auswählen</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateFrom}
                    onSelect={(date) => setFilters({ ...filters, dateFrom: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="dateTo">Bis Datum</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="dateTo"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !filters.dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filters.dateTo ? (
                      format(filters.dateTo, "PPP", { locale: de })
                    ) : (
                      <span>Datum auswählen</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={filters.dateTo}
                    onSelect={(date) => setFilters({ ...filters, dateTo: date })}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="search">Suche</Label>
              <div className="flex">
                <Input
                  id="search"
                  placeholder="Auftragsnr., Fahrzeug, Ort..."
                  value={filters.search}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                  className="rounded-r-none"
                />
                <Button 
                  type="button" 
                  variant="default" 
                  onClick={applyFilters}
                  className="rounded-l-none"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={resetFilters}>
            Filter zurücksetzen
          </Button>
          <Button onClick={applyFilters}>
            Filter anwenden
          </Button>
        </CardFooter>
      </Card>

      {/* Auftragsliste */}
      <Card>
        <CardHeader className="pb-0">
          <CardTitle>Aufträge</CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <p>Fehler beim Laden der Daten. Bitte versuchen Sie es später erneut.</p>
            </div>
          ) : data?.orders?.length > 0 ? (
            <>
              <div className="rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Auftragsnr.</TableHead>
                      <TableHead>Fahrzeug</TableHead>
                      <TableHead>Route</TableHead>
                      <TableHead>Datum</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Preis</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.orders.map((order: any) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.orderReference}</TableCell>
                        <TableCell>
                          {order.vehicle ? (
                            <div className="flex items-center">
                              <Package className="mr-2 h-4 w-4 text-muted-foreground" />
                              <span>{order.vehicle.make} {order.vehicle.model}</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Nicht angegeben</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Truck className="mr-2 h-4 w-4 text-muted-foreground" />
                            <div className="flex flex-col">
                              <span className="text-xs text-muted-foreground">Von:</span>
                              <span>{order.pickupCity}</span>
                              <span className="text-xs text-muted-foreground mt-1">Nach:</span>
                              <span>{order.deliveryCity}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs text-muted-foreground">Abholung:</span>
                            <span>{format(new Date(order.pickupDateFrom), "dd.MM.yyyy")}</span>
                            <span className="text-xs text-muted-foreground mt-1">Lieferung:</span>
                            <span>{format(new Date(order.deliveryDateFrom), "dd.MM.yyyy")}</span>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(order.status)}</TableCell>
                        <TableCell className="text-right">
                          {order.finalPrice ? (
                            <span className="font-medium">{order.finalPrice} €</span>
                          ) : order.estimatedPrice ? (
                            <span>{order.estimatedPrice} € (geschätzt)</span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/client/orders/${order.id}`}>
                              Details <ArrowRight className="ml-2 h-4 w-4" />
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {/* Paginierung */}
              {data.pagination && data.pagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-muted-foreground">
                    Zeige {(page - 1) * limit + 1} bis {Math.min(page * limit, data.pagination.total)} von {data.pagination.total} Einträgen
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => changePage(page - 1)}
                      disabled={page <= 1}
                    >
                      Zurück
                    </Button>
                    {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1)
                      .filter(p => Math.abs(p - page) < 2 || p === 1 || p === data.pagination.totalPages)
                      .map((p, i, arr) => (
                        <React.Fragment key={p}>
                          {i > 0 && arr[i - 1] !== p - 1 && (
                            <Button variant="outline" size="sm" disabled>
                              ...
                            </Button>
                          )}
                          <Button
                            variant={p === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => changePage(p)}
                          >
                            {p}
                          </Button>
                        </React.Fragment>
                      ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => changePage(page + 1)}
                      disabled={page >= data.pagination.totalPages}
                    >
                      Weiter
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Keine Aufträge gefunden</h3>
              <p className="text-muted-foreground mb-6">
                Es wurden keine Aufträge gefunden, die Ihren Filterkriterien entsprechen.
              </p>
              <div className="flex justify-center gap-4">
                <Button variant="outline" onClick={resetFilters}>
                  Filter zurücksetzen
                </Button>
                <Button asChild>
                  <Link href="/client/orders/create">
                    Neuen Auftrag erstellen
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
