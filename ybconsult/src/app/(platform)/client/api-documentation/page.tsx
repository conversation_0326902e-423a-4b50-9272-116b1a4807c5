"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

export default function ApiDocumentationPage() {
  const [activeTab, setActiveTab] = useState("overview");
  
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success("Code in die Zwischenablage kopiert");
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">API-Dokumentation</h1>
        <p className="text-gray-500">
          Integrieren Sie YoungMobility in Ihre eigenen Systeme mit unserer REST API
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="md:col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Inhaltsverzeichnis</CardTitle>
            </CardHeader>
            <CardContent>
              <nav className="space-y-1">
                <a 
                  href="#overview" 
                  className={`block px-3 py-2 rounded-md ${activeTab === "overview" ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"}`}
                  onClick={() => setActiveTab("overview")}
                >
                  Übersicht
                </a>
                <a 
                  href="#authentication" 
                  className={`block px-3 py-2 rounded-md ${activeTab === "authentication" ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"}`}
                  onClick={() => setActiveTab("authentication")}
                >
                  Authentifizierung
                </a>
                <a 
                  href="#orders" 
                  className={`block px-3 py-2 rounded-md ${activeTab === "orders" ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"}`}
                  onClick={() => setActiveTab("orders")}
                >
                  Aufträge
                </a>
                <a 
                  href="#errors" 
                  className={`block px-3 py-2 rounded-md ${activeTab === "errors" ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"}`}
                  onClick={() => setActiveTab("errors")}
                >
                  Fehlerbehandlung
                </a>
                <a 
                  href="#rate-limits" 
                  className={`block px-3 py-2 rounded-md ${activeTab === "rate-limits" ? "bg-primary text-primary-foreground" : "hover:bg-gray-100"}`}
                  onClick={() => setActiveTab("rate-limits")}
                >
                  Rate Limits
                </a>
                <div className="pt-4">
                  <Link href="/client/api-keys">
                    <Button variant="outline" className="w-full">
                      API-Schlüssel verwalten
                    </Button>
                  </Link>
                </div>
              </nav>
            </CardContent>
          </Card>
        </div>
        
        {/* Main content */}
        <div className="md:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            {/* Overview */}
            <TabsContent value="overview" id="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Übersicht</CardTitle>
                  <CardDescription>
                    Einführung in die YoungMobility API
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>
                    Die YoungMobility API ermöglicht es Ihnen, Ihre Systeme direkt mit unserer Plattform zu integrieren.
                    Sie können Aufträge erstellen, den Status verfolgen und vieles mehr.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">Basis-URL</h3>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200 flex justify-between items-center">
                    <code className="text-sm">https://api.youngmobility.com/v1</code>
                    <Button variant="ghost" size="sm" onClick={() => handleCopyCode("https://api.youngmobility.com/v1")}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <h3 className="text-lg font-medium mt-6">Anforderungsformat</h3>
                  <p>
                    Alle API-Anfragen müssen über HTTPS gestellt werden und im JSON-Format sein.
                    Die Antworten werden ebenfalls im JSON-Format zurückgegeben.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">Versionen</h3>
                  <p>
                    Die aktuelle API-Version ist <code>v1</code>. Wir empfehlen, die Versionsnummer immer explizit in der URL anzugeben,
                    um sicherzustellen, dass Ihre Integration auch bei zukünftigen API-Updates funktioniert.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Authentication */}
            <TabsContent value="authentication" id="authentication" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Authentifizierung</CardTitle>
                  <CardDescription>
                    Wie Sie sich bei der API authentifizieren
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>
                    Die YoungMobility API verwendet API-Schlüssel für die Authentifizierung. Jeder Schlüssel ist einem bestimmten Kunden zugeordnet.
                    Sie können Ihre API-Schlüssel in Ihrem <Link href="/client/api-keys" className="text-primary hover:underline">Kundenkonto</Link> verwalten.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">API-Schlüssel im Header</h3>
                  <p>
                    Fügen Sie Ihren API-Schlüssel zu jeder Anfrage im <code>Authorization</code>-Header hinzu:
                  </p>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200 flex justify-between items-center">
                    <code className="text-sm">Authorization: Bearer YOUR_API_KEY</code>
                    <Button variant="ghost" size="sm" onClick={() => handleCopyCode("Authorization: Bearer YOUR_API_KEY")}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <h3 className="text-lg font-medium mt-6">Beispiel mit cURL</h3>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="absolute top-2 right-2"
                      onClick={() => handleCopyCode('curl -X GET "https://api.youngmobility.com/v1/orders" \\\n  -H "Authorization: Bearer YOUR_API_KEY" \\\n  -H "Content-Type: application/json"')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <pre className="text-sm overflow-x-auto">
{`curl -X GET "https://api.youngmobility.com/v1/orders" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`}
                    </pre>
                  </div>
                  
                  <div className="bg-amber-50 p-4 rounded-md border border-amber-200 mt-6">
                    <h4 className="font-medium text-amber-800">Sicherheitshinweis</h4>
                    <p className="text-amber-700 mt-1">
                      Behandeln Sie Ihre API-Schlüssel wie Passwörter. Teilen Sie sie nicht öffentlich und speichern Sie sie sicher.
                      Bei Verdacht auf Kompromittierung sollten Sie den Schlüssel sofort erneuern.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Orders */}
            <TabsContent value="orders" id="orders" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Aufträge</CardTitle>
                  <CardDescription>
                    Endpunkte für die Verwaltung von Aufträgen
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">Auftrag erstellen</h3>
                    <p className="text-gray-500 mb-4">Erstellen Sie einen neuen Auftrag</p>
                    
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <code className="text-sm font-bold">POST /v1/orders</code>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleCopyCode('POST /v1/orders')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <h4 className="font-medium mb-2">Request Body</h4>
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative mb-4">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="absolute top-2 right-2"
                        onClick={() => handleCopyCode(JSON.stringify({
                          title: "Fahrzeugüberführung BMW X5",
                          description: "Überführung eines Neuwagens",
                          vehicleType: "SUV",
                          vehicleMake: "BMW",
                          vehicleModel: "X5",
                          vehicleYear: 2023,
                          pickupAddress: "Beispielstraße 1",
                          pickupCity: "München",
                          pickupPostalCode: "80331",
                          pickupCountry: "Deutschland",
                          pickupDate: "2023-12-01T10:00:00Z",
                          deliveryAddress: "Musterweg 42",
                          deliveryCity: "Berlin",
                          deliveryPostalCode: "10115",
                          deliveryCountry: "Deutschland",
                          deliveryDate: "2023-12-02T14:00:00Z",
                          specialInstructions: "Bitte vorsichtig fahren, Neuwagen",
                          urgency: "STANDARD",
                          externalReference: "ERP-12345"
                        }, null, 2))}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <pre className="text-sm overflow-x-auto">
{`{
  "title": "Fahrzeugüberführung BMW X5",
  "description": "Überführung eines Neuwagens",
  "vehicleType": "SUV",
  "vehicleMake": "BMW",
  "vehicleModel": "X5",
  "vehicleYear": 2023,
  "pickupAddress": "Beispielstraße 1",
  "pickupCity": "München",
  "pickupPostalCode": "80331",
  "pickupCountry": "Deutschland",
  "pickupDate": "2023-12-01T10:00:00Z",
  "deliveryAddress": "Musterweg 42",
  "deliveryCity": "Berlin",
  "deliveryPostalCode": "10115",
  "deliveryCountry": "Deutschland",
  "deliveryDate": "2023-12-02T14:00:00Z",
  "specialInstructions": "Bitte vorsichtig fahren, Neuwagen",
  "urgency": "STANDARD",
  "externalReference": "ERP-12345"
}`}
                      </pre>
                    </div>
                    
                    <h4 className="font-medium mb-2">Response (201 Created)</h4>
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative">
                      <pre className="text-sm overflow-x-auto">
{`{
  "message": "Order created successfully",
  "orderId": "clq1a2b3c4d5e6f7g8h9i0",
  "status": "POSTED"
}`}
                      </pre>
                    </div>
                  </div>
                  
                  <div className="pt-6 border-t">
                    <h3 className="text-lg font-medium">Aufträge abrufen</h3>
                    <p className="text-gray-500 mb-4">Liste aller Aufträge abrufen</p>
                    
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <code className="text-sm font-bold">GET /v1/orders</code>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleCopyCode('GET /v1/orders')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <h4 className="font-medium mb-2">Query Parameter</h4>
                    <ul className="list-disc pl-5 mb-4 space-y-2">
                      <li><code>status</code> - Filtert nach Auftragsstatus (optional)</li>
                      <li><code>page</code> - Seitennummer für Paginierung (Standard: 1)</li>
                      <li><code>limit</code> - Anzahl der Ergebnisse pro Seite (Standard: 10, Max: 100)</li>
                    </ul>
                    
                    <h4 className="font-medium mb-2">Response (200 OK)</h4>
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative">
                      <pre className="text-sm overflow-x-auto">
{`{
  "orders": [
    {
      "id": "clq1a2b3c4d5e6f7g8h9i0",
      "title": "Fahrzeugüberführung BMW X5",
      "status": "POSTED",
      "pickupDate": "2023-12-01T10:00:00Z",
      "deliveryDate": "2023-12-02T14:00:00Z",
      "externalReference": "ERP-12345",
      // ... weitere Auftragsdaten
    },
    // ... weitere Aufträge
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "totalCount": 42,
    "totalPages": 5
  }
}`}
                      </pre>
                    </div>
                  </div>
                  
                  <div className="pt-6 border-t">
                    <h3 className="text-lg font-medium">Auftrag abrufen</h3>
                    <p className="text-gray-500 mb-4">Details eines bestimmten Auftrags abrufen</p>
                    
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <code className="text-sm font-bold">GET /v1/orders/{"{orderId}"}</code>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleCopyCode('GET /v1/orders/{orderId}')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <h4 className="font-medium mb-2">Response (200 OK)</h4>
                    <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative">
                      <pre className="text-sm overflow-x-auto">
{`{
  "id": "clq1a2b3c4d5e6f7g8h9i0",
  "title": "Fahrzeugüberführung BMW X5",
  "description": "Überführung eines Neuwagens",
  "vehicleType": "SUV",
  "vehicleMake": "BMW",
  "vehicleModel": "X5",
  "vehicleYear": 2023,
  "pickupAddress": "Beispielstraße 1",
  "pickupCity": "München",
  "pickupPostalCode": "80331",
  "pickupCountry": "Deutschland",
  "pickupDate": "2023-12-01T10:00:00Z",
  "deliveryAddress": "Musterweg 42",
  "deliveryCity": "Berlin",
  "deliveryPostalCode": "10115",
  "deliveryCountry": "Deutschland",
  "deliveryDate": "2023-12-02T14:00:00Z",
  "status": "ASSIGNED",
  "externalReference": "ERP-12345",
  "assignment": {
    "id": "clq9z8y7x6w5v4u3t2s1r0",
    "status": "ACCEPTED",
    "assignedAt": "2023-11-25T14:30:00Z",
    "acceptedAt": "2023-11-25T15:45:00Z",
    "driverProfile": {
      "firstName": "Max",
      "lastName": "Mustermann",
      "phoneNumber": "+49123456789"
    }
  },
  // ... weitere Auftragsdaten
}`}
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Errors */}
            <TabsContent value="errors" id="errors" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Fehlerbehandlung</CardTitle>
                  <CardDescription>
                    Wie Fehler in der API behandelt werden
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>
                    Die YoungMobility API verwendet Standard-HTTP-Statuscodes, um den Erfolg oder Misserfolg einer API-Anfrage anzuzeigen.
                    Im Allgemeinen bedeuten Codes im Bereich 2xx Erfolg, Codes im Bereich 4xx weisen auf einen Fehler hin, der durch die
                    bereitgestellten Informationen verursacht wurde, und Codes im Bereich 5xx weisen auf einen Serverfehler hin.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">Häufige Fehlercodes</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-4">Status Code</th>
                          <th className="text-left py-2 px-4">Bedeutung</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>400</code></td>
                          <td className="py-2 px-4">Bad Request – Die Anfrage ist ungültig</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>401</code></td>
                          <td className="py-2 px-4">Unauthorized – API-Schlüssel fehlt oder ist ungültig</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>403</code></td>
                          <td className="py-2 px-4">Forbidden – Keine Berechtigung für diese Aktion</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>404</code></td>
                          <td className="py-2 px-4">Not Found – Die angeforderte Ressource existiert nicht</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>429</code></td>
                          <td className="py-2 px-4">Too Many Requests – Rate Limit überschritten</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>500</code></td>
                          <td className="py-2 px-4">Internal Server Error – Ein Serverfehler ist aufgetreten</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <h3 className="text-lg font-medium mt-6">Fehlerantwortformat</h3>
                  <p>
                    Fehlerantworten enthalten eine Nachricht, die den Fehler beschreibt, und manchmal zusätzliche Details:
                  </p>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200 relative">
                    <pre className="text-sm overflow-x-auto">
{`{
  "message": "Invalid input data",
  "errors": {
    "vehicleType": ["Invalid vehicle type"],
    "pickupDate": ["Invalid date format"]
  }
}`}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Rate Limits */}
            <TabsContent value="rate-limits" id="rate-limits" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Rate Limits</CardTitle>
                  <CardDescription>
                    Beschränkungen für API-Anfragen
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p>
                    Um die Stabilität unserer API zu gewährleisten, haben wir Rate Limits implementiert.
                    Diese begrenzen die Anzahl der Anfragen, die Sie in einem bestimmten Zeitraum stellen können.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">Standardlimits</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    <li>100 Anfragen pro Minute</li>
                    <li>5.000 Anfragen pro Tag</li>
                  </ul>
                  
                  <p className="mt-4">
                    Wenn Sie diese Limits überschreiten, erhalten Sie eine <code>429 Too Many Requests</code>-Antwort.
                    Die Antwort enthält Header, die Ihnen mitteilen, wie lange Sie warten müssen, bevor Sie weitere Anfragen stellen können.
                  </p>
                  
                  <h3 className="text-lg font-medium mt-6">Rate-Limit-Header</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-4">Header</th>
                          <th className="text-left py-2 px-4">Beschreibung</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>X-RateLimit-Limit</code></td>
                          <td className="py-2 px-4">Maximale Anzahl von Anfragen im aktuellen Zeitfenster</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>X-RateLimit-Remaining</code></td>
                          <td className="py-2 px-4">Verbleibende Anfragen im aktuellen Zeitfenster</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2 px-4"><code>X-RateLimit-Reset</code></td>
                          <td className="py-2 px-4">Zeit in Sekunden, bis das Limit zurückgesetzt wird</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <div className="bg-blue-50 p-4 rounded-md border border-blue-200 mt-6">
                    <h4 className="font-medium text-blue-800">Höhere Limits</h4>
                    <p className="text-blue-700 mt-1">
                      Wenn Sie höhere Rate Limits benötigen, kontaktieren Sie bitte unseren Support.
                      Wir können individuelle Limits für Geschäftskunden mit hohem Volumen einrichten.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
