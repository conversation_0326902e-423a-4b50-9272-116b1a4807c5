"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import { format, addMonths } from "date-fns";
import { de } from "date-fns/locale";
import { Copy, Key, Loader2, Plus, RefreshCw, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

// Types
interface ApiKey {
  id: string;
  key: string;
  name: string;
  isActive: boolean;
  expiresAt: string | null;
  lastUsedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function ApiKeysPage() {
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState("");
  const [expiryMonths, setExpiryMonths] = useState("12");
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  
  // Fetch API keys
  const { data: apiKeys, isLoading, refetch } = useQuery<ApiKey[]>({
    queryKey: ["apiKeys"],
    queryFn: async () => {
      const response = await fetch("/api/client/api-keys");
      
      if (!response.ok) {
        throw new Error("Failed to fetch API keys");
      }
      
      return response.json();
    },
  });
  
  // Create API key mutation
  const createKeyMutation = useMutation({
    mutationFn: async (data: { name: string; expiryMonths: number }) => {
      const response = await fetch("/api/client/api-keys", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error("Failed to create API key");
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["apiKeys"] });
      setNewApiKey(data.key);
      toast.success("API-Schlüssel erfolgreich erstellt");
    },
    onError: (error) => {
      console.error("Error creating API key:", error);
      toast.error("Fehler beim Erstellen des API-Schlüssels");
    },
  });
  
  // Update API key mutation
  const updateKeyMutation = useMutation({
    mutationFn: async (data: { id: string; isActive: boolean }) => {
      const response = await fetch(`/api/client/api-keys/${data.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isActive: data.isActive }),
      });
      
      if (!response.ok) {
        throw new Error("Failed to update API key");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["apiKeys"] });
      toast.success("API-Schlüssel erfolgreich aktualisiert");
    },
    onError: (error) => {
      console.error("Error updating API key:", error);
      toast.error("Fehler beim Aktualisieren des API-Schlüssels");
    },
  });
  
  // Delete API key mutation
  const deleteKeyMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/client/api-keys/${id}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        throw new Error("Failed to delete API key");
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["apiKeys"] });
      toast.success("API-Schlüssel erfolgreich gelöscht");
    },
    onError: (error) => {
      console.error("Error deleting API key:", error);
      toast.error("Fehler beim Löschen des API-Schlüssels");
    },
  });
  
  // Handle create API key
  const handleCreateKey = () => {
    if (!newKeyName.trim()) {
      toast.error("Bitte geben Sie einen Namen für den API-Schlüssel ein");
      return;
    }
    
    createKeyMutation.mutate({
      name: newKeyName.trim(),
      expiryMonths: parseInt(expiryMonths),
    });
  };
  
  // Handle toggle API key status
  const handleToggleKeyStatus = (key: ApiKey) => {
    updateKeyMutation.mutate({
      id: key.id,
      isActive: !key.isActive,
    });
  };
  
  // Handle delete API key
  const handleDeleteKey = (id: string) => {
    if (confirm("Sind Sie sicher, dass Sie diesen API-Schlüssel löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.")) {
      deleteKeyMutation.mutate(id);
    }
  };
  
  // Handle copy API key
  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key);
    toast.success("API-Schlüssel in die Zwischenablage kopiert");
  };
  
  // Reset create dialog
  const resetCreateDialog = () => {
    setNewKeyName("");
    setExpiryMonths("12");
    setNewApiKey(null);
    setIsCreateDialogOpen(false);
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold">API-Schlüssel</h1>
          <p className="text-gray-500">Verwalten Sie Ihre API-Schlüssel für die Integration mit externen Systemen</p>
        </div>
        <div className="flex gap-2 mt-4 md:mt-0">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Aktualisieren
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Neuer API-Schlüssel
          </Button>
        </div>
      </div>
      
      {/* API Keys List */}
      <Card>
        <CardHeader>
          <CardTitle>Ihre API-Schlüssel</CardTitle>
          <CardDescription>
            API-Schlüssel ermöglichen den Zugriff auf die YoungMobility API für die Integration mit Ihren Systemen.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto" />
              <p className="mt-2">API-Schlüssel werden geladen...</p>
            </div>
          ) : apiKeys && apiKeys.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Name</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Erstellt am</th>
                    <th className="text-left py-3 px-4">Läuft ab am</th>
                    <th className="text-left py-3 px-4">Zuletzt verwendet</th>
                    <th className="text-left py-3 px-4">Aktionen</th>
                  </tr>
                </thead>
                <tbody>
                  {apiKeys.map((key) => (
                    <tr key={key.id} className="border-b">
                      <td className="py-3 px-4">{key.name}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={key.isActive}
                            onCheckedChange={() => handleToggleKeyStatus(key)}
                          />
                          <Badge className={key.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                            {key.isActive ? "Aktiv" : "Inaktiv"}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {format(new Date(key.createdAt), "dd.MM.yyyy", { locale: de })}
                      </td>
                      <td className="py-3 px-4">
                        {key.expiresAt
                          ? format(new Date(key.expiresAt), "dd.MM.yyyy", { locale: de })
                          : "Nie"}
                      </td>
                      <td className="py-3 px-4">
                        {key.lastUsedAt
                          ? format(new Date(key.lastUsedAt), "dd.MM.yyyy HH:mm", { locale: de })
                          : "Nie verwendet"}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteKey(key.id)}
                            title="API-Schlüssel löschen"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Key className="h-12 w-12 text-gray-300 mx-auto" />
              <p className="mt-4 text-gray-500">Sie haben noch keine API-Schlüssel erstellt.</p>
              <Button className="mt-4" onClick={() => setIsCreateDialogOpen(true)}>
                Ersten API-Schlüssel erstellen
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Create API Key Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{newApiKey ? "API-Schlüssel erstellt" : "Neuen API-Schlüssel erstellen"}</DialogTitle>
            <DialogDescription>
              {newApiKey
                ? "Kopieren Sie Ihren neuen API-Schlüssel. Aus Sicherheitsgründen wird er nur einmal angezeigt."
                : "Erstellen Sie einen neuen API-Schlüssel für die Integration mit externen Systemen."}
            </DialogDescription>
          </DialogHeader>
          
          {newApiKey ? (
            <div className="space-y-4 py-4">
              <div className="bg-gray-50 p-3 rounded-md border border-gray-200 break-all">
                <code className="text-sm">{newApiKey}</code>
              </div>
              <p className="text-amber-600 text-sm">
                <strong>Wichtig:</strong> Kopieren Sie diesen Schlüssel jetzt. Er wird aus Sicherheitsgründen nicht mehr angezeigt.
              </p>
              <Button
                className="w-full"
                onClick={() => handleCopyKey(newApiKey)}
              >
                <Copy className="h-4 w-4 mr-2" />
                In Zwischenablage kopieren
              </Button>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="key-name">Name</Label>
                <Input
                  id="key-name"
                  placeholder="z.B. ERP-Integration"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                />
                <p className="text-sm text-gray-500">
                  Ein beschreibender Name, um den Zweck dieses API-Schlüssels zu identifizieren.
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="expiry">Gültigkeitsdauer</Label>
                <Select value={expiryMonths} onValueChange={setExpiryMonths}>
                  <SelectTrigger id="expiry">
                    <SelectValue placeholder="Wählen Sie eine Gültigkeitsdauer" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">3 Monate</SelectItem>
                    <SelectItem value="6">6 Monate</SelectItem>
                    <SelectItem value="12">12 Monate</SelectItem>
                    <SelectItem value="24">24 Monate</SelectItem>
                    <SelectItem value="0">Unbegrenzt</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">
                  Aus Sicherheitsgründen empfehlen wir, API-Schlüssel regelmäßig zu erneuern.
                </p>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex space-x-2 justify-end">
            <Button variant="outline" onClick={resetCreateDialog}>
              {newApiKey ? "Schließen" : "Abbrechen"}
            </Button>
            {!newApiKey && (
              <Button
                onClick={handleCreateKey}
                disabled={createKeyMutation.isPending || !newKeyName.trim()}
              >
                {createKeyMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Wird erstellt...
                  </>
                ) : (
                  "API-Schlüssel erstellen"
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
