"use client";

import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { GradientButton } from "@/components/ui/gradient-button";
import { Skeleton } from "@/components/ui/skeleton";
import { queryKeys } from "@/lib/queryKeys";
import { useRouter } from "next/navigation";
import { CalendarClock, Package, Clock, AlertCircle, Plus, TrendingUp } from "lucide-react";
import Link from "next/link";

/**
 * Dashboard-Seite für Geschäftskunden.
 * Zeigt eine Übersicht über Aufträge, Aktivitäten und Aktionen.
 */
export default function ClientDashboard() {
  const router = useRouter();

  // Daten für das Dashboard abrufen
  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: queryKeys.client.dashboard(),
    queryFn: async () => {
      const response = await fetch('/api/client/dashboard');
      if (!response.ok) {
        throw new Error('<PERSON><PERSON> beim <PERSON>den der Dashboard-Daten');
      }
      return response.json();
    },
  });

  // Platzhalter-Daten für den Fall, dass noch keine echten Daten vorhanden sind
  const placeholderData = {
    activeOrders: 0,
    pendingActions: 0,
    recentActivity: [],
  };

  // Daten, die angezeigt werden sollen (echte oder Platzhalter)
  const displayData = dashboardData || placeholderData;

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <GradientButton asChild>
          <Link href="/client/orders/create">
            <Plus className="mr-2 h-4 w-4" /> Neuen Auftrag erstellen
          </Link>
        </GradientButton>
      </div>

      {/* Übersichts-Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Aktive Aufträge */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Package className="mr-2 h-5 w-5 text-primary" />
              Aktive Aufträge
            </CardTitle>
            <CardDescription>Laufende Fahrzeugtransporte</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-14 w-full" />
            ) : (
              <div className="text-3xl font-bold">{displayData.activeOrders}</div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" asChild className="w-full justify-start">
              <Link href="/client/orders?status=active">
                Alle aktiven Aufträge anzeigen
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Anstehende Aktionen */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <AlertCircle className="mr-2 h-5 w-5 text-amber-500" />
              Anstehende Aktionen
            </CardTitle>
            <CardDescription>Aufträge, die Ihre Aufmerksamkeit benötigen</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-14 w-full" />
            ) : (
              <div className="text-3xl font-bold">{displayData.pendingActions}</div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" asChild className="w-full justify-start">
              <Link href="/client/orders?status=pending">
                Anstehende Aktionen anzeigen
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Statistik */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
              Statistik
            </CardTitle>
            <CardDescription>Übersicht Ihrer Aktivitäten</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-14 w-full" />
            ) : (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Abgeschlossene Aufträge</span>
                  <span className="font-medium">0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Durchschnittliche Bewertung</span>
                  <span className="font-medium">-</span>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" asChild className="w-full justify-start">
              <Link href="/client/orders?status=completed">
                Abgeschlossene Aufträge anzeigen
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Letzte Aktivitäten */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Letzte Aktivitäten</h2>
        <Card>
          <CardContent className="p-6">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : displayData.recentActivity && displayData.recentActivity.length > 0 ? (
              <ul className="space-y-4">
                {displayData.recentActivity.map((activity, index) => (
                  <li key={index} className="flex items-start gap-4 pb-4 border-b last:border-0">
                    <div className="rounded-full bg-primary/10 p-2">
                      <Clock className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">{activity.description}</p>
                      <p className="text-sm text-muted-foreground">{activity.timestamp}</p>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Keine Aktivitäten vorhanden</p>
                <Button variant="outline" className="mt-4" asChild>
                  <Link href="/client/orders/create">
                    Ersten Auftrag erstellen
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
