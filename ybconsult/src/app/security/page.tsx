"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ConsentManager } from "@/components/gdpr/ConsentManager";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { sanitizeHtml, containsMaliciousCode, anonymizeEmail } from "@/utils/security";
import { anonymizePersonalData } from "@/utils/gdpr";

/**
 * Security & GDPR Showcase Page
 * Teil der Phase 8: Security Hardening & GDPR Compliance Audit (YM-803)
 */
export default function SecurityPage() {
  const [sanitizedOutput, setSanitizedOutput] = useState("");
  const [anonymizedData, setAnonymizedData] = useState<any>(null);
  
  // Zod Schema für das Kontaktformular
  const contactFormSchema = z.object({
    name: z.string().min(2, "Name muss mindestens 2 Zeichen lang sein"),
    email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein"),
    phone: z.string().optional(),
    message: z.string().min(10, "Nachricht muss mindestens 10 Zeichen lang sein"),
    consent: z.boolean().refine(val => val === true, {
      message: "Sie müssen der Datenschutzerklärung zustimmen",
    }),
  });
  
  type ContactFormData = z.infer<typeof contactFormSchema>;
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      message: "",
      consent: false,
    },
  });
  
  const onSubmit = (data: ContactFormData) => {
    console.log("Form submitted:", data);
    
    // Anonymisiere die Daten für die Anzeige
    const anonymized = anonymizePersonalData({
      name: data.name,
      email: data.email,
      phone: data.phone,
      message: data.message,
    });
    
    setAnonymizedData(anonymized);
    reset();
  };
  
  const handleSanitizeInput = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const input = form.elements.namedItem("htmlInput") as HTMLTextAreaElement;
    
    if (input) {
      const html = input.value;
      
      // Prüfe auf schädlichen Code
      if (containsMaliciousCode(html)) {
        setSanitizedOutput("⚠️ Potenziell schädlicher Code erkannt! Der Inhalt wurde blockiert.");
        return;
      }
      
      // Sanitiere den HTML-Input
      const sanitized = sanitizeHtml(html);
      setSanitizedOutput(sanitized);
    }
  };
  
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-center mb-8">Sicherheit & DSGVO-Konformität</h1>
      <p className="text-center mb-12 max-w-3xl mx-auto">
        Diese Seite demonstriert verschiedene Sicherheits- und DSGVO-Funktionen, die im Rahmen von Phase 8 implementiert wurden.
      </p>
      
      {/* Eingabevalidierung */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Sichere Eingabevalidierung</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Kontaktformular mit Zod-Validierung</CardTitle>
              <CardDescription>Demonstriert sichere Eingabevalidierung mit Zod</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-1">Name</label>
                  <Input
                    id="name"
                    className="quechua-input w-full"
                    {...register("name")}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-1">E-Mail</label>
                  <Input
                    id="email"
                    type="email"
                    className="quechua-input w-full"
                    {...register("email")}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium mb-1">Telefon (optional)</label>
                  <Input
                    id="phone"
                    className="quechua-input w-full"
                    {...register("phone")}
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-1">Nachricht</label>
                  <Textarea
                    id="message"
                    className="quechua-input w-full"
                    rows={4}
                    {...register("message")}
                  />
                  {errors.message && (
                    <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
                  )}
                </div>
                
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="consent"
                    className="mt-1 mr-2"
                    {...register("consent")}
                  />
                  <label htmlFor="consent" className="text-sm">
                    Ich stimme der <a href="/privacy-policy" className="text-quechua-dark-green hover:underline">Datenschutzerklärung</a> zu und bin damit einverstanden, dass meine Daten zur Bearbeitung meiner Anfrage verarbeitet werden.
                  </label>
                </div>
                {errors.consent && (
                  <p className="text-red-500 text-xs">{errors.consent.message}</p>
                )}
                
                <GradientButton type="submit">
                  Nachricht senden
                </GradientButton>
              </form>
            </CardContent>
          </Card>
          
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Anonymisierte Daten</CardTitle>
              <CardDescription>DSGVO-konforme Datenspeicherung</CardDescription>
            </CardHeader>
            <CardContent>
              {anonymizedData ? (
                <div className="space-y-4">
                  <p>So würden Ihre Daten in unserer Datenbank gespeichert:</p>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                    {JSON.stringify(anonymizedData, null, 2)}
                  </pre>
                  <p className="text-sm text-gray-500">
                    Gemäß DSGVO werden personenbezogene Daten anonymisiert oder pseudonymisiert, wenn sie nicht mehr für den ursprünglichen Zweck benötigt werden.
                  </p>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <p>Füllen Sie das Formular aus, um zu sehen, wie Ihre Daten anonymisiert werden.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </section>
      
      {/* HTML-Sanitisierung */}
      <section className="mb-16">
        <h2 className="text-center mb-8">HTML-Sanitisierung</h2>
        <Card className="quechua-card">
          <CardHeader>
            <CardTitle>XSS-Schutz durch HTML-Sanitisierung</CardTitle>
            <CardDescription>Schutz vor Cross-Site Scripting (XSS) Angriffen</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSanitizeInput} className="space-y-4">
              <div>
                <label htmlFor="htmlInput" className="block text-sm font-medium mb-1">HTML-Eingabe</label>
                <p className="text-sm text-gray-500 mb-2">
                  Versuchen Sie, schädlichen Code einzugeben, z.B. <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code>
                </p>
                <Textarea
                  id="htmlInput"
                  name="htmlInput"
                  className="quechua-input w-full"
                  rows={4}
                  placeholder="Geben Sie HTML-Code ein..."
                />
              </div>
              
              <Button type="submit">Sanitisieren</Button>
              
              {sanitizedOutput && (
                <div className="mt-4">
                  <h3 className="text-lg font-medium mb-2">Sanitisierte Ausgabe:</h3>
                  <div className="bg-gray-100 p-4 rounded-md overflow-x-auto">
                    <code>{sanitizedOutput}</code>
                  </div>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
      </section>
      
      {/* GDPR Compliance */}
      <section className="mb-16">
        <h2 className="text-center mb-8">DSGVO-Konformität</h2>
        <div className="grid grid-cols-1 gap-6">
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Cookie-Einwilligungsmanager</CardTitle>
              <CardDescription>DSGVO-konformes Cookie-Management</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Unser Cookie-Einwilligungsmanager ermöglicht es Benutzern, ihre Cookie-Präferenzen zu verwalten und erfüllt die Anforderungen der DSGVO.
              </p>
              <p className="mb-4">
                Der Manager kategorisiert Cookies in verschiedene Gruppen (notwendig, funktional, Analyse, Marketing, Personalisierung) und ermöglicht eine granulare Kontrolle.
              </p>
              <p>
                Klicken Sie auf den Cookie-Button unten links, um den Einwilligungsmanager zu öffnen und Ihre Einstellungen anzupassen.
              </p>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="quechua-card">
              <CardHeader>
                <CardTitle>Datenschutzerklärung</CardTitle>
                <CardDescription>Transparente Information über Datenverarbeitung</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Unsere Datenschutzerklärung informiert Benutzer umfassend über die Verarbeitung ihrer personenbezogenen Daten gemäß DSGVO.
                </p>
                <Button asChild>
                  <a href="/privacy-policy">Datenschutzerklärung ansehen</a>
                </Button>
              </CardContent>
            </Card>
            
            <Card className="quechua-card">
              <CardHeader>
                <CardTitle>AGB</CardTitle>
                <CardDescription>Rechtliche Grundlage für die Nutzung unserer Dienste</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">
                  Unsere Allgemeinen Geschäftsbedingungen regeln das Vertragsverhältnis zwischen uns und unseren Kunden.
                </p>
                <Button asChild>
                  <a href="/terms-of-service">AGB ansehen</a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      {/* Cookie-Einwilligungsmanager */}
      <ConsentManager />
    </div>
  );
}
