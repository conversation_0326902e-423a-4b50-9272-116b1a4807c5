"use client";

import { useState } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search } from "lucide-react";
import FAQSchema from "@/components/seo/FAQSchema";

// FAQ data structure
interface FAQItem {
  question: string;
  answer: string;
  category: FAQCategory;
}

// FAQ categories
enum FAQCategory {
  GENERAL = "Allgemein",
  BUSINESS_CLIENT = "Geschäftskunden",
  DRIVER = "Fahrer",
  ORDERS = "Aufträge",
  PAYMENTS = "Zahlungen",
  TECHNICAL = "Technisches"
}

// Sample FAQ data
const faqData: FAQItem[] = [
  {
    question: "Was ist YoungMobility?",
    answer: "YoungMobility ist eine Plattform, die Unternehmen mit Fahrern verbindet, um Fahrzeugüberführungen effizient und zuverlässig zu organisieren. Wir bieten eine moderne, benutzerfreundliche Lösung für alle Ihre Fahrzeugtransportbedürfnisse.",
    category: FAQCategory.GENERAL
  },
  {
    question: "Wie registriere ich mich als Geschäftskunde?",
    answer: "Um sich als Geschäftskunde zu registrieren, klicken Sie auf 'Registrieren' und wählen Sie 'Als Unternehmen registrieren'. Füllen Sie das Formular mit Ihren Unternehmensdaten aus und reichen Sie es ein. Nach einer kurzen Überprüfung wird Ihr Konto aktiviert.",
    category: FAQCategory.BUSINESS_CLIENT
  },
  {
    question: "Wie werde ich YoungMover (Fahrer)?",
    answer: "Um YoungMover zu werden, registrieren Sie sich über 'Registrieren' und wählen 'Als Fahrer registrieren'. Sie benötigen einen gültigen Führerschein, ein Smartphone und müssen mindestens 21 Jahre alt sein. Nach dem Hochladen Ihrer Dokumente und einer Überprüfung können Sie mit dem Fahren beginnen.",
    category: FAQCategory.DRIVER
  },
  {
    question: "Wie erstelle ich einen Auftrag?",
    answer: "Nach der Anmeldung als Geschäftskunde können Sie über Ihr Dashboard einen neuen Auftrag erstellen. Geben Sie die Details des Fahrzeugs, Abhol- und Lieferort sowie Zeitfenster an. Sie können auch spezielle Anforderungen hinzufügen.",
    category: FAQCategory.ORDERS
  },
  {
    question: "Wie funktioniert die Bezahlung?",
    answer: "Die Bezahlung erfolgt sicher über unsere Plattform. Als Geschäftskunde erhalten Sie eine monatliche Rechnung oder können per Kreditkarte bezahlen. Fahrer erhalten ihre Vergütung wöchentlich auf ihr hinterlegtes Bankkonto.",
    category: FAQCategory.PAYMENTS
  },
  {
    question: "Was mache ich bei technischen Problemen?",
    answer: "Bei technischen Problemen können Sie unser Support-Team über das Kontaktformular oder direkt per E-<NAME_EMAIL> erreichen. Wir bemühen uns, alle Anfragen innerhalb von 24 Stunden zu beantworten.",
    category: FAQCategory.TECHNICAL
  },
  {
    question: "Wie funktioniert die Fahrerzuweisung?",
    answer: "Sobald ein Auftrag erstellt wurde, wird er in unserem System für verfügbare Fahrer sichtbar. Fahrer können Interesse bekunden oder direkt zugewiesen werden. Sie als Kunde werden über jeden Schritt informiert und können den Status in Echtzeit verfolgen.",
    category: FAQCategory.ORDERS
  },
  {
    question: "Welche Fahrzeugtypen können transportiert werden?",
    answer: "Wir unterstützen den Transport verschiedener Fahrzeugtypen, darunter PKW, SUVs, Transporter, LKWs und Luxusfahrzeuge. Bei speziellen Anforderungen kontaktieren Sie bitte unser Team für individuelle Lösungen.",
    category: FAQCategory.GENERAL
  },
  {
    question: "Wie kann ich meine Unternehmensdaten aktualisieren?",
    answer: "Sie können Ihre Unternehmensdaten jederzeit in Ihrem Profil unter 'Einstellungen' aktualisieren. Änderungen an wichtigen Informationen wie Steuernummer oder Firmenname können eine erneute Überprüfung erfordern.",
    category: FAQCategory.BUSINESS_CLIENT
  },
  {
    question: "Wie melde ich ein Problem mit einem Fahrer?",
    answer: "Probleme mit Fahrern können Sie direkt über die Auftragsdetailseite oder über unser Support-System melden. Wir nehmen alle Beschwerden ernst und bearbeiten sie umgehend, um eine hohe Servicequalität zu gewährleisten.",
    category: FAQCategory.BUSINESS_CLIENT
  }
];

export default function FAQPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState<string>("all");

  // Filter FAQs based on search term and active category
  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = activeCategory === "all" || faq.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Häufig gestellte Fragen</h1>
      <p className="text-lg text-center mb-12 max-w-3xl mx-auto">
        Hier finden Sie Antworten auf die häufigsten Fragen zu YoungMobility.
        Wenn Sie weitere Unterstützung benötigen, kontaktieren Sie bitte unser Support-Team.
      </p>

      {/* Search bar */}
      <div className="relative max-w-md mx-auto mb-8">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <Input
          type="text"
          placeholder="Suchen Sie nach Fragen oder Stichwörtern..."
          className="pl-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Category tabs */}
      <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory} className="max-w-4xl mx-auto mb-8">
        <TabsList className="grid grid-cols-3 md:grid-cols-7 mb-8">
          <TabsTrigger value="all">Alle</TabsTrigger>
          <TabsTrigger value={FAQCategory.GENERAL}>{FAQCategory.GENERAL}</TabsTrigger>
          <TabsTrigger value={FAQCategory.BUSINESS_CLIENT}>{FAQCategory.BUSINESS_CLIENT}</TabsTrigger>
          <TabsTrigger value={FAQCategory.DRIVER}>{FAQCategory.DRIVER}</TabsTrigger>
          <TabsTrigger value={FAQCategory.ORDERS}>{FAQCategory.ORDERS}</TabsTrigger>
          <TabsTrigger value={FAQCategory.PAYMENTS}>{FAQCategory.PAYMENTS}</TabsTrigger>
          <TabsTrigger value={FAQCategory.TECHNICAL}>{FAQCategory.TECHNICAL}</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-0">
          <Card>
            <CardContent className="pt-6">
              <Accordion type="single" collapsible className="w-full">
                {filteredFAQs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                    <AccordionContent>{faq.answer}</AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>

              {filteredFAQs.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">Keine Ergebnisse gefunden. Bitte versuchen Sie einen anderen Suchbegriff.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {Object.values(FAQCategory).map((category) => (
          <TabsContent key={category} value={category} className="mt-0">
            <Card>
              <CardContent className="pt-6">
                <Accordion type="single" collapsible className="w-full">
                  {filteredFAQs.map((faq, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                      <AccordionContent>{faq.answer}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>

                {filteredFAQs.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Keine Ergebnisse gefunden. Bitte versuchen Sie einen anderen Suchbegriff.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Contact support section */}
      <div className="max-w-4xl mx-auto mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">Noch Fragen?</h2>
        <p className="mb-6">
          Wenn Sie Ihre Frage hier nicht finden konnten, kontaktieren Sie unser Support-Team.
          Wir helfen Ihnen gerne weiter.
        </p>
        <div className="flex flex-col md:flex-row justify-center gap-4">
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>Kontaktformular</CardTitle>
              <CardDescription>Reichen Sie ein Support-Ticket ein</CardDescription>
            </CardHeader>
            <CardContent>
              <a href="/support/contact" className="text-primary transition-colors hover:text-primary/80">
                Zum Kontaktformular →
              </a>
            </CardContent>
          </Card>
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>E-Mail Support</CardTitle>
              <CardDescription>Direkte Kommunikation per E-Mail</CardDescription>
            </CardHeader>
            <CardContent>
              <a href="mailto:<EMAIL>" className="text-primary transition-colors hover:text-primary/80">
                <EMAIL>
              </a>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Structured data for SEO */}
      <FAQSchema faqs={faqData.map(item => ({ question: item.question, answer: item.answer }))} />
    </div>
  );
}
