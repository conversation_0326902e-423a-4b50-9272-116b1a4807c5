"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2, CheckCircle } from "lucide-react";

// Define the form schema with Zod
const supportTicketSchema = z.object({
  subject: z.string().min(5, "Betreff muss mindestens 5 Zeichen lang sein"),
  message: z.string().min(20, "Nachricht muss mindestens 20 Zeichen lang sein"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH", "CRITICAL"]),
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein").optional(),
});

type SupportTicketInput = z.infer<typeof supportTicketSchema>;

export default function ContactSupportPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<SupportTicketInput>({
    resolver: zodResolver(supportTicketSchema),
    defaultValues: {
      priority: "MEDIUM",
    }
  });
  
  const onSubmit: SubmitHandler<SupportTicketInput> = async (data) => {
    setIsLoading(true);
    
    try {
      const response = await fetch("/api/support/tickets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Ein Fehler ist aufgetreten");
      }
      
      // Success
      toast.success("Support-Anfrage erfolgreich gesendet");
      setIsSubmitted(true);
      reset();
      
    } catch (error) {
      console.error("Fehler beim Senden der Support-Anfrage:", error);
      toast.error("Fehler beim Senden der Support-Anfrage. Bitte versuchen Sie es später erneut.");
    } finally {
      setIsLoading(false);
    }
  };
  
  const priorityLabels = {
    LOW: "Niedrig - Allgemeine Frage oder Feedback",
    MEDIUM: "Mittel - Unterstützung benötigt, aber nicht dringend",
    HIGH: "Hoch - Problem beeinträchtigt die Nutzung",
    CRITICAL: "Kritisch - Schwerwiegendes Problem, sofortige Hilfe benötigt"
  };
  
  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">Kontakt zum Support</h1>
      <p className="text-lg text-center mb-12 max-w-3xl mx-auto">
        Haben Sie Fragen oder benötigen Sie Hilfe? Unser Support-Team steht Ihnen zur Verfügung.
        Füllen Sie das Formular aus, und wir werden uns so schnell wie möglich bei Ihnen melden.
      </p>
      
      <div className="max-w-2xl mx-auto">
        {isSubmitted ? (
          <Card>
            <CardHeader>
              <div className="flex justify-center mb-4">
                <CheckCircle className="h-16 w-16 text-green-500" />
              </div>
              <CardTitle className="text-center">Vielen Dank für Ihre Anfrage!</CardTitle>
              <CardDescription className="text-center">
                Wir haben Ihre Support-Anfrage erhalten und werden uns in Kürze bei Ihnen melden.
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex justify-center">
              <Button onClick={() => setIsSubmitted(false)}>Neue Anfrage stellen</Button>
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Support-Anfrage</CardTitle>
              <CardDescription>
                Bitte geben Sie Details zu Ihrem Anliegen an, damit wir Ihnen bestmöglich helfen können.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {!session && (
                  <div className="space-y-2">
                    <Label htmlFor="email">E-Mail-Adresse</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      {...register("email")}
                    />
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email.message}</p>
                    )}
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="subject">Betreff</Label>
                  <Input
                    id="subject"
                    placeholder="Kurze Beschreibung Ihres Anliegens"
                    {...register("subject")}
                  />
                  {errors.subject && (
                    <p className="text-sm text-red-500">{errors.subject.message}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="priority">Priorität</Label>
                  <Select 
                    defaultValue="MEDIUM" 
                    onValueChange={(value) => setValue("priority", value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Wählen Sie eine Priorität" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">{priorityLabels.LOW}</SelectItem>
                      <SelectItem value="MEDIUM">{priorityLabels.MEDIUM}</SelectItem>
                      <SelectItem value="HIGH">{priorityLabels.HIGH}</SelectItem>
                      <SelectItem value="CRITICAL">{priorityLabels.CRITICAL}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message">Nachricht</Label>
                  <Textarea
                    id="message"
                    placeholder="Beschreiben Sie Ihr Anliegen detailliert..."
                    rows={6}
                    {...register("message")}
                  />
                  {errors.message && (
                    <p className="text-sm text-red-500">{errors.message.message}</p>
                  )}
                </div>
                
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Wird gesendet...
                    </>
                  ) : (
                    "Anfrage senden"
                  )}
                </Button>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-center text-sm text-gray-500">
                <p>Alternativ können Sie uns auch direkt kontaktieren:</p>
                <p className="mt-2">
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
