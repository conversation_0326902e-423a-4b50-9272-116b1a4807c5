import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "API-Dokumentation | YoungMobility",
  description: "Technische Dokumentation der YoungMobility API für Entwickler. Integrieren Sie unsere Fahrzeugüberführungslösungen in Ihre Anwendungen.",
  keywords: "API, Dokumentation, Integration, Entwickler, YoungMobility, Fahrzeugüberführung",
};

// API-Endpunkte
const apiEndpoints = [
  {
    name: "Authentication",
    description: "Endpunkte für die Authentifizierung und Autorisierung",
    endpoints: [
      {
        method: "POST",
        path: "/api/v1/auth/token",
        description: "Generiert ein Access-Token für die API-Nutzung",
        parameters: [
          { name: "client_id", type: "string", required: true, description: "Ihre Client-ID" },
          { name: "client_secret", type: "string", required: true, description: "Ihr Client-Secret" },
          { name: "grant_type", type: "string", required: true, description: "Muss 'client_credentials' sein" },
        ],
        response: `{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}`,
      },
    ],
  },
  {
    name: "Transfers",
    description: "Endpunkte für die Verwaltung von Fahrzeugüberführungen",
    endpoints: [
      {
        method: "GET",
        path: "/api/v1/transfers",
        description: "Listet alle Fahrzeugüberführungen auf",
        parameters: [
          { name: "status", type: "string", required: false, description: "Filtert nach Status (pending, in_progress, completed, cancelled)" },
          { name: "from_date", type: "string (ISO 8601)", required: false, description: "Filtert nach Startdatum" },
          { name: "to_date", type: "string (ISO 8601)", required: false, description: "Filtert nach Enddatum" },
          { name: "page", type: "integer", required: false, description: "Seitennummer für Paginierung (Standard: 1)" },
          { name: "limit", type: "integer", required: false, description: "Anzahl der Ergebnisse pro Seite (Standard: 20, Max: 100)" },
        ],
        response: `{
  "data": [
    {
      "id": "transfer_123456",
      "status": "in_progress",
      "vehicle": {
        "id": "vehicle_789012",
        "make": "BMW",
        "model": "X5",
        "year": 2022,
        "license_plate": "M-AB 1234"
      },
      "pickup": {
        "location": "München",
        "address": "Beispielstraße 1, 80331 München",
        "datetime": "2024-06-15T10:00:00Z"
      },
      "delivery": {
        "location": "Berlin",
        "address": "Musterweg 42, 10115 Berlin",
        "datetime": "2024-06-15T18:00:00Z"
      },
      "driver": {
        "id": "driver_345678",
        "name": "Max Mustermann"
      },
      "created_at": "2024-06-10T08:30:00Z",
      "updated_at": "2024-06-15T11:45:00Z"
    },
    // ...
  ],
  "meta": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "pages": 3
  }
}`,
      },
      {
        method: "POST",
        path: "/api/v1/transfers",
        description: "Erstellt eine neue Fahrzeugüberführung",
        parameters: [
          { name: "vehicle_id", type: "string", required: true, description: "ID des zu überführenden Fahrzeugs" },
          { name: "pickup_location", type: "string", required: true, description: "Abholort" },
          { name: "pickup_address", type: "string", required: true, description: "Abholadresse" },
          { name: "pickup_datetime", type: "string (ISO 8601)", required: true, description: "Abholzeitpunkt" },
          { name: "delivery_location", type: "string", required: true, description: "Zielort" },
          { name: "delivery_address", type: "string", required: true, description: "Zieladresse" },
          { name: "delivery_datetime", type: "string (ISO 8601)", required: true, description: "Lieferzeitpunkt" },
          { name: "notes", type: "string", required: false, description: "Zusätzliche Anmerkungen" },
        ],
        response: `{
  "id": "transfer_123456",
  "status": "pending",
  "vehicle": {
    "id": "vehicle_789012",
    "make": "BMW",
    "model": "X5",
    "year": 2022,
    "license_plate": "M-AB 1234"
  },
  "pickup": {
    "location": "München",
    "address": "Beispielstraße 1, 80331 München",
    "datetime": "2024-06-15T10:00:00Z"
  },
  "delivery": {
    "location": "Berlin",
    "address": "Musterweg 42, 10115 Berlin",
    "datetime": "2024-06-15T18:00:00Z"
  },
  "driver": null,
  "created_at": "2024-06-10T08:30:00Z",
  "updated_at": "2024-06-10T08:30:00Z"
}`,
      },
      {
        method: "GET",
        path: "/api/v1/transfers/:id",
        description: "Ruft Details einer spezifischen Fahrzeugüberführung ab",
        parameters: [
          { name: "id", type: "string", required: true, description: "ID der Fahrzeugüberführung" },
        ],
        response: `{
  "id": "transfer_123456",
  "status": "in_progress",
  "vehicle": {
    "id": "vehicle_789012",
    "make": "BMW",
    "model": "X5",
    "year": 2022,
    "license_plate": "M-AB 1234"
  },
  "pickup": {
    "location": "München",
    "address": "Beispielstraße 1, 80331 München",
    "datetime": "2024-06-15T10:00:00Z",
    "completed_at": "2024-06-15T10:15:00Z",
    "notes": "Fahrzeug wurde ohne Schäden übernommen"
  },
  "delivery": {
    "location": "Berlin",
    "address": "Musterweg 42, 10115 Berlin",
    "datetime": "2024-06-15T18:00:00Z",
    "completed_at": null,
    "notes": null
  },
  "driver": {
    "id": "driver_345678",
    "name": "Max Mustermann",
    "phone": "+49123456789"
  },
  "tracking": {
    "current_location": {
      "latitude": 52.1234,
      "longitude": 13.5678,
      "updated_at": "2024-06-15T14:30:00Z"
    },
    "estimated_arrival": "2024-06-15T18:15:00Z"
  },
  "created_at": "2024-06-10T08:30:00Z",
  "updated_at": "2024-06-15T14:30:00Z"
}`,
      },
    ],
  },
  {
    name: "Vehicles",
    description: "Endpunkte für die Verwaltung von Fahrzeugen",
    endpoints: [
      {
        method: "GET",
        path: "/api/v1/vehicles",
        description: "Listet alle Fahrzeuge auf",
        parameters: [
          { name: "status", type: "string", required: false, description: "Filtert nach Status (available, in_transfer, maintenance)" },
          { name: "location", type: "string", required: false, description: "Filtert nach aktuellem Standort" },
          { name: "page", type: "integer", required: false, description: "Seitennummer für Paginierung (Standard: 1)" },
          { name: "limit", type: "integer", required: false, description: "Anzahl der Ergebnisse pro Seite (Standard: 20, Max: 100)" },
        ],
        response: `{
  "data": [
    {
      "id": "vehicle_789012",
      "make": "BMW",
      "model": "X5",
      "year": 2022,
      "license_plate": "M-AB 1234",
      "vin": "WBA12345678901234",
      "status": "available",
      "location": "München",
      "created_at": "2023-01-15T10:00:00Z",
      "updated_at": "2024-06-10T08:30:00Z"
    },
    // ...
  ],
  "meta": {
    "total": 87,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}`,
      },
    ],
  },
];

// Code-Beispiele
const codeExamples = {
  curl: `# Authentifizierung und Token-Generierung
curl -X POST \\
  https://api.youngmobility.com/api/v1/auth/token \\
  -H "Content-Type: application/json" \\
  -d '{
    "client_id": "YOUR_CLIENT_ID",
    "client_secret": "YOUR_CLIENT_SECRET",
    "grant_type": "client_credentials"
  }'

# Fahrzeugüberführung erstellen
curl -X POST \\
  https://api.youngmobility.com/api/v1/transfers \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{
    "vehicle_id": "vehicle_789012",
    "pickup_location": "München",
    "pickup_address": "Beispielstraße 1, 80331 München",
    "pickup_datetime": "2024-06-15T10:00:00Z",
    "delivery_location": "Berlin",
    "delivery_address": "Musterweg 42, 10115 Berlin",
    "delivery_datetime": "2024-06-15T18:00:00Z"
  }'`,
  
  javascript: `// Authentifizierung und Token-Generierung
async function getAccessToken() {
  const response = await fetch('https://api.youngmobility.com/api/v1/auth/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      client_id: 'YOUR_CLIENT_ID',
      client_secret: 'YOUR_CLIENT_SECRET',
      grant_type: 'client_credentials',
    }),
  });
  
  const data = await response.json();
  return data.access_token;
}

// Fahrzeugüberführung erstellen
async function createTransfer() {
  const token = await getAccessToken();
  
  const response = await fetch('https://api.youngmobility.com/api/v1/transfers', {
    method: 'POST',
    headers: {
      'Authorization': \`Bearer \${token}\`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      vehicle_id: 'vehicle_789012',
      pickup_location: 'München',
      pickup_address: 'Beispielstraße 1, 80331 München',
      pickup_datetime: '2024-06-15T10:00:00Z',
      delivery_location: 'Berlin',
      delivery_address: 'Musterweg 42, 10115 Berlin',
      delivery_datetime: '2024-06-15T18:00:00Z',
    }),
  });
  
  const data = await response.json();
  console.log('Transfer created:', data);
}`,
  
  python: `import requests
import json

# Authentifizierung und Token-Generierung
def get_access_token():
    url = "https://api.youngmobility.com/api/v1/auth/token"
    payload = {
        "client_id": "YOUR_CLIENT_ID",
        "client_secret": "YOUR_CLIENT_SECRET",
        "grant_type": "client_credentials"
    }
    headers = {"Content-Type": "application/json"}
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    data = response.json()
    
    return data["access_token"]

# Fahrzeugüberführung erstellen
def create_transfer():
    token = get_access_token()
    url = "https://api.youngmobility.com/api/v1/transfers"
    
    payload = {
        "vehicle_id": "vehicle_789012",
        "pickup_location": "München",
        "pickup_address": "Beispielstraße 1, 80331 München",
        "pickup_datetime": "2024-06-15T10:00:00Z",
        "delivery_location": "Berlin",
        "delivery_address": "Musterweg 42, 10115 Berlin",
        "delivery_datetime": "2024-06-15T18:00:00Z"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    data = response.json()
    
    print("Transfer created:", data)`,
};

export default function ApiDocsPage() {
  return (
    <div className="space-y-16 py-8">
      {/* Header */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-6 text-gray-900">API-Dokumentation</h1>
          <p className="text-xl text-gray-700 mb-8">
            Willkommen bei der YoungMobility API-Dokumentation. Hier finden Sie alle Informationen, die Sie benötigen, um unsere API in Ihre Anwendungen zu integrieren.
          </p>
          <div className="bg-accent/10 p-6 rounded-xl">
            <h2 className="text-xl font-semibold mb-3 text-gray-900">Basis-URL</h2>
            <code className="bg-gray-800 text-white p-3 rounded block overflow-x-auto">
              https://api.youngmobility.com
            </code>
            <p className="mt-3 text-gray-700">
              Alle API-Anfragen müssen an diese Basis-URL gesendet werden. Die API verwendet HTTPS für alle Endpunkte.
            </p>
          </div>
        </div>
      </section>

      {/* Authentifizierung */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Authentifizierung</h2>
          <p className="text-lg text-gray-700 mb-6">
            Die YoungMobility API verwendet OAuth 2.0 mit Client Credentials Flow für die Authentifizierung. Um auf die API zugreifen zu können, benötigen Sie eine Client-ID und ein Client-Secret, die Sie im Developer-Portal erhalten.
          </p>
          <div className="bg-white p-6 rounded-xl shadow-md mb-6">
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Token-Generierung</h3>
            <p className="text-gray-700 mb-4">
              Um ein Access-Token zu erhalten, senden Sie eine POST-Anfrage an den <code className="bg-gray-100 px-1 py-0.5 rounded">/api/v1/auth/token</code> Endpunkt mit Ihren Client-Credentials.
            </p>
            <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
              {`POST /api/v1/auth/token
Content-Type: application/json

{
  "client_id": "YOUR_CLIENT_ID",
  "client_secret": "YOUR_CLIENT_SECRET",
  "grant_type": "client_credentials"
}`}
            </pre>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Verwendung des Tokens</h3>
            <p className="text-gray-700 mb-4">
              Fügen Sie das erhaltene Token als Bearer-Token im Authorization-Header zu allen API-Anfragen hinzu.
            </p>
            <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
              {`GET /api/v1/transfers
Authorization: Bearer YOUR_ACCESS_TOKEN`}
            </pre>
          </div>
        </div>
      </section>

      {/* API-Endpunkte */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-8 text-gray-900">API-Endpunkte</h2>
          
          <div className="space-y-12">
            {apiEndpoints.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <h3 className="text-2xl font-semibold mb-4 text-gray-900">{category.name}</h3>
                <p className="text-gray-700 mb-6">{category.description}</p>
                
                <div className="space-y-8">
                  {category.endpoints.map((endpoint, endpointIndex) => (
                    <div key={endpointIndex} className="bg-white p-6 rounded-xl shadow-md">
                      <div className="flex items-center mb-4">
                        <span className={`px-3 py-1 rounded-full text-white text-sm font-medium mr-3 ${
                          endpoint.method === 'GET' ? 'bg-blue-500' :
                          endpoint.method === 'POST' ? 'bg-green-500' :
                          endpoint.method === 'PUT' ? 'bg-yellow-500' :
                          endpoint.method === 'DELETE' ? 'bg-red-500' : 'bg-gray-500'
                        }`}>
                          {endpoint.method}
                        </span>
                        <code className="text-lg font-mono">{endpoint.path}</code>
                      </div>
                      
                      <p className="text-gray-700 mb-4">{endpoint.description}</p>
                      
                      {endpoint.parameters && endpoint.parameters.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold mb-2 text-gray-900">Parameter</h4>
                          <div className="overflow-x-auto">
                            <table className="min-w-full border-collapse">
                              <thead>
                                <tr className="bg-gray-50">
                                  <th className="py-2 px-4 text-left text-sm font-medium text-gray-700 border border-gray-200">Name</th>
                                  <th className="py-2 px-4 text-left text-sm font-medium text-gray-700 border border-gray-200">Typ</th>
                                  <th className="py-2 px-4 text-left text-sm font-medium text-gray-700 border border-gray-200">Erforderlich</th>
                                  <th className="py-2 px-4 text-left text-sm font-medium text-gray-700 border border-gray-200">Beschreibung</th>
                                </tr>
                              </thead>
                              <tbody>
                                {endpoint.parameters.map((param, paramIndex) => (
                                  <tr key={paramIndex} className={paramIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                    <td className="py-2 px-4 text-sm text-gray-700 border border-gray-200">{param.name}</td>
                                    <td className="py-2 px-4 text-sm text-gray-700 border border-gray-200">{param.type}</td>
                                    <td className="py-2 px-4 text-sm text-gray-700 border border-gray-200">{param.required ? 'Ja' : 'Nein'}</td>
                                    <td className="py-2 px-4 text-sm text-gray-700 border border-gray-200">{param.description}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      )}
                      
                      <div>
                        <h4 className="text-lg font-semibold mb-2 text-gray-900">Beispielantwort</h4>
                        <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
                          {endpoint.response}
                        </pre>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Code-Beispiele */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-8 text-gray-900">Code-Beispiele</h2>
          
          <Tabs defaultValue="curl" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="curl">cURL</TabsTrigger>
              <TabsTrigger value="javascript">JavaScript</TabsTrigger>
              <TabsTrigger value="python">Python</TabsTrigger>
            </TabsList>
            
            <TabsContent value="curl">
              <div className="bg-white p-6 rounded-xl shadow-md">
                <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
                  {codeExamples.curl}
                </pre>
              </div>
            </TabsContent>
            
            <TabsContent value="javascript">
              <div className="bg-white p-6 rounded-xl shadow-md">
                <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
                  {codeExamples.javascript}
                </pre>
              </div>
            </TabsContent>
            
            <TabsContent value="python">
              <div className="bg-white p-6 rounded-xl shadow-md">
                <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
                  {codeExamples.python}
                </pre>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Fehlerbehandlung */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Fehlerbehandlung</h2>
          <p className="text-lg text-gray-700 mb-6">
            Die YoungMobility API verwendet standardmäßige HTTP-Statuscodes, um den Erfolg oder Misserfolg einer API-Anfrage anzuzeigen. Im Allgemeinen werden Codes im Bereich 2xx für erfolgreiche Anfragen zurückgegeben, Codes im Bereich 4xx für Fehler, die durch die bereitgestellten Informationen verursacht wurden, und Codes im Bereich 5xx für Fehler auf unseren Servern.
          </p>
          
          <div className="bg-white p-6 rounded-xl shadow-md">
            <h3 className="text-xl font-semibold mb-3 text-gray-900">Fehlerantwort-Format</h3>
            <pre className="bg-gray-800 text-white p-4 rounded overflow-x-auto">
              {`{
  "error": {
    "code": "invalid_request",
    "message": "The request was invalid",
    "details": [
      {
        "field": "pickup_datetime",
        "message": "Pickup datetime must be in the future"
      }
    ]
  }
}`}
            </pre>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="container mx-auto px-4 text-center py-8">
        <div className="bg-accent text-white p-8 md:p-12 rounded-xl max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6">Bereit für die Integration?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Registrieren Sie sich im Developer-Portal, um Ihre API-Schlüssel zu erhalten und mit der Integration zu beginnen.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-white text-accent hover:bg-gray-100">
              <Link href="/developer-portal">Zum Developer-Portal</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Link href="/contact">Support kontaktieren</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
