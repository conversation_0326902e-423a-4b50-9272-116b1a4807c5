"use client"

import { GradientButton } from "@/components/ui/gradient-button"

export default function LinkTestPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black text-center">🔗 Link Button Test</h1>
      
      <div className="max-w-2xl mx-auto space-y-12">
        {/* Current Link Button */}
        <div className="p-8 bg-gray-50 rounded-lg border-2 border-gray-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">Current Link Button</h2>
          
          <div className="text-center space-y-6">
            <p className="text-gray-700">
              Hover over the Link Button below to see the current behavior:
            </p>
            
            <div className="flex justify-center">
              <GradientButton variant="link">
                Link Button Test
              </GradientButton>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h3 className="font-bold text-gray-800 mb-2">Expected Behavior:</h3>
              <ul className="text-sm text-gray-600 space-y-1 text-left">
                <li>• <strong>Base State:</strong> Dark blue text (#4338ca) with underline</li>
                <li>• <strong>Hover Effects:</strong></li>
                <li>&nbsp;&nbsp;- Same dark blue color (no color change)</li>
                <li>&nbsp;&nbsp;- Slides 4px to the right (translateX)</li>
                <li>&nbsp;&nbsp;- Light blue shimmer effect passes through</li>
                <li>&nbsp;&nbsp;- Maintains underline styling</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Visual Comparison */}
        <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-blue-800">Visual Effects Test</h2>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center space-y-4">
                <h3 className="text-lg font-bold text-gray-700">Link Button</h3>
                <GradientButton variant="link">Hover Me</GradientButton>
                <p className="text-xs text-gray-500">Should slide right + shimmer</p>
              </div>
              
              <div className="text-center space-y-4">
                <h3 className="text-lg font-bold text-gray-700">For Comparison</h3>
                <GradientButton variant="ghost">Ghost Button</GradientButton>
                <p className="text-xs text-gray-500">Different hover behavior</p>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">What You Should See:</h4>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>Link Button Hover:</strong></p>
                <ul className="ml-4 space-y-1">
                  <li>• Text stays the same dark blue color</li>
                  <li>• Button slides smoothly to the right</li>
                  <li>• A light blue shimmer effect sweeps across</li>
                  <li>• Underline remains visible</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="p-8 bg-yellow-50 rounded-lg border-2 border-yellow-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-yellow-800">Technical Implementation</h2>
          
          <div className="space-y-6">
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">CSS Properties:</h4>
              <div className="text-xs font-mono space-y-1">
                <p><strong>Base State:</strong></p>
                <p>color: #4338ca;</p>
                <p>text-decoration: underline;</p>
                <p>text-decoration-color: #4338ca;</p>
                <br />
                <p><strong>Hover State:</strong></p>
                <p>color: #4338ca; (same - no change)</p>
                <p>text-decoration-color: #4338ca; (same)</p>
                <p>transform: translateX(4px); (slide effect)</p>
                <br />
                <p><strong>Shimmer Effect (::before):</strong></p>
                <p>background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);</p>
                <p>left: -100% → 100% (on hover)</p>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">Why No Color Change:</h4>
              <p className="text-sm text-gray-600">
                As requested, both the base state and hover state now use the same color (#4338ca) 
                to eliminate the color change effect. The focus is purely on the slide animation 
                and shimmer effect for a more subtle, consistent interaction.
              </p>
            </div>
          </div>
        </div>

        {/* Confirmation */}
        <div className="p-8 bg-green-50 rounded-lg border-2 border-green-300">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-green-800">✅ Implementation Confirmed</h2>
            <p className="text-lg text-green-700 mb-6">
              The Link Button now uses consistent text color with only slide and shimmer effects on hover
            </p>
            <div className="flex justify-center">
              <GradientButton variant="link">Perfect Consistency!</GradientButton>
            </div>
            <p className="text-sm text-green-600 mt-4">
              No color change - just smooth slide and shimmer animation
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
