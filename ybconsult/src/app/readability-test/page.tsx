"use client"

import { GradientButton } from "@/components/ui/gradient-button"
import { useState } from "react"

export default function ReadabilityTestPage() {
  const [hoveredButton, setHoveredButton] = useState<string | null>(null)

  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black text-center">📖 Destructive Button Readability Test</h1>
      
      <div className="max-w-4xl mx-auto space-y-12">
        {/* Improvement Banner */}
        <div className="p-8 bg-green-100 rounded-lg border-2 border-green-400">
          <h2 className="text-2xl font-bold mb-4 text-green-800 text-center">✅ Readability Improvement Complete!</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-green-700">
            <div>
              <h3 className="font-bold mb-2">🎯 What Was Fixed:</h3>
              <ul className="space-y-1 text-sm">
                <li>• Changed from dark red text (#7f1d1d) to white text (#ffffff)</li>
                <li>• Updated to lighter red gradient colors (coral, pink, rose)</li>
                <li>• Improved contrast ratio for better accessibility</li>
                <li>• Maintained red theme and destructive button purpose</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-2">🚀 New Hover Colors:</h3>
              <ul className="space-y-1 text-sm">
                <li>• Light red/pink (#fca5a5)</li>
                <li>• Coral red (#f87171)</li>
                <li>• Rose pink (#fb7185)</li>
                <li>• Light pink (#fda4af)</li>
                <li>• Very light pink (#fecaca)</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Interactive Test */}
        <div className="p-8 bg-red-50 rounded-lg border-2 border-red-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-red-800">🎮 Interactive Readability Test</h2>
          
          <div className="text-center space-y-6">
            <p className="text-gray-700">
              Hover over the Destructive Button below to test the improved readability:
            </p>
            
            <div className="flex justify-center">
              <GradientButton 
                variant="destructive"
                onMouseEnter={() => setHoveredButton('destructive')}
                onMouseLeave={() => setHoveredButton(null)}
              >
                Delete Account
              </GradientButton>
            </div>
            
            {hoveredButton === 'destructive' && (
              <div className="p-4 bg-white rounded border-2 border-red-400">
                <p className="text-red-700 font-medium">
                  🎯 Hover detected! The white text should now be clearly readable against the light red gradient background.
                </p>
              </div>
            )}
            
            <p className="text-sm text-gray-600">
              The button should maintain excellent text readability in both base and hover states.
            </p>
          </div>
        </div>

        {/* Before vs After Comparison */}
        <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-blue-800">📊 Before vs After Comparison</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-bold text-red-600">❌ Before (Poor Readability)</h3>
              <div className="p-4 bg-white rounded border">
                <p className="text-sm text-gray-600 mb-3">Hover colors were:</p>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center justify-between">
                    <span>Dark red (#b91c1c)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#b91c1c'}}></div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Red (#dc2626)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#dc2626'}}></div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Bright red (#ef4444)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#ef4444'}}></div>
                  </div>
                </div>
                <p className="text-xs text-red-600 mt-3 font-medium">
                  Text color: Very dark red (#7f1d1d) - Poor contrast!
                </p>
              </div>
            </div>
            
            <div className="text-center space-y-4">
              <h3 className="text-lg font-bold text-green-600">✅ After (Excellent Readability)</h3>
              <div className="p-4 bg-white rounded border">
                <p className="text-sm text-gray-600 mb-3">Hover colors are now:</p>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center justify-between">
                    <span>Light red (#fca5a5)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#fca5a5'}}></div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Coral red (#f87171)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#f87171'}}></div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Rose pink (#fb7185)</span>
                    <div className="w-6 h-6 rounded" style={{backgroundColor: '#fb7185'}}></div>
                  </div>
                </div>
                <p className="text-xs text-green-600 mt-3 font-medium">
                  Text color: White (#ffffff) - Excellent contrast!
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* All Button Variants Comparison */}
        <div className="p-8 bg-purple-50 rounded-lg border-2 border-purple-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-purple-800">🔄 All Button Variants Readability</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Default</h4>
              <GradientButton variant="default">Default Button</GradientButton>
              <p className="text-xs text-gray-500">White text on dark gradient</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Variant</h4>
              <GradientButton variant="variant">Variant Button</GradientButton>
              <p className="text-xs text-gray-500">White text on gradient</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-red-700">Destructive (IMPROVED)</h4>
              <GradientButton variant="destructive">Delete Button</GradientButton>
              <p className="text-xs text-red-600">White text on light red gradient</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-purple-700">Outline</h4>
              <GradientButton variant="outline">Outline Button</GradientButton>
              <p className="text-xs text-gray-500">White text, gradient border</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-gray-700">Ghost</h4>
              <GradientButton variant="ghost">Ghost Button</GradientButton>
              <p className="text-xs text-gray-500">Adaptive text color</p>
            </div>
            
            <div className="text-center space-y-3">
              <h4 className="font-semibold text-blue-700">Link</h4>
              <GradientButton variant="link">Link Button</GradientButton>
              <p className="text-xs text-gray-500">Blue text with underline</p>
            </div>
          </div>
          
          <p className="text-center text-purple-700 font-medium mt-6">
            ✅ All button variants now maintain excellent text readability in both base and hover states
          </p>
        </div>

        {/* Technical Details */}
        <div className="p-8 bg-yellow-50 rounded-lg border-2 border-yellow-300">
          <h2 className="text-2xl font-bold mb-6 text-center text-yellow-800">⚙️ Technical Implementation Details</h2>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold text-gray-800 mb-3">🎨 Color Changes Made:</h3>
                <div className="bg-white p-4 rounded border text-sm">
                  <p className="font-medium mb-2">Hover Gradient Colors:</p>
                  <ul className="space-y-1 text-xs font-mono">
                    <li>--color-1: #fca5a5 (Light red/pink)</li>
                    <li>--color-2: #f87171 (Coral red)</li>
                    <li>--color-3: #fb7185 (Rose pink)</li>
                    <li>--color-4: #fda4af (Light pink)</li>
                    <li>--color-5: #fecaca (Very light pink)</li>
                  </ul>
                  <p className="font-medium mt-3 mb-1">Text Color:</p>
                  <p className="text-xs font-mono">color: #ffffff (White)</p>
                </div>
              </div>
              
              <div>
                <h3 className="font-bold text-gray-800 mb-3">✅ Benefits Achieved:</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Improved contrast ratio for accessibility</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Maintained red theme for destructive actions</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Consistent 0.5s animation timing</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Same gradient structure as other variants</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="text-green-500 font-bold">✓</span>
                    <span>Better user experience and readability</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded border">
              <h4 className="font-bold text-gray-800 mb-2">CSS Implementation:</h4>
              <pre className="text-xs text-gray-600 overflow-x-auto">
{`.gradient-button-destructive:hover {
  --color-1: #fca5a5; /* Light red/pink */
  --color-2: #f87171; /* Coral red */
  --color-3: #fb7185; /* Rose pink */
  --color-4: #fda4af; /* Light pink */
  --color-5: #fecaca; /* Very light pink */
  color: #ffffff; /* White text for optimal contrast */
}`}
              </pre>
            </div>
          </div>
        </div>

        {/* Success Confirmation */}
        <div className="p-8 bg-gradient-to-r from-green-100 to-emerald-100 rounded-lg border-2 border-green-400">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4 text-green-800">🎉 Readability Improvement Complete!</h2>
            <p className="text-lg text-green-700 mb-6">
              The Destructive Button now provides excellent text readability while maintaining its red theme and consistent behavior
            </p>
            <div className="flex justify-center">
              <GradientButton variant="destructive">Perfect Readability Achieved!</GradientButton>
            </div>
            <p className="text-sm text-green-600 mt-4">
              Hover over the button above to see the improved light red gradient with excellent white text contrast
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
