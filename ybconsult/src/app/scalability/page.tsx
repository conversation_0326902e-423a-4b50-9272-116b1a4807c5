"use client";

import React, { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { SystemMetrics, AppMetrics, HealthStatus } from "@/utils/monitoring";

/**
 * Scalability & Reliability Showcase Page
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */
export default function ScalabilityPage() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [appMetrics, setAppMetrics] = useState<AppMetrics | null>(null);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Simuliere API-Aufrufe für Metriken und Gesundheitsstatus
        const systemMetricsResponse = await fetch("/api/monitoring/system");
        const appMetricsResponse = await fetch("/api/monitoring/app");
        const healthStatusResponse = await fetch("/api/health");
        
        if (!systemMetricsResponse.ok || !appMetricsResponse.ok || !healthStatusResponse.ok) {
          throw new Error("Failed to fetch monitoring data");
        }
        
        const systemMetricsData = await systemMetricsResponse.json();
        const appMetricsData = await appMetricsResponse.json();
        const healthStatusData = await healthStatusResponse.json();
        
        setSystemMetrics(systemMetricsData);
        setAppMetrics(appMetricsData);
        setHealthStatus(healthStatusData);
        setError(null);
      } catch (err) {
        console.error("Error fetching monitoring data:", err);
        setError("Failed to fetch monitoring data. Using simulated data instead.");
        
        // Simulierte Daten für die Demo
        setSystemMetrics({
          timestamp: new Date().toISOString(),
          cpu: {
            usage: 25.4,
            loadAvg: [1.2, 1.5, 1.7],
          },
          memory: {
            total: 16000000000,
            free: **********,
            used: **********,
            usagePercent: 50,
          },
          uptime: 86400,
        });
        
        setAppMetrics({
          timestamp: new Date().toISOString(),
          requests: {
            total: 15000,
            success: 14850,
            error: 150,
            avgResponseTime: 120,
          },
          database: {
            connectionCount: 5,
            queryCount: 45000,
            avgQueryTime: 15,
          },
          cache: {
            hits: 12000,
            misses: 3000,
            hitRate: 0.8,
          },
        });
        
        setHealthStatus({
          status: "healthy",
          timestamp: new Date().toISOString(),
          services: {
            app: {
              status: "healthy",
            },
            database: {
              status: "healthy",
              responseTime: 5,
            },
            cache: {
              status: "healthy",
            },
          },
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
    
    // Aktualisiere die Daten alle 30 Sekunden
    const interval = setInterval(fetchData, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Formatiere Bytes in lesbare Größen
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  
  // Formatiere Sekunden in lesbare Zeit
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    return `${days}d ${hours}h ${minutes}m`;
  };
  
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-center mb-8">Skalierbarkeit & Zuverlässigkeit</h1>
      <p className="text-center mb-12 max-w-3xl mx-auto">
        Diese Seite demonstriert verschiedene Aspekte der Skalierbarkeit und Zuverlässigkeit, die im Rahmen von Phase 8 implementiert wurden.
      </p>
      
      {loading && !systemMetrics ? (
        <div className="text-center py-12">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-4">Lade Monitoring-Daten...</p>
        </div>
      ) : (
        <>
          {error && (
            <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-8">
              <p>{error}</p>
            </div>
          )}
          
          {/* Gesundheitsstatus */}
          {healthStatus && (
            <section className="mb-16">
              <h2 className="text-center mb-8">Systemgesundheit</h2>
              <Card className={`quechua-card border-l-4 ${
                healthStatus.status === 'healthy' 
                  ? 'border-l-green-500' 
                  : healthStatus.status === 'degraded' 
                    ? 'border-l-yellow-500' 
                    : 'border-l-red-500'
              }`}>
                <CardHeader>
                  <CardTitle>Gesamtstatus: {
                    healthStatus.status === 'healthy' 
                      ? 'Gesund' 
                      : healthStatus.status === 'degraded' 
                        ? 'Beeinträchtigt' 
                        : 'Ungesund'
                  }</CardTitle>
                  <CardDescription>Letzte Aktualisierung: {new Date(healthStatus.timestamp).toLocaleString()}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className={`p-4 rounded-lg ${
                      healthStatus.services.app.status === 'healthy' 
                        ? 'bg-green-100' 
                        : healthStatus.services.app.status === 'degraded' 
                          ? 'bg-yellow-100' 
                          : 'bg-red-100'
                    }`}>
                      <h3 className="font-bold mb-2">Anwendung</h3>
                      <p>{healthStatus.services.app.status === 'healthy' 
                        ? 'Gesund' 
                        : healthStatus.services.app.status === 'degraded' 
                          ? 'Beeinträchtigt' 
                          : 'Ungesund'
                      }</p>
                      {healthStatus.services.app.message && (
                        <p className="text-sm mt-2">{healthStatus.services.app.message}</p>
                      )}
                    </div>
                    
                    <div className={`p-4 rounded-lg ${
                      healthStatus.services.database.status === 'healthy' 
                        ? 'bg-green-100' 
                        : healthStatus.services.database.status === 'degraded' 
                          ? 'bg-yellow-100' 
                          : 'bg-red-100'
                    }`}>
                      <h3 className="font-bold mb-2">Datenbank</h3>
                      <p>{healthStatus.services.database.status === 'healthy' 
                        ? 'Gesund' 
                        : healthStatus.services.database.status === 'degraded' 
                          ? 'Beeinträchtigt' 
                          : 'Ungesund'
                      }</p>
                      {healthStatus.services.database.responseTime && (
                        <p className="text-sm mt-2">Antwortzeit: {healthStatus.services.database.responseTime}ms</p>
                      )}
                      {healthStatus.services.database.message && (
                        <p className="text-sm mt-2">{healthStatus.services.database.message}</p>
                      )}
                    </div>
                    
                    <div className={`p-4 rounded-lg ${
                      healthStatus.services.cache.status === 'healthy' 
                        ? 'bg-green-100' 
                        : healthStatus.services.cache.status === 'degraded' 
                          ? 'bg-yellow-100' 
                          : 'bg-red-100'
                    }`}>
                      <h3 className="font-bold mb-2">Cache</h3>
                      <p>{healthStatus.services.cache.status === 'healthy' 
                        ? 'Gesund' 
                        : healthStatus.services.cache.status === 'degraded' 
                          ? 'Beeinträchtigt' 
                          : 'Ungesund'
                      }</p>
                      {healthStatus.services.cache.message && (
                        <p className="text-sm mt-2">{healthStatus.services.cache.message}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          )}
          
          {/* Systemmetriken */}
          {systemMetrics && (
            <section className="mb-16">
              <h2 className="text-center mb-8">Systemmetriken</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>CPU-Auslastung</CardTitle>
                    <CardDescription>Aktuelle CPU-Nutzung und Last</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-4xl font-bold mb-4">{systemMetrics.cpu.usage.toFixed(1)}%</div>
                    <div className="mb-2">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-quechua-dark-green h-2.5 rounded-full" 
                          style={{ width: `${systemMetrics.cpu.usage}%` }}
                        ></div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Load Average: {systemMetrics.cpu.loadAvg.map(load => load.toFixed(2)).join(", ")}
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>Speichernutzung</CardTitle>
                    <CardDescription>Aktueller Speicherverbrauch</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-4xl font-bold mb-4">{systemMetrics.memory.usagePercent.toFixed(1)}%</div>
                    <div className="mb-2">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-quechua-dark-green h-2.5 rounded-full" 
                          style={{ width: `${systemMetrics.memory.usagePercent}%` }}
                        ></div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      Genutzt: {formatBytes(systemMetrics.memory.used)} / {formatBytes(systemMetrics.memory.total)}
                    </p>
                    <p className="text-sm text-gray-500">
                      Frei: {formatBytes(systemMetrics.memory.free)}
                    </p>
                  </CardContent>
                </Card>
              </div>
              
              <div className="mt-6">
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>Systemuptime</CardTitle>
                    <CardDescription>Zeit seit dem letzten Neustart</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatUptime(systemMetrics.uptime)}</div>
                  </CardContent>
                </Card>
              </div>
            </section>
          )}
          
          {/* Anwendungsmetriken */}
          {appMetrics && (
            <section className="mb-16">
              <h2 className="text-center mb-8">Anwendungsmetriken</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>Anfragen</CardTitle>
                    <CardDescription>Anfragenstatistiken</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-4xl font-bold mb-4">{appMetrics.requests.total.toLocaleString()}</div>
                    <p className="mb-1">
                      <span className="text-green-600">✓ {appMetrics.requests.success.toLocaleString()}</span> erfolgreich
                    </p>
                    <p className="mb-4">
                      <span className="text-red-600">✗ {appMetrics.requests.error.toLocaleString()}</span> fehlgeschlagen
                    </p>
                    <p className="text-sm text-gray-500">
                      Durchschnittliche Antwortzeit: {appMetrics.requests.avgResponseTime.toFixed(2)}ms
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>Datenbank</CardTitle>
                    <CardDescription>Datenbankstatistiken</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-4xl font-bold mb-4">{appMetrics.database.queryCount.toLocaleString()}</div>
                    <p className="mb-4">Abfragen insgesamt</p>
                    <p className="text-sm text-gray-500">
                      Aktive Verbindungen: {appMetrics.database.connectionCount}
                    </p>
                    <p className="text-sm text-gray-500">
                      Durchschnittliche Abfragezeit: {appMetrics.database.avgQueryTime.toFixed(2)}ms
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="quechua-card">
                  <CardHeader>
                    <CardTitle>Cache</CardTitle>
                    <CardDescription>Cache-Statistiken</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-4xl font-bold mb-4">{(appMetrics.cache.hitRate * 100).toFixed(1)}%</div>
                    <p className="mb-4">Cache-Trefferrate</p>
                    <p className="text-sm text-gray-500">
                      Treffer: {appMetrics.cache.hits.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500">
                      Fehlschläge: {appMetrics.cache.misses.toLocaleString()}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </section>
          )}
        </>
      )}
      
      {/* Skalierbarkeits- und Zuverlässigkeitsfunktionen */}
      <section className="mb-16">
        <h2 className="text-center mb-8">Implementierte Funktionen</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Datenbankverbindungs-Pooling</CardTitle>
              <CardDescription>Optimierte Datenbankverbindungen</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Unsere Anwendung verwendet Connection Pooling, um die Anzahl der Datenbankverbindungen zu optimieren und die Leistung zu verbessern.
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>Wiederverwendung von Datenbankverbindungen</li>
                <li>Konfigurierbare Pool-Größe</li>
                <li>Automatische Wiederherstellung bei Verbindungsfehlern</li>
                <li>Überwachung der Poolauslastung</li>
              </ul>
            </CardContent>
          </Card>
          
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Robuste Fehlerbehandlung</CardTitle>
              <CardDescription>Umfassende Fehlerbehandlung und Logging</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Unsere Anwendung implementiert eine robuste Fehlerbehandlung, um die Zuverlässigkeit zu verbessern und Ausfallzeiten zu minimieren.
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>Strukturierte Fehlerbehandlung für API-Routen</li>
                <li>Detailliertes Fehler-Logging</li>
                <li>Benutzerfreundliche Fehlermeldungen</li>
                <li>Automatische Wiederherstellung bei transienten Fehlern</li>
              </ul>
            </CardContent>
          </Card>
          
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Datensicherung und -wiederherstellung</CardTitle>
              <CardDescription>Schutz vor Datenverlust</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Unsere Anwendung implementiert automatische Datensicherungen und Wiederherstellungsmechanismen, um Datenverlust zu verhindern.
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>Regelmäßige automatische Backups</li>
                <li>Inkrementelle und vollständige Backups</li>
                <li>Einfache Wiederherstellung</li>
                <li>Automatische Bereinigung alter Backups</li>
              </ul>
            </CardContent>
          </Card>
          
          <Card className="quechua-card">
            <CardHeader>
              <CardTitle>Monitoring und Alarmierung</CardTitle>
              <CardDescription>Proaktive Überwachung der Anwendung</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Unsere Anwendung implementiert umfassendes Monitoring und Alarmierung, um Probleme frühzeitig zu erkennen und zu beheben.
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>Echtzeit-Überwachung von System- und Anwendungsmetriken</li>
                <li>Gesundheitschecks für alle Komponenten</li>
                <li>Automatische Alarmierung bei Problemen</li>
                <li>Detaillierte Leistungsanalyse</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
