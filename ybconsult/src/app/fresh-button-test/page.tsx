import { GradientButton } from "@/components/ui/gradient-button"

export default function FreshButtonTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 to-blue-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold mb-4 text-black">🔥 FRESH BUTTON TEST - CACHE BYPASS</h1>
        <p className="text-lg mb-8 text-gray-700">
          Timestamp: {new Date().toISOString()} - This ensures fresh loading
        </p>
        
        <div className="mb-8 p-6 bg-red-100 border-l-4 border-red-500 rounded">
          <h2 className="text-xl font-bold text-red-800 mb-2">🚨 CRITICAL TEST</h2>
          <p className="text-red-700">
            If you still see the old Outline Button design, please do a <strong>HARD REFRESH</strong>:
            <br />• <strong>Mac:</strong> Cmd + Shift + R
            <br />• <strong>Windows:</strong> Ctrl + Shift + R
            <br />• <strong>Or open in Incognito/Private mode</strong>
          </p>
        </div>

        {/* Side by side comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white p-8 rounded-lg shadow-lg border-2 border-gray-300">
            <h3 className="text-2xl font-bold mb-6 text-gray-800">📋 DEFAULT BUTTON (Reference)</h3>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">This is what the Outline Button should match in size/alignment:</p>
              <GradientButton variant="default">Default Button Reference</GradientButton>
              <p className="text-xs text-gray-500">Note the size, padding, and border-radius</p>
            </div>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-lg border-2 border-purple-300">
            <h3 className="text-2xl font-bold mb-6 text-purple-800">🎯 OUTLINE BUTTON (Fixed)</h3>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">Should have transparent background + gradient border:</p>
              <GradientButton variant="outline">Outline Button Fixed</GradientButton>
              <p className="text-xs text-purple-600">Should match Default Button size exactly</p>
            </div>
          </div>
        </div>

        {/* Detailed test section */}
        <div className="bg-white p-8 rounded-lg shadow-lg border-2 border-green-300 mb-8">
          <h3 className="text-2xl font-bold mb-6 text-green-800">🧪 DETAILED OUTLINE BUTTON TEST</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-semibold text-green-700 mb-3">What you should see:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="bg-green-50 p-4 rounded">
                  <h5 className="font-bold text-green-800">✅ Base State:</h5>
                  <ul className="text-green-700 space-y-1 mt-2">
                    <li>• Transparent background (you see the white through)</li>
                    <li>• Dark gradient border (same colors as Default Button)</li>
                    <li>• Exact same size as Default Button</li>
                    <li>• Same border-radius (11px rounded corners)</li>
                  </ul>
                </div>
                <div className="bg-blue-50 p-4 rounded">
                  <h5 className="font-bold text-blue-800">✅ Hover State:</h5>
                  <ul className="text-blue-700 space-y-1 mt-2">
                    <li>• Border gradient animates smoothly</li>
                    <li>• Changes to warm colors (like Default Button hover)</li>
                    <li>• Same animation timing (0.5s)</li>
                    <li>• Border stays same thickness</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="border-t pt-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">🎯 Test the Outline Button:</h4>
              <div className="flex flex-wrap gap-4 items-center">
                <GradientButton variant="outline">Hover Over Me!</GradientButton>
                <span className="text-gray-600">← Hover to test gradient border animation</span>
              </div>
            </div>

            <div className="border-t pt-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">📏 Size Comparison:</h4>
              <div className="flex flex-wrap gap-4 items-start">
                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-2">Default</p>
                  <GradientButton variant="default">Test</GradientButton>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-2">Outline</p>
                  <GradientButton variant="outline">Test</GradientButton>
                </div>
                <div className="text-center">
                  <p className="text-xs text-gray-500 mb-2">Should be identical size!</p>
                  <div className="w-1 h-12 bg-red-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* All buttons for reference */}
        <div className="bg-gray-50 p-8 rounded-lg">
          <h3 className="text-2xl font-bold mb-6 text-gray-800">🎨 All Button Variants</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Default</p>
              <GradientButton variant="default">Default</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Variant</p>
              <GradientButton variant="variant">Variant</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Destructive</p>
              <GradientButton variant="destructive">Destructive</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Outline (FIXED)</p>
              <GradientButton variant="outline">Outline</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Ghost</p>
              <GradientButton variant="ghost">Ghost</GradientButton>
            </div>
            <div className="text-center space-y-2">
              <p className="text-sm font-medium text-gray-600">Link</p>
              <GradientButton variant="link">Link</GradientButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
