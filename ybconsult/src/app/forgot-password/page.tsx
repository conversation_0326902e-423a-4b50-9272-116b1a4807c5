"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";
import { Mail } from "lucide-react";

// Validierungsschema mit Zod
const forgotPasswordSchema = z.object({
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein"),
});

// Typ aus dem Schema ableiten
type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;

/**
 * Forgot Password Page
 * Ermöglicht Benutzern, einen Link zum Zurücksetzen ihres Passworts anzufordern
 */
export default function ForgotPasswordPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordInput>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  /**
   * Behandelt die Formularübermittlung für die Passwort-Zurücksetzung
   */
  const onSubmit: SubmitHandler<ForgotPasswordInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Passwort-Reset angefordert für:", data.email);

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Passwort-Reset-Anfrage fehlgeschlagen:", result.message);
        setError(result.message || "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
        toast.error("Anfrage Fehlgeschlagen", { description: result.message || "Ein Fehler ist aufgetreten." });
      } else {
        console.log("Passwort-Reset-Anfrage erfolgreich, E-Mail gesendet");
        toast.success("E-Mail Gesendet", { description: "Wenn ein Konto mit dieser E-Mail existiert, erhalten Sie einen Link zum Zurücksetzen Ihres Passworts." });
        setIsSubmitted(true);
      }
    } catch (e) {
      console.error("Ausnahme während der Passwort-Reset-Anfrage:", e);
      setError("Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Passwort vergessen?</CardTitle>
          <CardDescription className="text-center">
            Geben Sie Ihre E-Mail-Adresse ein, um einen Link zum Zurücksetzen Ihres Passworts zu erhalten.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isSubmitted ? (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">E-Mail-Adresse</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email")}
                  aria-invalid={errors.email ? "true" : "false"}
                />
                {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
              </div>

              {error && <p className="text-sm text-red-500 text-center">{error}</p>}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Wird gesendet..." : "Link zum Zurücksetzen senden"}
              </Button>
            </form>
          ) : (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <Mail className="h-12 w-12 text-primary" />
              </div>
              <h3 className="text-lg font-medium">E-Mail gesendet</h3>
              <p>
                Wenn ein Konto mit dieser E-Mail-Adresse existiert, haben wir einen Link zum Zurücksetzen Ihres Passworts gesendet.
              </p>
              <p className="text-sm text-muted-foreground">
                Bitte überprüfen Sie auch Ihren Spam-Ordner, falls Sie die E-Mail nicht finden können.
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/login" className="text-sm text-primary hover:underline">
            Zurück zur Anmeldung
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
