import { GradientButton } from "@/components/ui/gradient-button"

export default function ButtonTestNewPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-4xl font-bold mb-8 text-black">🚀 NEUE BUTTON TEST SEITE - CACHE UMGEHEN</h1>
      
      <div className="mb-8 p-4 bg-yellow-100 border-l-4 border-yellow-500">
        <p className="text-yellow-800 font-semibold">
          ⚠️ Diese Seite wurde erstellt, um Browser-Cache-Probleme zu umgehen!
        </p>
      </div>

      <div className="space-y-16">
        {/* Test der Button-Varianten */}
        <div className="space-y-8">
          <h2 className="text-2xl font-bold text-black border-b-2 border-gray-200 pb-2">
            🧪 BUTTON VARIANTEN TEST
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-4 p-6 bg-gray-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-black">Default Button</h3>
              <GradientButton variant="default">Default</GradientButton>
            </div>

            <div className="space-y-4 p-6 bg-red-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-red-700">Destructive Button</h3>
              <GradientButton variant="destructive">Destructive</GradientButton>
            </div>

            <div className="space-y-4 p-6 bg-purple-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-purple-700">Outline Button (NEU)</h3>
              <GradientButton variant="outline">Outline</GradientButton>
              <p className="text-xs text-purple-600">
                Sollte transparenten Hintergrund + Default Button Gradient als Border haben!
              </p>
            </div>

            <div className="space-y-4 p-6 bg-gray-100 rounded-lg border">
              <h3 className="text-lg font-semibold text-gray-700">Ghost Button</h3>
              <GradientButton variant="ghost">Ghost</GradientButton>
            </div>

            <div className="space-y-4 p-6 bg-indigo-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-indigo-700">Link Button</h3>
              <GradientButton variant="link">Link</GradientButton>
            </div>

            <div className="space-y-4 p-6 bg-blue-50 rounded-lg border">
              <h3 className="text-lg font-semibold text-blue-700">Variant Button</h3>
              <GradientButton variant="variant">Variant</GradientButton>
            </div>
          </div>
        </div>

        {/* Spezifischer Outline Button Test */}
        <div className="p-8 bg-purple-100 rounded-lg border-2 border-purple-300">
          <h3 className="text-xl font-bold mb-6 text-purple-800">🎯 OUTLINE BUTTON SPEZIAL-TEST</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="font-bold text-purple-700 mb-2">Was du sehen solltest:</h4>
              <ul className="text-sm text-purple-800 space-y-1">
                <li>• <strong>Transparenter Hintergrund</strong> (du siehst den lila Hintergrund durch)</li>
                <li>• <strong>Gradient-Border</strong> mit den Default Button Farben</li>
                <li>• <strong>Beim Hover</strong>: Border animiert wie Default Button</li>
              </ul>
            </div>
            
            <div className="flex gap-4 items-center">
              <GradientButton variant="outline">Test Outline Button</GradientButton>
              <span className="text-purple-700">← Hover über diesen Button!</span>
            </div>
            
            <div>
              <h4 className="font-bold text-purple-700 mb-2">Vergleich:</h4>
              <div className="flex gap-4">
                <div>
                  <p className="text-sm text-purple-600 mb-2">Default (Referenz):</p>
                  <GradientButton variant="default">Default</GradientButton>
                </div>
                <div>
                  <p className="text-sm text-purple-600 mb-2">Outline (Neu):</p>
                  <GradientButton variant="outline">Outline</GradientButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
