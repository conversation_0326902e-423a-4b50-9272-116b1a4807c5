// /Users/<USER>/Coding/ybconsulting/ybconsult/src/app/register/page.tsx
"use client";

import { useForm, SubmitHandler } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Grad<PERSON>Button } from "@/components/ui/gradient-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import Link from "next/link";

/**
 * @interface IRegisterFormInput
 * Definiert die Struktur der Eingabefelder für das Registrierungsformular.
 */
interface IRegisterFormInput {
  email: string;
  password: string;
  confirmPassword: string;
}

/**
 * RegisterPage Komponente für die Benutzerregistrierung.
 * Ermöglicht neuen Benutzern, ein Konto mit E-Mail und Passwort zu erstellen.
 */
export default function RegisterPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<IRegisterFormInput>();

  // Beobachte das Passwortfeld, um es mit dem Bestätigungspasswortfeld zu vergleichen
  const password = watch("password");

  /**
   * Behandelt die Formularübermittlung für die Benutzerregistrierung.
   * @param {IRegisterFormInput} data - Die Formulardaten mit E-Mail und Passwort.
   */
  const onSubmit: SubmitHandler<IRegisterFormInput> = async (data) => {
    setIsLoading(true);
    setError(null);
    console.log("Registrierungsversuch mit:", data.email); // Loggt den Registrierungsversuch

    try {
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: data.email, password: data.password }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("Registrierung fehlgeschlagen:", result.message); // Loggt Fehler bei der Registrierung
        setError(result.message || "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.");
        toast.error("Registrierung Fehlgeschlagen", { description: result.message || "Ein Fehler ist aufgetreten." });
      } else {
        console.log("Registrierung erfolgreich, Weiterleitung zum Login..."); // Loggt erfolgreiche Registrierung
        toast.success("Registrierung Erfolgreich", { description: "Sie werden zur Anmeldeseite weitergeleitet..." });
        router.push("/login"); // Leitet zur Anmeldeseite weiter
      }
    } catch (e: any) {
      console.error("Ausnahme während der Registrierung:", e); // Loggt Ausnahmen
      setError("Ein Netzwerkfehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung.");
      toast.error("Netzwerkfehler", { description: "Ein Fehler ist aufgetreten. Bitte überprüfen Sie Ihre Verbindung." });
    }
    setIsLoading(false);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Registrieren</CardTitle>
          <CardDescription className="text-center">
            Erstellen Sie ein neues Konto, um auf alle Funktionen zugreifen zu können.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-Mail</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email", {
                  required: "E-Mail ist erforderlich",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Bitte geben Sie eine gültige E-Mail-Adresse ein."
                  }
                })}
                aria-invalid={errors.email ? "true" : "false"}
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Passwort</Label>
              <Input
                id="password"
                type="password"
                placeholder="Ihr Passwort"
                {...register("password", {
                  required: "Passwort ist erforderlich",
                  minLength: {
                    value: 8,
                    message: "Das Passwort muss mindestens 8 Zeichen lang sein."
                  }
                })}
                aria-invalid={errors.password ? "true" : "false"}
              />
              {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Passwort bestätigen</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Passwort erneut eingeben"
                {...register("confirmPassword", {
                  required: "Passwortbestätigung ist erforderlich",
                  validate: (value) =>
                    value === password || "Die Passwörter stimmen nicht überein.",
                })}
                aria-invalid={errors.confirmPassword ? "true" : "false"}
              />
              {errors.confirmPassword && <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>}
            </div>
            {error && <p className="text-sm text-red-500 text-center">{error}</p>}
            <GradientButton type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Registrieren..." : "Konto erstellen"}
            </GradientButton>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-center text-sm">
          <p>
            Sie haben bereits ein Konto?{" "}
            <Link href="/login" className="font-medium text-primary hover:underline">
              Hier anmelden
            </Link>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}