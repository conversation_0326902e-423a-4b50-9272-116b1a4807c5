"use client"

import { GradientButton } from "@/components/ui/gradient-button"
import { useState } from "react"

export default function ButtonShowcasePage() {
  const [hoveredButton, setHoveredButton] = useState<string | null>(null)

  const buttonVariants = [
    {
      variant: "default" as const,
      name: "Default",
      description: "Primary gradient button with dark to purple gradient",
      colors: "Dark gradient (#000 → #88394c)",
      hoverColors: "Warm gradient (#c96287 → #000)",
      useCase: "Primary actions, main CTAs",
      bgColor: "bg-gray-50",
      borderColor: "border-gray-300"
    },
    {
      variant: "variant" as const,
      name: "Variant",
      description: "Alternative gradient style for secondary actions",
      colors: "Alternative gradient colors",
      hoverColors: "Enhanced gradient animation",
      useCase: "Secondary actions, alternatives",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-300"
    },
    {
      variant: "destructive" as const,
      name: "Destructive",
      description: "Red gradient theme for dangerous actions",
      colors: "Red gradient theme",
      hoverColors: "Darker red gradient",
      useCase: "Delete, remove, dangerous actions",
      bgColor: "bg-red-50",
      borderColor: "border-red-300"
    },
    {
      variant: "outline" as const,
      name: "Outline",
      description: "Transparent background with animated gradient border",
      colors: "Gradient border (#000 → #88394c)",
      hoverColors: "Warm gradient border (#c96287 → #000)",
      useCase: "Secondary actions, cancel buttons",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-300"
    },
    {
      variant: "ghost" as const,
      name: "Ghost",
      description: "Subtle gray gradient for minimal actions",
      colors: "Gray gradient (#374151 → #d1d5db)",
      hoverColors: "Lighter gray gradient",
      useCase: "Subtle actions, minimal UI",
      bgColor: "bg-gray-50",
      borderColor: "border-gray-300"
    },
    {
      variant: "link" as const,
      name: "Link",
      description: "Minimal link-style button with underline",
      colors: "Blue text with underline",
      hoverColors: "Darker blue + slide effect",
      useCase: "Navigation, text links",
      bgColor: "bg-indigo-50",
      borderColor: "border-indigo-300"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto p-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
            🎨 Gradient Button Showcase
          </h1>
          <p className="text-2xl text-gray-600 mb-4">
            Complete collection of modern gradient buttons with animations
          </p>
          <p className="text-lg text-gray-500 max-w-3xl mx-auto">
            Featuring the newly implemented Outline Button with gradient border animations,
            alongside all other button variants with consistent styling and smooth transitions.
          </p>
        </div>

        {/* Success Banner */}
        <div className="mb-16 p-8 bg-gradient-to-r from-green-100 to-emerald-100 border-l-8 border-green-500 rounded-xl shadow-lg">
          <div className="flex items-center mb-4">
            <div className="text-4xl mr-4">✅</div>
            <h2 className="text-3xl font-bold text-green-800">Outline Button Successfully Implemented!</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-green-700">
            <div>
              <h3 className="font-bold text-lg mb-2">🎯 Features:</h3>
              <ul className="space-y-1 text-sm">
                <li>• Transparent background</li>
                <li>• Gradient border animation</li>
                <li>• Perfect size matching</li>
                <li>• Smooth 0.5s transitions</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-lg mb-2">🚀 Technical:</h3>
              <ul className="space-y-1 text-sm">
                <li>• CSS pseudo-element approach</li>
                <li>• High specificity selectors</li>
                <li>• Default Button integration</li>
                <li>• Cross-browser compatibility</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-lg mb-2">✨ Design:</h3>
              <ul className="space-y-1 text-sm">
                <li>• Modern gradient aesthetics</li>
                <li>• Consistent button family</li>
                <li>• Accessible interactions</li>
                <li>• Responsive design</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Button Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
          {buttonVariants.map((button) => (
            <div
              key={button.variant}
              className={`${button.bgColor} p-8 rounded-2xl shadow-lg border-2 ${button.borderColor} transition-all duration-300 hover:shadow-xl hover:-translate-y-1`}
            >
              <div className="text-center space-y-6">
                <h3 className="text-2xl font-bold text-gray-800 flex items-center justify-center gap-2">
                  {button.name}
                </h3>

                <p className="text-gray-600 text-sm leading-relaxed">
                  {button.description}
                </p>

                <div className="flex justify-center">
                  <GradientButton
                    variant={button.variant}
                    onMouseEnter={() => setHoveredButton(button.variant)}
                    onMouseLeave={() => setHoveredButton(null)}
                  >
                    {button.name} Button
                  </GradientButton>
                </div>

                <div className="space-y-3 text-xs text-gray-600">
                  <div>
                    <strong className="text-gray-800">Base:</strong> {button.colors}
                  </div>
                  <div>
                    <strong className="text-gray-800">Hover:</strong> {button.hoverColors}
                  </div>
                  <div>
                    <strong className="text-gray-800">Use Case:</strong> {button.useCase}
                  </div>
                </div>

                {hoveredButton === button.variant && (
                  <div className="text-xs text-blue-600 font-medium animate-pulse">
                    🎯 Hover detected! Animation active
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Interactive Comparison Section */}
        <div className="bg-white p-10 rounded-2xl shadow-xl border border-gray-200 mb-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">
            🔄 Interactive Button Comparison
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {buttonVariants.map((button) => (
              <div key={button.variant} className="text-center space-y-3">
                <h4 className="font-semibold text-gray-700">{button.name}</h4>
                <GradientButton variant={button.variant}>
                  Test {button.name}
                </GradientButton>
              </div>
            ))}
          </div>

          <div className="mt-8 p-6 bg-blue-50 rounded-xl">
            <p className="text-center text-blue-800 font-medium">
              ✨ Hover over any button to see the gradient animations in action!
            </p>
          </div>
        </div>

        {/* Size Consistency Test */}
        <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-10 rounded-2xl shadow-xl mb-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-purple-800">
            📏 Size Consistency Verification
          </h2>

          <div className="flex flex-wrap justify-center items-center gap-4 mb-6">
            <GradientButton variant="default">Default</GradientButton>
            <GradientButton variant="outline">Outline</GradientButton>
            <GradientButton variant="destructive">Destructive</GradientButton>
            <GradientButton variant="ghost">Ghost</GradientButton>
          </div>

          <div className="text-center space-y-2">
            <p className="text-purple-700 font-medium">
              ✅ All buttons should have identical dimensions and alignment
            </p>
            <p className="text-sm text-purple-600">
              Same padding (px-9 py-4), border-radius (11px), and min-width (132px)
            </p>
          </div>
        </div>

        {/* Technical Implementation Details */}
        <div className="bg-gray-50 p-10 rounded-2xl shadow-xl border border-gray-300">
          <h2 className="text-3xl font-bold text-center mb-8 text-gray-800">
            ⚙️ Technical Implementation Overview
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-bold text-gray-700 mb-4">🎨 Outline Button Innovation</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <span className="text-green-500 font-bold">✓</span>
                  <span>Transparent background with gradient border</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-green-500 font-bold">✓</span>
                  <span>CSS pseudo-element (::after) for gradient border</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-green-500 font-bold">✓</span>
                  <span>High specificity CSS (.gradient-button.gradient-button-outline)</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-green-500 font-bold">✓</span>
                  <span>Fallback solid border for reliability</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-green-500 font-bold">✓</span>
                  <span>Same gradient variables as Default Button</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-bold text-gray-700 mb-4">🚀 System Features</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">⚡</span>
                  <span>Consistent 0.5s animation timing across all variants</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">⚡</span>
                  <span>CSS custom properties for dynamic gradient control</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">⚡</span>
                  <span>Radial gradient animations with position changes</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">⚡</span>
                  <span>Perfect dimensional consistency (132px min-width)</span>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">⚡</span>
                  <span>Accessible focus states and keyboard navigation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
