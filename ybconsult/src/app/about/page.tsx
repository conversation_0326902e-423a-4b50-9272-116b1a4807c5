import { GradientButton } from "@/components/ui/gradient-button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Über uns | YoungMobility",
  description: "Erfahren Sie mehr über YoungMobility, unsere Mission und unser Team. Wir revolutionieren die Fahrzeuglogistik mit innovativen Lösungen.",
  keywords: "YoungMobility, Über uns, Fahrzeugüberführung, Fahrzeuglogistik, Team, Mission, Vision",
};

// Team-Mitglieder
const teamMembers = [
  {
    name: "<PERSON>",
    position: "Gr<PERSON>nder & CEO",
    bio: "Markus hat über 15 Jahre Erfahrung in der Automobilbranche und erkannte die Ineffizienzen in der Fahrzeuglogistik. Mit YoungMobility hat er eine Plattform geschaffen, die diese Herausforderungen löst.",
    imageUrl: "/images/team-markus.jpg",
  },
  {
    name: "<PERSON>",
    position: "CO<PERSON>",
    bio: "<PERSON> bringt umfangreiche Erfahrung im Bereich Operations und Logistik mit. Sie sorgt dafür, dass alle Prozesse bei YoungMobility reibungslos funktionieren.",
    imageUrl: "/images/team-julia.jpg",
  },
  {
    name: "Thomas Müller",
    position: "CTO",
    bio: "Thomas ist ein erfahrener Technologie-Experte mit Hintergrund in der Entwicklung von Plattformen für die Sharing Economy. Er leitet die technische Entwicklung von YoungMobility.",
    imageUrl: "/images/team-thomas.jpg",
  },
  {
    name: "Sarah Fischer",
    position: "Head of Customer Success",
    bio: "Sarah stellt sicher, dass unsere Kunden den maximalen Nutzen aus unserer Plattform ziehen. Sie leitet das Customer Success Team und entwickelt kontinuierlich neue Serviceangebote.",
    imageUrl: "/images/team-sarah.jpg",
  },
];

// Meilensteine
const milestones = [
  {
    year: "2020",
    title: "Gründung",
    description: "YoungMobility wird mit der Vision gegründet, die Fahrzeuglogistik zu revolutionieren.",
  },
  {
    year: "2021",
    title: "Erste Pilotprojekte",
    description: "Start der ersten Pilotprojekte mit ausgewählten Autovermietungen und Leasing-Unternehmen.",
  },
  {
    year: "2022",
    title: "Markteinführung",
    description: "Offizielle Markteinführung der YoungMobility-Plattform nach erfolgreichen Pilotprojekten.",
  },
  {
    year: "2023",
    title: "Expansion",
    description: "Expansion in weitere Regionen und Erweiterung des Kundenstamms auf Autohändler und Flottenmanagement.",
  },
  {
    year: "2024",
    title: "Technologische Innovation",
    description: "Einführung neuer Features wie KI-basierte Routenoptimierung und erweitertes Reporting.",
  },
];

// Werte
const values = [
  {
    title: "Innovation",
    description: "Wir denken neu und entwickeln kontinuierlich innovative Lösungen für die Herausforderungen der Fahrzeuglogistik.",
    icon: "💡",
  },
  {
    title: "Zuverlässigkeit",
    description: "Unsere Kunden und Fahrer können sich auf uns verlassen. Zuverlässigkeit ist die Grundlage unseres Geschäfts.",
    icon: "🤝",
  },
  {
    title: "Transparenz",
    description: "Wir schaffen Transparenz in allen Prozessen und kommunizieren offen mit unseren Kunden und Partnern.",
    icon: "👁️",
  },
  {
    title: "Nachhaltigkeit",
    description: "Durch die Optimierung von Fahrzeugbewegungen und die Reduzierung von Leerfahrten tragen wir aktiv zum Umweltschutz bei.",
    icon: "🌱",
  },
];

export default function AboutPage() {
  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[40vh] min-h-[300px] flex items-center justify-center text-center">
        <Image
          src="/images/about-hero.jpg"
          alt="Über YoungMobility"
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="relative z-10 p-8 md:p-12 bg-black/40 backdrop-blur-sm rounded-lg max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
            Über YoungMobility
          </h1>
          <p className="text-lg md:text-xl text-gray-100 mb-6 max-w-3xl mx-auto">
            Wir revolutionieren die Fahrzeuglogistik mit innovativen Lösungen für Geschäftskunden und Fahrer.
          </p>
        </div>
      </section>

      {/* Unsere Geschichte */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6 text-gray-900">Unsere Geschichte</h2>
          <div className="flex flex-col md:flex-row gap-8 items-center">
            <div className="md:w-1/2">
              <p className="text-lg text-gray-700 mb-4">
                YoungMobility wurde 2020 von Markus Weber gegründet, der nach über 15 Jahren in der Automobilbranche die Ineffizienzen in der Fahrzeuglogistik erkannte. Seine Vision war es, eine Plattform zu schaffen, die Geschäftskunden mit qualifizierten Fahrern verbindet, um Fahrzeugüberführungen effizienter, transparenter und kostengünstiger zu gestalten.
              </p>
              <p className="text-lg text-gray-700 mb-4">
                Nach erfolgreichen Pilotprojekten mit ausgewählten Autovermietungen und Leasing-Unternehmen wurde die YoungMobility-Plattform 2022 offiziell auf den Markt gebracht. Seitdem haben wir unser Angebot kontinuierlich erweitert und verbessert, um den Bedürfnissen unserer Kunden gerecht zu werden.
              </p>
              <p className="text-lg text-gray-700">
                Heute ist YoungMobility ein führender Anbieter von Fahrzeugüberführungslösungen für Geschäftskunden aus verschiedenen Branchen. Unser Netzwerk aus qualifizierten Fahrern wächst stetig, und wir arbeiten kontinuierlich an der Weiterentwicklung unserer Plattform, um die Fahrzeuglogistik noch effizienter zu gestalten.
              </p>
            </div>
            <div className="md:w-1/2 relative h-80 rounded-xl overflow-hidden">
              <Image
                src="/images/about-story.jpg"
                alt="Die Geschichte von YoungMobility"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Unsere Mission & Vision */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="bg-white p-8 rounded-xl shadow-md">
                <h3 className="text-2xl font-bold mb-4 text-gray-900">Unsere Mission</h3>
                <p className="text-lg text-gray-700">
                  Unsere Mission ist es, die Fahrzeuglogistik zu revolutionieren, indem wir Geschäftskunden mit qualifizierten Fahrern verbinden. Wir schaffen Transparenz, Effizienz und Kosteneinsparungen in einem traditionell komplexen und intransparenten Markt.
                </p>
              </div>
              <div className="bg-white p-8 rounded-xl shadow-md">
                <h3 className="text-2xl font-bold mb-4 text-gray-900">Unsere Vision</h3>
                <p className="text-lg text-gray-700">
                  Unsere Vision ist eine Welt, in der Fahrzeugüberführungen nahtlos, effizient und nachhaltig sind. Wir streben danach, der führende Anbieter von Fahrzeuglogistiklösungen zu werden und kontinuierlich innovative Technologien zu entwickeln, die die Branche voranbringen.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Unsere Werte */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Unsere Werte</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {values.map((value, index) => (
            <div key={index} className="bg-white p-8 rounded-xl shadow-md text-center">
              <div className="text-5xl mb-4 flex justify-center">{value.icon}</div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">{value.title}</h3>
              <p className="text-gray-700">{value.description}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Meilensteine */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Unsere Meilensteine</h2>

          <div className="max-w-4xl mx-auto">
            <div className="relative border-l-4 border-accent pl-8 ml-4 space-y-10">
              {milestones.map((milestone, index) => (
                <div key={index} className="relative">
                  <div className="absolute -left-12 w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white font-bold">
                    {index + 1}
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="text-sm text-accent font-semibold mb-1">{milestone.year}</div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-900">{milestone.title}</h3>
                    <p className="text-gray-700">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Unser Team */}
      <section className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Unser Team</h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {teamMembers.map((member, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="relative h-64">
                <Image
                  src={member.imageUrl}
                  alt={member.name}
                  fill
                  className="object-cover"
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-1 text-gray-900">{member.name}</h3>
                <p className="text-accent mb-3">{member.position}</p>
                <p className="text-gray-700 text-sm">{member.bio}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* CTA */}
      <section className="bg-accent text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Werden Sie Teil unserer Geschichte</h2>
          <p className="text-xl mb-10 max-w-3xl mx-auto">
            Entdecken Sie, wie YoungMobility Ihre Fahrzeuglogistik revolutionieren kann, oder werden Sie als Fahrer Teil unseres wachsenden Netzwerks.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton asChild>
              <Link href="/register-client">Für Unternehmen</Link>
            </GradientButton>
            <GradientButton variant="variant" asChild>
              <Link href="/youngmovers">Für Fahrer</Link>
            </GradientButton>
          </div>
        </div>
      </section>
    </div>
  );
}
