import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Blog | YoungMobility",
  description: "Erfahren Sie mehr über Trends und Entwicklungen in der Fahrzeuglogistik, Erfolgsgeschichten unserer Kunden und Neuigkeiten von YoungMobility.",
  keywords: "Blog, Fahrzeuglogistik, Fahrzeugüberführung, Autotransport, Logistiktrends, YoungMobility",
};

// Blog-Kategorien
const categories = [
  "Alle",
  "Branchentrends",
  "Erfolgsgeschichten",
  "Produktupdates",
  "Tipps & Tricks",
  "Unternehmensnews",
];

// Blog-Beiträge
const blogPosts = [
  {
    id: "die-zukunft-der-fahrzeuglogistik",
    title: "Die Zukunft der Fahrzeuglogistik: Trends und Entwicklungen",
    excerpt: "Die Fahrzeuglogistik befindet sich im Wandel. Neue Technologien, veränderte Kundenerwartungen und der Fokus auf Nachhaltigkeit prägen die Zukunft der Branche.",
    imageUrl: "/images/blog-future-logistics.jpg",
    category: "Branchentrends",
    author: "<PERSON> Weber",
    authorRole: "CEO",
    authorImageUrl: "/images/team-markus.jpg",
    date: "15. Mai 2024",
    readTime: "5 min",
  },
  {
    id: "wie-eurorent-kosten-reduzierte",
    title: "Wie EuroRent seine Logistikkosten um 25% reduzierte",
    excerpt: "Erfahren Sie, wie EuroRent durch die Implementierung von YoungMobility seine Logistikkosten deutlich senken und die Fahrzeugverfügbarkeit verbessern konnte.",
    imageUrl: "/images/blog-case-study.jpg",
    category: "Erfolgsgeschichten",
    author: "Sarah Fischer",
    authorRole: "Head of Customer Success",
    authorImageUrl: "/images/team-sarah.jpg",
    date: "28. April 2024",
    readTime: "7 min",
  },
  {
    id: "neue-features-q2-2024",
    title: "Neue Features: Was Q2 2024 für unsere Kunden bereithält",
    excerpt: "Entdecken Sie die neuen Funktionen und Verbesserungen, die wir im zweiten Quartal 2024 für unsere Plattform geplant haben.",
    imageUrl: "/images/blog-product-update.jpg",
    category: "Produktupdates",
    author: "Thomas Müller",
    authorRole: "CTO",
    authorImageUrl: "/images/team-thomas.jpg",
    date: "10. April 2024",
    readTime: "4 min",
  },
  {
    id: "5-tipps-fuer-effiziente-fahrzeugueberfuehrungen",
    title: "5 Tipps für effizientere Fahrzeugüberführungen",
    excerpt: "Mit diesen fünf Tipps optimieren Sie Ihre Fahrzeugüberführungen und sparen Zeit und Kosten bei der Fahrzeuglogistik.",
    imageUrl: "/images/blog-tips.jpg",
    category: "Tipps & Tricks",
    author: "Julia Schmidt",
    authorRole: "COO",
    authorImageUrl: "/images/team-julia.jpg",
    date: "22. März 2024",
    readTime: "6 min",
  },
  {
    id: "youngmobility-expandiert-nach-oesterreich",
    title: "YoungMobility expandiert nach Österreich",
    excerpt: "Wir freuen uns, unsere Expansion nach Österreich bekannt zu geben. Ab sofort bieten wir unsere Dienstleistungen auch in Wien, Graz und Linz an.",
    imageUrl: "/images/blog-expansion.jpg",
    category: "Unternehmensnews",
    author: "Markus Weber",
    authorRole: "CEO",
    authorImageUrl: "/images/team-markus.jpg",
    date: "5. März 2024",
    readTime: "3 min",
  },
  {
    id: "nachhaltigkeit-in-der-fahrzeuglogistik",
    title: "Nachhaltigkeit in der Fahrzeuglogistik: Herausforderungen und Lösungen",
    excerpt: "Die Fahrzeuglogistik steht vor der Herausforderung, nachhaltiger zu werden. Wir beleuchten die aktuellen Entwicklungen und zeigen Lösungsansätze auf.",
    imageUrl: "/images/blog-sustainability.jpg",
    category: "Branchentrends",
    author: "Julia Schmidt",
    authorRole: "COO",
    authorImageUrl: "/images/team-julia.jpg",
    date: "18. Februar 2024",
    readTime: "8 min",
  },
];

export default function BlogPage() {
  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[40vh] min-h-[300px] flex items-center justify-center text-center">
        <Image
          src="/images/blog-hero.jpg"
          alt="YoungMobility Blog"
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="relative z-10 p-8 md:p-12 bg-black/40 backdrop-blur-sm rounded-lg max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-4">
            YoungMobility Blog
          </h1>
          <p className="text-lg md:text-xl text-gray-100 mb-6 max-w-3xl mx-auto">
            Erfahren Sie mehr über Trends und Entwicklungen in der Fahrzeuglogistik, Erfolgsgeschichten unserer Kunden und Neuigkeiten von YoungMobility.
          </p>
        </div>
      </section>

      {/* Kategorien */}
      <section className="container mx-auto px-4">
        <div className="flex flex-wrap gap-3 justify-center mb-12">
          {categories.map((category, index) => (
            <Button
              key={index}
              variant={index === 0 ? "default" : "outline"}
              className={index === 0 ? "bg-accent hover:bg-accent/90" : ""}
            >
              {category}
            </Button>
          ))}
        </div>
      </section>

      {/* Featured Post */}
      <section className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 md:grid-cols-2">
              <div className="relative h-64 md:h-auto">
                <Image
                  src={blogPosts[0].imageUrl}
                  alt={blogPosts[0].title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              </div>
              <div className="p-8">
                <div className="flex items-center mb-4">
                  <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm">{blogPosts[0].category}</span>
                  <span className="text-gray-500 text-sm ml-4">{blogPosts[0].date} • {blogPosts[0].readTime} Lesezeit</span>
                </div>
                <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-900">{blogPosts[0].title}</h2>
                <p className="text-gray-700 mb-6">{blogPosts[0].excerpt}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3 relative">
                      <Image
                        src={blogPosts[0].authorImageUrl}
                        alt={blogPosts[0].author}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{blogPosts[0].author}</p>
                      <p className="text-sm text-gray-500">{blogPosts[0].authorRole}</p>
                    </div>
                  </div>
                  <Button asChild>
                    <Link href={`/blog/${blogPosts[0].id}`}>Weiterlesen</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {blogPosts.slice(1).map((post) => (
            <div key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col h-full">
              <div className="relative h-48">
                <Image
                  src={post.imageUrl}
                  alt={post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
              </div>
              <div className="p-6 flex-grow flex flex-col">
                <div className="flex items-center mb-3">
                  <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm">{post.category}</span>
                  <span className="text-gray-500 text-sm ml-auto">{post.readTime}</span>
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{post.title}</h3>
                <p className="text-gray-700 mb-4 flex-grow">{post.excerpt}</p>
                <div className="flex items-center justify-between mt-auto pt-4 border-t border-gray-100">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full overflow-hidden mr-2 relative">
                      <Image
                        src={post.authorImageUrl}
                        alt={post.author}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <p className="font-medium text-sm text-gray-900">{post.author}</p>
                      <p className="text-xs text-gray-500">{post.date}</p>
                    </div>
                  </div>
                  <Button asChild variant="ghost" size="sm">
                    <Link href={`/blog/${post.id}`}>Lesen</Link>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Newsletter */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-4 text-gray-900">Newsletter abonnieren</h2>
            <p className="text-lg text-gray-600 mb-8">
              Bleiben Sie auf dem Laufenden über die neuesten Trends in der Fahrzeuglogistik und erhalten Sie exklusive Einblicke und Tipps.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-lg mx-auto">
              <input
                type="email"
                placeholder="Ihre E-Mail-Adresse"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
              <Button className="bg-accent hover:bg-accent/90 whitespace-nowrap">
                Abonnieren
              </Button>
            </div>
            <p className="text-xs text-gray-500 mt-4">
              Wir respektieren Ihre Privatsphäre. Sie können den Newsletter jederzeit abbestellen.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
