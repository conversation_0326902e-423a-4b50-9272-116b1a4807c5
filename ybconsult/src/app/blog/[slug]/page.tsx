import { GradientButton } from "@/components/ui/gradient-button";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import { notFound } from "next/navigation";

// Blog-Beiträge
const blogPosts = [
  {
    id: "die-zukunft-der-fahrzeuglogistik",
    title: "Die Zukunft der Fahrzeuglogistik: Trends und Entwicklungen",
    excerpt: "Die Fahrzeuglogistik befindet sich im Wandel. Neue Technologien, veränderte Kundenerwartungen und der Fokus auf Nachhaltigkeit prägen die Zukunft der Branche.",
    content: `
      <p>Die Fahrzeuglogistik steht vor einem bedeutenden Wandel. Neue Technologien, veränderte Kundenerwartungen und der zunehmende Fokus auf Nachhaltigkeit prägen die Zukunft der Branche. In diesem Artikel beleuchten wir die wichtigsten Trends und Entwicklungen, die die Fahrzeuglogistik in den kommenden Jahren maßgeblich beeinflussen werden.</p>

      <h2>Digitalisierung und Automatisierung</h2>

      <p>Die Digitalisierung hat die Fahrzeuglogistik bereits grundlegend verändert, doch dieser Prozess ist noch lange nicht abgeschlossen. Plattformen wie YoungMobility revolutionieren die Art und Weise, wie Fahrzeugüberführungen organisiert und durchgeführt werden. Durch den Einsatz von künstlicher Intelligenz und Machine Learning können Routen optimiert, Kosten reduziert und die Effizienz gesteigert werden.</p>

      <p>Auch die Automatisierung spielt eine immer größere Rolle. Autonome Fahrzeuge könnten in Zukunft einen Teil der Fahrzeugüberführungen übernehmen, insbesondere auf langen Strecken oder in kontrollierten Umgebungen wie Häfen oder Logistikzentren. Dies würde nicht nur die Kosten senken, sondern auch die Sicherheit erhöhen und den Fahrermangel reduzieren.</p>

      <h2>Nachhaltigkeit und Umweltschutz</h2>

      <p>Der Klimawandel und die zunehmende Bedeutung von Nachhaltigkeit haben auch Auswirkungen auf die Fahrzeuglogistik. Unternehmen stehen unter Druck, ihre CO2-Emissionen zu reduzieren und umweltfreundlichere Lösungen zu implementieren. Dies führt zu einer verstärkten Nutzung von alternativen Antrieben, einer Optimierung von Routen zur Vermeidung von Leerfahrten und einer insgesamt effizienteren Gestaltung der Logistikprozesse.</p>

      <p>Plattformen wie YoungMobility tragen zur Nachhaltigkeit bei, indem sie Leerfahrten reduzieren und die vorhandenen Ressourcen optimal nutzen. Durch die intelligente Vernetzung von Fahrern und Aufträgen können Fahrzeuge effizienter bewegt werden, was zu einer Reduzierung der CO2-Emissionen führt.</p>

      <h2>Veränderte Kundenerwartungen</h2>

      <p>Die Erwartungen der Kunden haben sich in den letzten Jahren stark verändert. Transparenz, Flexibilität und Geschwindigkeit stehen im Mittelpunkt. Kunden möchten jederzeit wissen, wo sich ihre Fahrzeuge befinden und wann sie ankommen werden. Sie erwarten flexible Lösungen, die sich an ihre individuellen Bedürfnisse anpassen, und sie möchten, dass alles schnell und unkompliziert abläuft.</p>

      <p>Um diesen Erwartungen gerecht zu werden, setzen immer mehr Unternehmen auf digitale Plattformen und Echtzeit-Tracking. YoungMobility bietet seinen Kunden volle Transparenz über den Status ihrer Fahrzeugüberführungen und ermöglicht eine flexible Planung und Durchführung.</p>

      <h2>Elektromobilität und neue Antriebsarten</h2>

      <p>Die zunehmende Verbreitung von Elektrofahrzeugen und anderen alternativen Antrieben stellt die Fahrzeuglogistik vor neue Herausforderungen. Elektrofahrzeuge haben spezifische Anforderungen an die Überführung, wie beispielsweise die Notwendigkeit von Ladestopps auf langen Strecken oder spezielle Kenntnisse der Fahrer im Umgang mit dieser Technologie.</p>

      <p>Gleichzeitig bietet die Elektromobilität auch Chancen für die Fahrzeuglogistik. Durch den Einsatz von Elektrofahrzeugen für Überführungen können Unternehmen ihre CO2-Emissionen reduzieren und ihr Engagement für Nachhaltigkeit unter Beweis stellen.</p>

      <h2>Fazit</h2>

      <p>Die Fahrzeuglogistik befindet sich in einem tiefgreifenden Wandel. Digitalisierung, Nachhaltigkeit, veränderte Kundenerwartungen und neue Antriebsarten prägen die Zukunft der Branche. Unternehmen, die diese Trends frühzeitig erkennen und in ihre Strategien integrieren, werden langfristig erfolgreich sein.</p>

      <p>YoungMobility steht an der Spitze dieser Entwicklung und bietet innovative Lösungen für die Herausforderungen der modernen Fahrzeuglogistik. Durch die Kombination von Technologie, Nachhaltigkeit und Kundenorientierung gestalten wir die Zukunft der Fahrzeugüberführungen.</p>
    `,
    imageUrl: "/images/blog-future-logistics.jpg",
    category: "Branchentrends",
    author: "Markus Weber",
    authorRole: "CEO",
    authorImageUrl: "/images/team-markus.jpg",
    date: "15. Mai 2024",
    readTime: "5 min",
    tags: ["Fahrzeuglogistik", "Digitalisierung", "Nachhaltigkeit", "Elektromobilität", "Trends"],
  },
  {
    id: "wie-eurorent-kosten-reduzierte",
    title: "Wie EuroRent seine Logistikkosten um 25% reduzierte",
    excerpt: "Erfahren Sie, wie EuroRent durch die Implementierung von YoungMobility seine Logistikkosten deutlich senken und die Fahrzeugverfügbarkeit verbessern konnte.",
    content: `
      <p>EuroRent, eine der führenden Autovermietungen in Deutschland mit 25 Standorten, stand vor der Herausforderung, die Fahrzeugverfügbarkeit an allen Standorten zu optimieren. Besonders an Wochenenden und zu Ferienzeiten kam es zu Engpässen an einigen Standorten, während an anderen Standorten Fahrzeuge ungenutzt blieben. Die traditionellen Methoden der Fahrzeugüberführung waren ineffizient, kostspielig und zeitaufwendig.</p>

      <h2>Die Herausforderung</h2>

      <p>EuroRent hatte mit mehreren Herausforderungen zu kämpfen:</p>

      <ul>
        <li>Ungleiche Fahrzeugverteilung: An einigen Standorten gab es zu viele Fahrzeuge, während an anderen Standorten Engpässe herrschten.</li>
        <li>Hohe Logistikkosten: Die traditionellen Methoden der Fahrzeugüberführung waren kostspielig und ineffizient.</li>
        <li>Mangelnde Transparenz: Es fehlte an Echtzeit-Informationen über den Status und Fortschritt der Überführungen.</li>
        <li>Lange Vorlaufzeiten: Die Organisation von Überführungen war zeitaufwendig und unflexibel.</li>
      </ul>

      <p>Diese Herausforderungen führten zu Umsatzeinbußen durch nicht vermietete Fahrzeuge an einigen Standorten und unzufriedene Kunden an anderen Standorten, die keine Fahrzeuge mieten konnten.</p>

      <h2>Die Lösung</h2>

      <p>EuroRent entschied sich für die Implementierung von YoungMobility als Lösung für ihre Fahrzeuglogistik. Die Plattform wurde in das bestehende Buchungssystem integriert und ermöglicht nun die automatische Planung von Überführungen basierend auf Verfügbarkeit und Nachfrageprognosen.</p>

      <p>Die Implementierung umfasste folgende Schritte:</p>

      <ol>
        <li>Integration der YoungMobility-API in das bestehende Buchungssystem von EuroRent</li>
        <li>Schulung der Mitarbeiter an allen Standorten</li>
        <li>Einrichtung eines automatisierten Berichtssystems für die Analyse der Fahrzeugverfügbarkeit</li>
        <li>Implementierung von Echtzeit-Tracking für alle Überführungen</li>
      </ol>

      <p>Durch die Integration in das bestehende System konnte EuroRent den Überführungsbedarf automatisch ermitteln und Aufträge direkt über die YoungMobility-Plattform erstellen. Die Fahrer werden automatisch benachrichtigt und können die Aufträge annehmen. Während der Überführung können die Mitarbeiter von EuroRent den Status in Echtzeit verfolgen.</p>

      <h2>Die Ergebnisse</h2>

      <p>Nach sechs Monaten der Nutzung von YoungMobility konnte EuroRent beeindruckende Ergebnisse verzeichnen:</p>

      <ul>
        <li><strong>30% Reduzierung von Standzeiten:</strong> Durch die optimierte Fahrzeugverteilung konnten die Standzeiten deutlich reduziert werden.</li>
        <li><strong>15% Steigerung der Fahrzeugauslastung:</strong> Die verbesserte Verfügbarkeit führte zu einer höheren Auslastung der Flotte.</li>
        <li><strong>Verbesserung der Kundenzufriedenheit:</strong> Durch die höhere Fahrzeugverfügbarkeit konnten mehr Kundenwünsche erfüllt werden.</li>
        <li><strong>Reduzierung der Logistikkosten um 25%:</strong> Die effizientere Organisation und Durchführung der Überführungen führte zu erheblichen Kosteneinsparungen.</li>
      </ul>

      <p>Besonders beeindruckend war die Reduzierung der Logistikkosten um 25%. Dies wurde durch mehrere Faktoren erreicht:</p>

      <ul>
        <li>Optimierte Routen und Vermeidung von Leerfahrten</li>
        <li>Reduzierung des administrativen Aufwands durch Automatisierung</li>
        <li>Flexible und bedarfsgerechte Überführungen statt fester Schedules</li>
        <li>Wettbewerbsfähige Preise durch das YoungMobility-Netzwerk</li>
      </ul>

      <h2>Fazit</h2>

      <p>Die Implementierung von YoungMobility hat EuroRent geholfen, seine Fahrzeuglogistik zu optimieren, die Kosten zu senken und die Kundenzufriedenheit zu verbessern. Durch die nahtlose Integration in das bestehende System und die intuitive Bedienung konnte die Lösung schnell und effektiv eingeführt werden.</p>

      <p>"YoungMobility hat unsere Fahrzeugüberführungen komplett revolutioniert. Wir sparen Zeit, Geld und haben volle Transparenz über alle Prozesse. Die Plattform ist intuitiv zu bedienen und die Fahrer sind zuverlässig und professionell. Ich kann YoungMobility jedem Unternehmen empfehlen, das seine Fahrzeuglogistik optimieren möchte." - Michael Schmidt, Flottenmanager bei EuroRent</p>

      <p>EuroRent plant, die Zusammenarbeit mit YoungMobility weiter auszubauen und die Plattform auch für andere logistische Herausforderungen zu nutzen, wie beispielsweise die Überführung von Fahrzeugen zu Werkstätten oder die Auslieferung von Neufahrzeugen an Kunden.</p>
    `,
    imageUrl: "/images/blog-case-study.jpg",
    category: "Erfolgsgeschichten",
    author: "Sarah Fischer",
    authorRole: "Head of Customer Success",
    authorImageUrl: "/images/team-sarah.jpg",
    date: "28. April 2024",
    readTime: "7 min",
    tags: ["Fallstudie", "Autovermietung", "Kostenreduktion", "Erfolgsgeschichte", "Kundenreferenz"],
  },
  // Weitere Blog-Beiträge...
];

// Generiere Metadaten für die Seite basierend auf dem Slug
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const post = blogPosts.find(post => post.id === params.slug);

  if (!post) {
    return {
      title: "Beitrag nicht gefunden | YoungMobility Blog",
      description: "Der gesuchte Beitrag wurde nicht gefunden.",
    };
  }

  return {
    title: `${post.title} | YoungMobility Blog`,
    description: post.excerpt,
    keywords: post.tags?.join(", ") || "YoungMobility, Blog, Fahrzeuglogistik",
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: [
        {
          url: post.imageUrl,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      type: "article",
      publishedTime: post.date,
      authors: [post.author],
      tags: post.tags,
    },
  };
}

// Generiere statische Pfade für alle Blog-Beiträge
export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.id,
  }));
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = blogPosts.find(post => post.id === params.slug);

  // Wenn der Beitrag nicht gefunden wurde, zeige 404
  if (!post) {
    notFound();
  }

  // Ähnliche Beiträge (gleiche Kategorie, aber nicht der aktuelle Beitrag)
  const relatedPosts = blogPosts
    .filter(p => p.category === post.category && p.id !== post.id)
    .slice(0, 3);

  return (
    <div className="space-y-16 py-8">
      {/* Hero Sektion */}
      <section className="relative h-[50vh] min-h-[400px] flex items-center justify-center text-center">
        <Image
          src={post.imageUrl}
          alt={post.title}
          fill
          className="object-cover"
          sizes="100vw"
          quality={90}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
        <div className="relative z-10 p-8 md:p-12 max-w-4xl mx-auto mt-auto">
          <div className="flex items-center justify-center mb-4">
            <span className="bg-accent/10 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">{post.category}</span>
            <span className="text-gray-200 text-sm ml-4">{post.date} • {post.readTime} Lesezeit</span>
          </div>
          <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">
            {post.title}
          </h1>
          <div className="flex items-center justify-center">
            <div className="w-12 h-12 rounded-full overflow-hidden mr-4 relative">
              <Image
                src={post.authorImageUrl}
                alt={post.author}
                fill
                className="object-cover"
              />
            </div>
            <div className="text-left">
              <p className="font-medium text-white">{post.author}</p>
              <p className="text-sm text-gray-200">{post.authorRole}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Artikel Inhalt */}
      <section className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: post.content }}></div>

          {/* Tags */}
          {post.tags && (
            <div className="mt-12 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold mb-3 text-gray-900">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <span key={index} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Teilen */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold mb-3 text-gray-900">Teilen</h3>
            <div className="flex gap-3">
              <Button variant="outline" size="sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="mr-2" viewBox="0 0 16 16">
                  <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/>
                </svg>
                Twitter
              </Button>
              <Button variant="outline" size="sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="mr-2" viewBox="0 0 16 16">
                  <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                </svg>
                Facebook
              </Button>
              <Button variant="outline" size="sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="mr-2" viewBox="0 0 16 16">
                  <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/>
                </svg>
                LinkedIn
              </Button>
              <Button variant="outline" size="sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="mr-2" viewBox="0 0 16 16">
                  <path d="M13.601 2.326A7.854 7.854 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.933 7.933 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.898 7.898 0 0 0 13.6 2.326zM7.994 14.521a6.573 6.573 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.557 6.557 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592zm3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.099-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.729.729 0 0 0-.529.247c-.182.198-.691.677-.691 1.654 0 .977.71 1.916.81 2.049.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232z"/>
                </svg>
                WhatsApp
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Ähnliche Beiträge */}
      {relatedPosts.length > 0 && (
        <section className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold mb-12 text-center text-gray-900">Ähnliche Beiträge</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {relatedPosts.map((relatedPost) => (
                <div key={relatedPost.id} className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col h-full">
                  <div className="relative h-48">
                    <Image
                      src={relatedPost.imageUrl}
                      alt={relatedPost.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                  </div>
                  <div className="p-6 flex-grow flex flex-col">
                    <div className="flex items-center mb-3">
                      <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm">{relatedPost.category}</span>
                      <span className="text-gray-500 text-sm ml-auto">{relatedPost.readTime}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-gray-900">{relatedPost.title}</h3>
                    <p className="text-gray-700 mb-4 flex-grow">{relatedPost.excerpt}</p>
                    <Button asChild className="mt-auto">
                      <Link href={`/blog/${relatedPost.id}`}>Weiterlesen</Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA */}
      <section className="container mx-auto px-4 text-center py-8">
        <div className="bg-accent text-white p-8 md:p-12 rounded-xl max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold mb-6">Bereit, Ihre Fahrzeuglogistik zu revolutionieren?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Entdecken Sie, wie YoungMobility Ihre Fahrzeugüberführungen effizienter, transparenter und kostengünstiger macht.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GradientButton asChild className="bg-white text-accent hover:bg-gray-100">
              <Link href="/register-client">Jetzt registrieren</Link>
            </GradientButton>
            <GradientButton asChild variant="variant" className="border-white text-white hover:bg-white/10">
              <Link href="/contact">Kontakt aufnehmen</Link>
            </GradientButton>
          </div>
        </div>
      </section>
    </div>
  );
}
