import { GradientButton } from "@/components/ui/gradient-button"

export default function ButtonVariantsPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-3xl font-bold mb-8 text-black">🎨 Button Variants - Komplett Überarbeitet</h1>

      <div className="space-y-16">
        {/* Unveränderte Buttons */}
        <div className="space-y-8">
          <h2 className="text-2xl font-bold text-black border-b-2 border-gray-200 pb-2">
            ✅ UNVERÄNDERTE BUTTONS
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4 p-6 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold text-black">Default Button</h3>
              <p className="text-sm text-gray-600">Ursprünglicher schwarzer Hintergrund mit warmen Gradient-Animationen</p>
              <GradientButton variant="default">Default Button</GradientButton>
            </div>

            <div className="space-y-4 p-6 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold text-black">Variant Button</h3>
              <p className="text-sm text-gray-600">Ursprüngliche blau/teal/gelb Farbkombination</p>
              <GradientButton variant="variant">Variant Button</GradientButton>
            </div>
          </div>
        </div>

        {/* Neue Button-Varianten */}
        <div className="space-y-8">
          <h2 className="text-2xl font-bold text-black border-b-2 border-red-200 pb-2">
            🆕 NEUE BUTTON-VARIANTEN
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4 p-6 bg-red-50 rounded-lg border-2 border-red-200">
              <h3 className="text-lg font-semibold text-red-700">🔴 Destructive Button (ÜBERARBEITET)</h3>
              <p className="text-sm text-gray-700">
                <strong>Basis:</strong> Rötlicher Gradient ins Schwarze (wie Ghost Button, nur in rot)<br/>
                <strong>Hover:</strong> Verstärkte rote Gradient-Animation
              </p>
              <GradientButton variant="destructive">Delete Account</GradientButton>
              <p className="text-xs text-red-600 italic">Jetzt mit rötlichem Basis-Gradient!</p>
            </div>

            <div className="space-y-4 p-6 bg-purple-50 rounded-lg border-2 border-purple-200">
              <h3 className="text-lg font-semibold text-purple-700">🎨 Outline Button (KOMPLETT NEU ÜBERARBEITET)</h3>
              <p className="text-sm text-gray-700">
                <strong>Basis:</strong> Transparenter Hintergrund + Default Button Gradient als Border<br/>
                <strong>Hover:</strong> Default Button Hover-Gradient als animierte Border<br/>
                <strong>Zweck:</strong> Echter Outline Button mit Default Button Gradient-System
              </p>
              <GradientButton variant="outline">Cancel</GradientButton>
              <p className="text-xs text-purple-600 italic">Default Button Gradient als Border - echter Outline!</p>
            </div>

            <div className="space-y-4 p-6 bg-gray-100 rounded-lg border-2 border-gray-300">
              <h3 className="text-lg font-semibold text-gray-700">⚫ Ghost Button (PERFEKT)</h3>
              <p className="text-sm text-gray-700">
                <strong>Basis:</strong> Grauer Hintergrund<br/>
                <strong>Hover:</strong> Subtile graue Gradient-Animation
              </p>
              <GradientButton variant="ghost">Skip</GradientButton>
              <p className="text-xs text-green-600 italic">✅ Perfekt wie gewünscht!</p>
            </div>

            <div className="space-y-4 p-6 bg-indigo-50 rounded-lg border-2 border-indigo-200">
              <h3 className="text-lg font-semibold text-indigo-700">🔗 Link Button (KOMPLETT NEU)</h3>
              <p className="text-sm text-gray-700">
                <strong>Basis:</strong> Minimalistischer Link-Style mit Underline<br/>
                <strong>Hover:</strong> Slide-Effekt + Bewegung nach rechts
              </p>
              <GradientButton variant="link">Learn More</GradientButton>
              <p className="text-xs text-indigo-600 italic">Minimalistischer Link mit Slide-Animation!</p>
            </div>
          </div>
        </div>

        {/* Test-Anweisungen */}
        <div className="p-8 bg-blue-50 rounded-lg border-2 border-blue-200">
          <h3 className="text-xl font-bold mb-6 text-blue-800">🧪 TEST-ANWEISUNGEN</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-bold text-blue-700 mb-3">✅ Erwartete Ergebnisse:</h4>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>• <strong>Default & Variant:</strong> Sehen aus wie immer</li>
                <li>• <strong>Destructive:</strong> Rötlicher Gradient → verstärkt rot beim Hover</li>
                <li>• <strong>Outline:</strong> Transparenter Hintergrund + Default Button Gradient als Border</li>
                <li>• <strong>Ghost:</strong> Grau → wird heller grau beim Hover ✅</li>
                <li>• <strong>Link:</strong> Minimalistischer Link mit Slide-Animation</li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold text-blue-700 mb-3">🔍 Was zu testen ist:</h4>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>• <strong>Hover-Effekte:</strong> Über jeden Button hovern</li>
                <li>• <strong>Unterschiede:</strong> Jeder Button sollte anders aussehen</li>
                <li>• <strong>Outline:</strong> Sollte wirklich transparent sein</li>
                <li>• <strong>Animationen:</strong> Sollten smooth und sichtbar sein</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-100 rounded border-l-4 border-yellow-500">
            <p className="text-sm text-yellow-800">
              <strong>⚠️ Wichtig:</strong> Falls alle Buttons gleich aussehen, mache einen Hard Refresh (Cmd+Shift+R / Ctrl+Shift+R)
              oder öffne die Seite im Inkognito-Modus!
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
