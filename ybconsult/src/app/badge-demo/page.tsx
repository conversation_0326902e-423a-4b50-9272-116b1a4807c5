"use client"

import { Badge } from "@/components/ui/badge"
import { Announcement, AnnouncementTag, AnnouncementTitle } from "@/components/ui/announcement"

export default function BadgeDemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      <div className="max-w-6xl mx-auto p-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
            🏷️ Gradient Badge System Demo
          </h1>
          <p className="text-2xl text-gray-600 mb-4">
            Gradient-styled Badges mit Button-System Konsistenz
          </p>
          <p className="text-lg text-gray-500 max-w-4xl mx-auto">
            Entdecken Sie unser modernes Badge-System mit den gleichen Gradient-Effekten wie unsere Buttons -
            für perfekte visuelle Konsistenz und ansprechende Hover-Animationen.
          </p>
        </div>

        {/* Gradient Badges Section */}
        <div className="bg-white p-10 rounded-2xl shadow-xl border border-gray-200 mb-16">
          <div className="flex items-center mb-8">
            <div className="text-4xl mr-4">✨</div>
            <h2 className="text-3xl font-bold text-gray-800">Gradient Badges</h2>
          </div>

          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-700">Alle Varianten:</h3>
              <div className="flex flex-wrap gap-4">
                <Badge variant="default">Default</Badge>
                <Badge variant="secondary">Secondary</Badge>
                <Badge variant="destructive">Destructive</Badge>
                <Badge variant="outline">Outline</Badge>
                <Badge variant="success">Success</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="info">Info</Badge>
                <Badge variant="ghost">Ghost</Badge>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-700">Verschiedene Größen:</h3>
              <div className="flex flex-wrap items-center gap-4">
                <Badge variant="default" className="text-xs px-2 py-0.5">Small</Badge>
                <Badge variant="success" className="text-sm px-3 py-1">Medium</Badge>
                <Badge variant="warning" className="text-base px-4 py-1.5">Large</Badge>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-700">Hover-Effekte (bewegen Sie die Maus über die Badges):</h3>
              <div className="flex flex-wrap gap-4">
                <Badge variant="default" className="cursor-pointer">Hover me!</Badge>
                <Badge variant="success" className="cursor-pointer">Success Hover</Badge>
                <Badge variant="warning" className="cursor-pointer">Warning Hover</Badge>
                <Badge variant="destructive" className="cursor-pointer">Error Hover</Badge>
                <Badge variant="info" className="cursor-pointer">Info Hover</Badge>
                <Badge variant="ghost" className="cursor-pointer">Ghost Hover</Badge>
                <Badge variant="outline" className="cursor-pointer">Outline Hover</Badge>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-purple-50 to-blue-50 p-6 rounded-xl">
                <h4 className="font-bold text-purple-800 mb-4">✨ Gradient Features:</h4>
                <ul className="text-sm text-purple-600 space-y-2">
                  <li>• Identische Gradient-Effekte wie Buttons</li>
                  <li>• Animierte Hover-Übergänge</li>
                  <li>• 8 verschiedene Varianten</li>
                  <li>• Farbfamilien-treue Hover-Effekte</li>
                  <li>• Konsistente visuelle Sprache</li>
                </ul>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl">
                <h4 className="font-bold text-green-800 mb-4">🎯 Perfekt für:</h4>
                <ul className="text-sm text-green-600 space-y-2">
                  <li>• Status-Anzeigen mit visueller Wirkung</li>
                  <li>• Moderne Dashboard-Elemente</li>
                  <li>• Interaktive Tag-Systeme</li>
                  <li>• Konsistenz mit Button-Design</li>
                  <li>• Hochwertige UI-Erfahrungen</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Announcement System Section */}
        <div className="bg-white p-10 rounded-2xl shadow-xl border border-blue-200 mb-16">
          <div className="flex items-center mb-8">
            <div className="text-4xl mr-4">📢</div>
            <h2 className="text-3xl font-bold text-blue-800">Announcement System</h2>
          </div>

          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold mb-4 text-gray-700">Verschiedene Konfigurationen:</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-600 mb-2">Standard Announcement:</h4>
                  <Announcement>
                    <AnnouncementTag>NEW</AnnouncementTag>
                    <AnnouncementTitle>Neue Feature verfügbar</AnnouncementTitle>
                  </Announcement>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-600 mb-2">Themed Announcement:</h4>
                  <Announcement themed>
                    <AnnouncementTag>BETA</AnnouncementTag>
                    <AnnouncementTitle>Beta-Version mit verbessertem Design</AnnouncementTitle>
                  </Announcement>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-600 mb-2">Nur Titel:</h4>
                  <Announcement>
                    <AnnouncementTitle>Wichtige Systemankündigung ohne Tag</AnnouncementTitle>
                  </Announcement>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-600 mb-2">Verschiedene Badge-Varianten:</h4>
                  <div className="space-y-2">
                    <Announcement variant="default">
                      <AnnouncementTag>UPDATE</AnnouncementTag>
                      <AnnouncementTitle>Default Variant</AnnouncementTitle>
                    </Announcement>
                    <Announcement variant="secondary">
                      <AnnouncementTag>INFO</AnnouncementTag>
                      <AnnouncementTitle>Secondary Variant</AnnouncementTitle>
                    </Announcement>
                    <Announcement variant="destructive">
                      <AnnouncementTag>URGENT</AnnouncementTag>
                      <AnnouncementTitle>Destructive Variant</AnnouncementTitle>
                    </Announcement>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-green-50 p-6 rounded-xl">
                <h4 className="font-bold text-green-800 mb-4">✅ Vorteile:</h4>
                <ul className="text-sm text-green-600 space-y-2">
                  <li>• Zusammengesetzte Struktur</li>
                  <li>• Context-basierte Theming</li>
                  <li>• Flexible Tag + Titel Kombination</li>
                  <li>• Perfekt für Notifications</li>
                  <li>• TypeScript-Support</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-6 rounded-xl">
                <h4 className="font-bold text-blue-800 mb-4">🎯 Beste Verwendung:</h4>
                <ul className="text-sm text-blue-600 space-y-2">
                  <li>• Announcement-Banner</li>
                  <li>• Notification-Systeme</li>
                  <li>• Feature-Highlights</li>
                  <li>• Update-Meldungen</li>
                  <li>• Strukturierte Informationen</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Practical Examples */}
        <div className="bg-white p-10 rounded-2xl shadow-xl mb-16">
          <div className="flex items-center mb-8">
            <div className="text-4xl mr-4">💼</div>
            <h2 className="text-3xl font-bold text-gray-800">Praktische Anwendungsbeispiele</h2>
          </div>

          <div className="space-y-12">
            {/* Dashboard Example */}
            <div>
              <h3 className="text-2xl font-bold text-gray-700 mb-6">🖥️ Dashboard-Szenario</h3>
              <div className="bg-gray-50 p-6 rounded-xl">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">Server Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Web Server:</span>
                        <Badge variant="success">Online</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Database:</span>
                        <Badge variant="warning">Slow</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">API:</span>
                        <Badge variant="destructive">Error</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Cache:</span>
                        <Badge variant="info">Optimized</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">User Roles</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-sm">Admin:</span>
                        <Badge variant="default">Full Access</Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">Editor:</span>
                        <Badge variant="info">Limited</Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">Viewer:</span>
                        <Badge variant="ghost">Read Only</Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">Guest:</span>
                        <Badge variant="outline">Restricted</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">Notifications</h4>
                    <div className="space-y-3">
                      <Announcement variant="default">
                        <AnnouncementTag>NEW</AnnouncementTag>
                        <AnnouncementTitle>System Update</AnnouncementTitle>
                      </Announcement>
                      <Announcement variant="destructive">
                        <AnnouncementTag>URGENT</AnnouncementTag>
                        <AnnouncementTitle>Security Alert</AnnouncementTitle>
                      </Announcement>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* E-commerce Example */}
            <div>
              <h3 className="text-2xl font-bold text-gray-700 mb-6">🛒 E-Commerce-Szenario</h3>
              <div className="bg-gray-50 p-6 rounded-xl">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">Produktstatus</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Laptop Pro:</span>
                        <Badge variant="success">Verfügbar</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Smartphone X:</span>
                        <Badge variant="warning">Wenige</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Tablet Mini:</span>
                        <Badge variant="destructive">Ausverkauft</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Monitor 4K:</span>
                        <Badge variant="info">Vorbestellung</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">Kategorien</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="info">Elektronik</Badge>
                      <Badge variant="secondary">Computer</Badge>
                      <Badge variant="ghost">Zubehör</Badge>
                      <Badge variant="warning">Sale</Badge>
                      <Badge variant="outline">Premium</Badge>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow">
                    <h4 className="font-bold text-gray-700 mb-3">Promotions</h4>
                    <div className="space-y-2">
                      <Announcement themed>
                        <AnnouncementTag>SALE</AnnouncementTag>
                        <AnnouncementTitle>50% Rabatt auf Laptops</AnnouncementTitle>
                      </Announcement>
                      <Announcement>
                        <AnnouncementTag>NEW</AnnouncementTag>
                        <AnnouncementTitle>Neue Smartphone-Serie</AnnouncementTitle>
                      </Announcement>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Implementation */}
        <div className="bg-gradient-to-r from-gray-100 to-slate-100 p-10 rounded-2xl shadow-xl">
          <div className="flex items-center mb-8">
            <div className="text-4xl mr-4">⚙️</div>
            <h2 className="text-3xl font-bold text-gray-800">Technische Implementierung</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4">Import-Statements</h3>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
                <div className="space-y-2">
                  <div>// Gradient Badges</div>
                  <div>import &#123; Badge &#125; from "@/components/ui/badge"</div>
                  <div></div>
                  <div>// Announcements</div>
                  <div>import &#123; Announcement, AnnouncementTag, AnnouncementTitle &#125;</div>
                  <div>&nbsp;&nbsp;from "@/components/ui/announcement"</div>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-lg">
              <h3 className="text-xl font-bold text-gray-800 mb-4">Verwendungsbeispiele</h3>
              <div className="bg-gray-900 text-blue-400 p-4 rounded-lg font-mono text-sm">
                <div className="space-y-2">
                  <div>// Gradient Badges - All Variants</div>
                  <div>&lt;Badge variant="default"&gt;Default&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="success"&gt;Success&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="warning"&gt;Warning&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="destructive"&gt;Error&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="info"&gt;Info&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="ghost"&gt;Ghost&lt;/Badge&gt;</div>
                  <div>&lt;Badge variant="outline"&gt;Outline&lt;/Badge&gt;</div>
                  <div></div>
                  <div>// Announcement</div>
                  <div>&lt;Announcement&gt;</div>
                  <div>&nbsp;&nbsp;&lt;AnnouncementTag&gt;NEW&lt;/AnnouncementTag&gt;</div>
                  <div>&nbsp;&nbsp;&lt;AnnouncementTitle&gt;Feature&lt;/AnnouncementTitle&gt;</div>
                  <div>&lt;/Announcement&gt;</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-xl">
            <h3 className="text-xl font-bold text-purple-800 mb-4">✨ Zusammenfassung</h3>
            <p className="text-purple-700">
              Unser Gradient Badge-System bietet perfekte visuelle Konsistenz mit unserem Button-System!
              Mit identischen Gradient-Effekten, animierten Hover-Übergängen und 8 verschiedenen Varianten
              schaffen wir eine einheitliche, moderne Design-Sprache für alle UI-Komponenten.
            </p>
            <div className="mt-4 flex flex-wrap gap-2">
              <Badge variant="success" className="cursor-pointer">Konsistent</Badge>
              <Badge variant="info" className="cursor-pointer">Modern</Badge>
              <Badge variant="warning" className="cursor-pointer">Animiert</Badge>
              <Badge variant="default" className="cursor-pointer">Professionell</Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
