export default function SimpleTestPage() {
  return (
    <div className="p-8">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">Einfache Testseite</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">Karte 1</h2>
          <p className="text-gray-600 mb-4">Dies ist ein einfacher Test für Tailwind CSS Styles.</p>
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            Button 1
          </button>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">Karte 2</h2>
          <p className="text-gray-600 mb-4">Ein weiterer Test für Tailwind CSS Styles.</p>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
            Button 2
          </button>
        </div>
      </div>
      
      <div className="bg-yellow-100 p-6 rounded-lg border border-yellow-300 mb-8">
        <h2 className="text-xl font-semibold mb-2 text-yellow-800">Hinweis</h2>
        <p className="text-yellow-700">
          Diese Seite verwendet nur grundlegende Tailwind CSS Klassen ohne benutzerdefinierte Komponenten.
        </p>
      </div>
      
      <div className="flex space-x-4">
        <a href="/" className="text-blue-500 hover:text-blue-700 underline">
          Zurück zur Startseite
        </a>
        <a href="/test" className="text-blue-500 hover:text-blue-700 underline">
          Zur Komponenten-Testseite
        </a>
      </div>
    </div>
  );
}
