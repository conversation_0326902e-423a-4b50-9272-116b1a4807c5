import { Metadata } from "next";
import { HomePageContent } from "@/components/pages/HomePageContent";

/**
 * Metadaten für die Hauptlandingpage
 */
export const metadata: Metadata = {
  title: "YoungMobility - Innovative Fahrzeugüberführungen",
  description: "YoungMobility revolutioniert die Fahrzeuglogistik mit einer modernen, benutzerfreundlichen Plattform für Geschäftskunden und Fahrer.",
  keywords: "Fahrzeugüberführung, Fahrzeuglogistik, Autotransport, Fahrzeugtransfer, YoungMobility",
  openGraph: {
    title: "YoungMobility - Innovative Fahrzeugüberführungen",
    description: "Revolutionäre Fahrzeuglogistik für Geschäftskunden und Fahrer",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "YoungMobility - Innovative Fahrzeugüberführungen",
      },
    ],
    type: "website",
  },
};

/**
 * Landing Page der YoungMobility-Plattform.
 * Dient als Einstiegspunkt und stellt die Marke vor.
 * @returns Die gerenderte Landing Page.
 */
export default function HomePage() {
  return <HomePageContent />;
}