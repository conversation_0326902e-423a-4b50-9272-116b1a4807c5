import { GradientButton } from "@/components/ui/gradient-button"

export default function ButtonTestPage() {
  return (
    <div className="min-h-screen bg-white p-8">
      <h1 className="text-3xl font-bold mb-8 text-black">Button Color Test</h1>

      <div className="space-y-8">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-black">Default <PERSON></h2>
          <button className="gradient-button inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Default <PERSON> (Direct CSS)
          </button>
          <GradientButton variant="default">Default <PERSON> (Component)</GradientButton>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-black">Variant But<PERSON></h2>
          <button className="gradient-button gradient-button-variant inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Variant Button (Direct CSS)
          </button>
          <GradientButton variant="variant">Variant Button (Component)</GradientButton>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-red-600">Destructive Button (Should be RED)</h2>
          <button className="gradient-button gradient-button-destructive inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Destructive Button (Direct CSS)
          </button>
          <GradientButton variant="destructive">Destructive Button (Component)</GradientButton>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-orange-600">Outline Button (Should be ORANGE)</h2>
          <button className="gradient-button gradient-button-outline inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Outline Button (Direct CSS)
          </button>
          <GradientButton variant="outline">Outline Button (Component)</GradientButton>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-600">Ghost Button (Should be GRAY)</h2>
          <button className="gradient-button gradient-button-ghost inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Ghost Button (Direct CSS)
          </button>
          <GradientButton variant="ghost">Ghost Button (Component)</GradientButton>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-purple-600">Link Button (Should be PURPLE)</h2>
          <button className="gradient-button gradient-button-link inline-flex items-center justify-center rounded-[11px] min-w-[132px] px-9 py-4 text-base leading-[19px] font-[500] text-white font-sans font-bold">
            Link Button (Direct CSS)
          </button>
          <GradientButton variant="link">Link Button (Component)</GradientButton>
        </div>
      </div>

      <div className="mt-12 p-4 bg-gray-100 rounded">
        <h3 className="text-lg font-semibold mb-2 text-black">Expected Colors:</h3>
        <ul className="text-sm text-black space-y-1">
          <li>• Default: Dark gradient (original)</li>
          <li>• Variant: Blue/teal/yellow gradient (original)</li>
          <li>• Destructive: <span className="text-red-600 font-bold">Bright RED gradient</span></li>
          <li>• Outline: <span className="text-orange-600 font-bold">Bright ORANGE gradient</span></li>
          <li>• Ghost: <span className="text-gray-600 font-bold">Gray gradient</span></li>
          <li>• Link: <span className="text-purple-600 font-bold">Bright PURPLE gradient</span></li>
        </ul>

        <div className="mt-4 p-2 bg-yellow-100 rounded">
          <p className="text-sm text-black">
            <strong>Debug:</strong> Die "Direct CSS" Buttons sollten die Farben zeigen.
            Wenn sie alle gleich aussehen, liegt das Problem in der CSS-Definition.
          </p>
        </div>
      </div>
    </div>
  )
}
