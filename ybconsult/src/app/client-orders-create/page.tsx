"use client";

import { useState } from 'react';

export default function CreateOrderPage() {
  const [step, setStep] = useState(1);
  
  const nextStep = () => {
    setStep(step + 1);
  };
  
  const prevStep = () => {
    setStep(step - 1);
  };
  
  return (
    <div style={{ maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>Neuen Auftrag erstellen</h1>
      
      {/* Fortschrittsanzeige */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', position: 'relative' }}>
          <div style={{ 
            width: '30px', 
            height: '30px', 
            borderRadius: '50%', 
            backgroundColor: step >= 1 ? '#3b82f6' : '#e2e8f0',
            color: step >= 1 ? 'white' : '#64748b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            zIndex: 1
          }}>
            1
          </div>
          <div style={{ 
            width: '30px', 
            height: '30px', 
            borderRadius: '50%', 
            backgroundColor: step >= 2 ? '#3b82f6' : '#e2e8f0',
            color: step >= 2 ? 'white' : '#64748b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            zIndex: 1
          }}>
            2
          </div>
          <div style={{ 
            width: '30px', 
            height: '30px', 
            borderRadius: '50%', 
            backgroundColor: step >= 3 ? '#3b82f6' : '#e2e8f0',
            color: step >= 3 ? 'white' : '#64748b',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            zIndex: 1
          }}>
            3
          </div>
          <div style={{ 
            position: 'absolute',
            height: '2px',
            backgroundColor: '#e2e8f0',
            top: '14px',
            left: '30px',
            right: '30px',
            zIndex: 0
          }}>
            <div style={{ 
              height: '100%', 
              backgroundColor: '#3b82f6', 
              width: step === 1 ? '0%' : step === 2 ? '50%' : '100%' 
            }}></div>
          </div>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
          <div style={{ width: '30px', textAlign: 'center', fontSize: '12px', color: '#64748b' }}>Start</div>
          <div style={{ width: '30px', textAlign: 'center', fontSize: '12px', color: '#64748b' }}>Details</div>
          <div style={{ width: '30px', textAlign: 'center', fontSize: '12px', color: '#64748b' }}>Bestätigung</div>
        </div>
      </div>
      
      {/* Formular */}
      <div style={{ 
        border: '1px solid #e2e8f0', 
        borderRadius: '8px', 
        backgroundColor: 'white',
        padding: '20px',
        marginBottom: '20px'
      }}>
        {step === 1 && (
          <div>
            <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>Schritt 1: Grundinformationen</h2>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Abholort</label>
              <input 
                type="text" 
                placeholder="Straße, Hausnummer, PLZ, Ort"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Zielort</label>
              <input 
                type="text" 
                placeholder="Straße, Hausnummer, PLZ, Ort"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Gewünschtes Abholdatum</label>
              <input 
                type="date"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
          </div>
        )}
        
        {step === 2 && (
          <div>
            <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>Schritt 2: Fahrzeugdetails</h2>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Fahrzeugtyp</label>
              <select
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              >
                <option value="">Bitte wählen</option>
                <option value="pkw">PKW</option>
                <option value="transporter">Transporter</option>
                <option value="lkw">LKW</option>
              </select>
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Marke</label>
              <input 
                type="text" 
                placeholder="z.B. Volkswagen, BMW, Mercedes"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Modell</label>
              <input 
                type="text" 
                placeholder="z.B. Golf, 3er, C-Klasse"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>Kennzeichen</label>
              <input 
                type="text" 
                placeholder="z.B. M-AB 123"
                style={{ 
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #e2e8f0',
                  borderRadius: '6px'
                }}
              />
            </div>
          </div>
        )}
        
        {step === 3 && (
          <div>
            <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>Schritt 3: Bestätigung</h2>
            
            <div style={{ 
              backgroundColor: '#f8fafc', 
              padding: '15px', 
              borderRadius: '6px',
              marginBottom: '20px'
            }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>Zusammenfassung</h3>
              
              <div style={{ marginBottom: '10px' }}>
                <div style={{ fontWeight: '500' }}>Abholort:</div>
                <div style={{ color: '#64748b' }}>Beispielstraße 1, 80331 München</div>
              </div>
              
              <div style={{ marginBottom: '10px' }}>
                <div style={{ fontWeight: '500' }}>Zielort:</div>
                <div style={{ color: '#64748b' }}>Musterweg 42, 10115 Berlin</div>
              </div>
              
              <div style={{ marginBottom: '10px' }}>
                <div style={{ fontWeight: '500' }}>Abholdatum:</div>
                <div style={{ color: '#64748b' }}>01.01.2023</div>
              </div>
              
              <div style={{ marginBottom: '10px' }}>
                <div style={{ fontWeight: '500' }}>Fahrzeug:</div>
                <div style={{ color: '#64748b' }}>PKW - Volkswagen Golf (M-AB 123)</div>
              </div>
            </div>
            
            <div style={{ marginBottom: '15px' }}>
              <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                <input 
                  type="checkbox"
                  style={{ marginRight: '8px' }}
                />
                <span>Ich bestätige die Richtigkeit der Angaben und akzeptiere die AGB</span>
              </label>
            </div>
          </div>
        )}
      </div>
      
      {/* Buttons */}
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        {step > 1 ? (
          <button 
            onClick={prevStep}
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '6px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            Zurück
          </button>
        ) : (
          <a 
            href="/client-orders"
            style={{ 
              padding: '8px 16px',
              backgroundColor: 'white',
              color: '#64748b',
              border: '1px solid #e2e8f0',
              borderRadius: '6px',
              textDecoration: 'none',
              fontWeight: '500'
            }}
          >
            Abbrechen
          </a>
        )}
        
        {step < 3 ? (
          <button 
            onClick={nextStep}
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              border: 'none',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            Weiter
          </button>
        ) : (
          <button 
            style={{ 
              padding: '8px 16px',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '6px',
              border: 'none',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            Auftrag erstellen
          </button>
        )}
      </div>
    </div>
  );
}
