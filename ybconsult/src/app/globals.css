@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @property --pos-x {
    syntax: '<percentage>';
    initial-value: 11.14%;
    inherits: false;
  }

  @property --pos-y {
    syntax: '<percentage>';
    initial-value: 140%;
    inherits: false;
  }

  @property --spread-x {
    syntax: '<percentage>';
    initial-value: 150%;
    inherits: false;
  }

  @property --spread-y {
    syntax: '<percentage>';
    initial-value: 180.06%;
    inherits: false;
  }

  @property --color-1 {
    syntax: '<color>';
    initial-value: #000;
    inherits: false;
  }

  @property --color-2 {
    syntax: '<color>';
    initial-value: #08012c;
    inherits: false;
  }

  @property --color-3 {
    syntax: '<color>';
    initial-value: #4e1e40;
    inherits: false;
  }

  @property --color-4 {
    syntax: '<color>';
    initial-value: #70464e;
    inherits: false;
  }

  @property --color-5 {
    syntax: '<color>';
    initial-value: #88394c;
    inherits: false;
  }

  @property --border-angle {
    syntax: '<angle>';
    initial-value: 20deg;
    inherits: true;
  }

  @property --border-color-1 {
    syntax: '<color>';
    initial-value: hsla(340, 75%, 60%, 0.2);
    inherits: true;
  }

  @property --border-color-2 {
    syntax: '<color>';
    initial-value: hsla(340, 75%, 40%, 0.75);
    inherits: true;
  }

  @property --stop-1 {
    syntax: '<percentage>';
    initial-value: 37.35%;
    inherits: false;
  }

  @property --stop-2 {
    syntax: '<percentage>';
    initial-value: 61.36%;
    inherits: false;
  }

  @property --stop-3 {
    syntax: '<percentage>';
    initial-value: 78.42%;
    inherits: false;
  }

  @property --stop-4 {
    syntax: '<percentage>';
    initial-value: 89.52%;
    inherits: false;
  }

  @property --stop-5 {
    syntax: '<percentage>';
    initial-value: 100%;
    inherits: false;
  }
}

@layer components {
  /* No underline utility class */
  .no-underline {
    text-decoration: none !important;
  }

  .no-underline:hover, .no-underline:focus, .no-underline:active, .no-underline:visited {
    text-decoration: none !important;
  }

  .gradient-button {
    @apply relative appearance-none cursor-pointer;
    background: radial-gradient(
      var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
      var(--color-1) var(--stop-1),
      var(--color-2) var(--stop-2),
      var(--color-3) var(--stop-3),
      var(--color-4) var(--stop-4),
      var(--color-5) var(--stop-5)
    );
    text-decoration: none !important;
    transition:
      --pos-x 0.5s,
      --pos-y 0.5s,
      --spread-x 0.5s,
      --spread-y 0.5s,
      --color-1 0.5s,
      --color-2 0.5s,
      --color-3 0.5s,
      --color-4 0.5s,
      --color-5 0.5s,
      --border-angle 0.5s,
      --border-color-1 0.5s,
      --border-color-2 0.5s,
      --stop-1 0.5s,
      --stop-2 0.5s,
      --stop-3 0.5s,
      --stop-4 0.5s,
      --stop-5 0.5s;
  }

  .gradient-button::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(
      var(--border-angle),
      var(--border-color-1),
      var(--border-color-2)
    );
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    pointer-events: none;
  }

  .gradient-button:hover {
    --pos-x: 0%;
    --pos-y: 91.51%;
    --spread-x: 120.24%;
    --spread-y: 103.18%;
    --color-1: #c96287;
    --color-2: #c66c64;
    --color-3: #cc7d23;
    --color-4: #37140a;
    --color-5: #000;
    --border-angle: 190deg;
    --border-color-1: hsla(340, 78%, 90%, 0.1);
    --border-color-2: hsla(340, 75%, 90%, 0.6);
    --stop-1: 0%;
    --stop-2: 8.8%;
    --stop-3: 21.44%;
    --stop-4: 71.34%;
    --stop-5: 85.76%;
    text-decoration: none !important;
  }

  .gradient-button-variant {
    --color-1: #000022;
    --color-2: #1f3f6d;
    --color-3: #469396;
    --color-4: #f1ffa5;
    --border-angle: 200deg;
    --border-color-1: hsla(320, 75%, 90%, 0.6);
    --border-color-2: hsla(320, 50%, 90%, 0.15);
  }

  .gradient-button-variant:hover {
    --pos-x: 0%;
    --pos-y: 95.51%;
    --spread-x: 110.24%;
    --spread-y: 110.2%;
    --color-1: #000020;
    --color-2: #f1ffa5;
    --color-3: #469396;
    --color-4: #1f3f6d;
    --color-5: #000;
    --stop-1: 0%;
    --stop-2: 10%;
    --stop-3: 35.44%;
    --stop-4: 71.34%;
    --stop-5: 90.76%;
    --border-angle: 210deg;
    --border-color-1: hsla(320, 75%, 90%, 0.2);
    --border-color-2: hsla(320, 50%, 90%, 0.75);
    text-decoration: none !important;
  }

  /* Destructive Gradient Button - Rötlicher Gradient ins Schwarze (wie Ghost Button, nur in rot) */
  .gradient-button-destructive {
    --pos-x: 11.14%;
    --pos-y: 140%;
    --spread-x: 150%;
    --spread-y: 180.06%;
    --color-1: #7f1d1d;
    --color-2: #991b1b;
    --color-3: #b91c1c;
    --color-4: #dc2626;
    --color-5: #ef4444;
    --border-angle: 20deg;
    --border-color-1: rgba(220, 38, 38, 0.2);
    --border-color-2: rgba(220, 38, 38, 0.4);
    --stop-1: 37.35%;
    --stop-2: 61.36%;
    --stop-3: 78.42%;
    --stop-4: 89.52%;
    --stop-5: 100%;
    color: #ffffff;
  }

  .gradient-button-destructive:hover {
    /* Lighter red gradient animation on hover for better text readability */
    --pos-x: 0%;
    --pos-y: 91.51%;
    --spread-x: 120.24%;
    --spread-y: 103.18%;
    --color-1: #fca5a5; /* Light red/pink */
    --color-2: #f87171; /* Coral red */
    --color-3: #fb7185; /* Rose pink */
    --color-4: #fda4af; /* Light pink */
    --color-5: #fecaca; /* Very light pink */
    --border-angle: 190deg;
    --border-color-1: hsla(0, 75%, 80%, 0.3);
    --border-color-2: hsla(0, 75%, 70%, 0.8);
    --stop-1: 0%;
    --stop-2: 8.8%;
    --stop-3: 21.44%;
    --stop-4: 71.34%;
    --stop-5: 85.76%;
    color: #000000; /* Black text for maximum contrast */
    text-decoration: none !important;
  }

  /* Outline Gradient Button - Reliable pseudo-element approach */
  .gradient-button.gradient-button-outline {
    /* Base properties */
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    color: #000000 !important; /* Black text to match base border */
    position: relative !important;
    border-radius: 11px !important;
    box-sizing: border-box !important;
    border: 2px solid #000 !important; /* Fallback solid border */

    /* Adjust padding to compensate for border and maintain same total size */
    padding: calc(1rem - 2px) calc(2.25rem - 2px) !important; /* px-9 py-4 minus border */

    /* Default Button gradient variables */
    --pos-x: 11.14%;
    --pos-y: 140%;
    --spread-x: 150%;
    --spread-y: 180.06%;
    --color-1: #000;
    --color-2: #08012c;
    --color-3: #4e1e40;
    --color-4: #70464e;
    --color-5: #88394c;
    --stop-1: 37.35%;
    --stop-2: 61.36%;
    --stop-3: 78.42%;
    --stop-4: 89.52%;
    --stop-5: 100%;

    transition: all 0.5s ease !important;
  }

  /* Create gradient border using ::after pseudo-element */
  .gradient-button.gradient-button-outline::after {
    content: '' !important;
    position: absolute !important;
    inset: -2px !important;
    background: radial-gradient(
      var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
      var(--color-1) var(--stop-1),
      var(--color-2) var(--stop-2),
      var(--color-3) var(--stop-3),
      var(--color-4) var(--stop-4),
      var(--color-5) var(--stop-5)
    ) !important;
    border-radius: 13px !important;
    z-index: -1 !important;
    pointer-events: none !important;
    display: block !important;
  }

  /* Disable ::before to avoid conflicts */
  .gradient-button.gradient-button-outline::before {
    display: none !important;
  }

  /* Hover state with gradient animation */
  .gradient-button.gradient-button-outline:hover {
    /* Default Button hover gradient variables */
    --pos-x: 0%;
    --pos-y: 91.51%;
    --spread-x: 120.24%;
    --spread-y: 103.18%;
    --color-1: #c96287;
    --color-2: #c66c64;
    --color-3: #cc7d23;
    --color-4: #37140a;
    --color-5: #000;
    --stop-1: 0%;
    --stop-2: 8.8%;
    --stop-3: 21.44%;
    --stop-4: 71.34%;
    --stop-5: 85.76%;
    border-color: #c96287 !important; /* Fallback hover color */

    /* Gradient text effect matching the border gradient */
    background: radial-gradient(
      var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
      var(--color-1) var(--stop-1),
      var(--color-2) var(--stop-2),
      var(--color-3) var(--stop-3),
      var(--color-4) var(--stop-4),
      var(--color-5) var(--stop-5)
    ) !important;
    background-clip: text !important;
    -webkit-background-clip: text !important;
    color: transparent !important;
  }



  /* Ghost Gradient Button - Gray background with subtle gray gradient animation */
  .gradient-button-ghost {
    --pos-x: 11.14%;
    --pos-y: 140%;
    --spread-x: 150%;
    --spread-y: 180.06%;
    --color-1: #374151;
    --color-2: #4b5563;
    --color-3: #6b7280;
    --color-4: #9ca3af;
    --color-5: #d1d5db;
    --border-angle: 20deg;
    --border-color-1: rgba(156, 163, 175, 0.2);
    --border-color-2: rgba(156, 163, 175, 0.4);
    --stop-1: 37.35%;
    --stop-2: 61.36%;
    --stop-3: 78.42%;
    --stop-4: 89.52%;
    --stop-5: 100%;
    color: #ffffff;
  }

  .gradient-button-ghost:hover {
    --pos-x: 0%;
    --pos-y: 91.51%;
    --spread-x: 120.24%;
    --spread-y: 103.18%;
    --color-1: #6b7280;
    --color-2: #9ca3af;
    --color-3: #d1d5db;
    --color-4: #e5e7eb;
    --color-5: #f3f4f6;
    --border-angle: 190deg;
    --border-color-1: rgba(156, 163, 175, 0.3);
    --border-color-2: rgba(156, 163, 175, 0.6);
    --stop-1: 0%;
    --stop-2: 8.8%;
    --stop-3: 21.44%;
    --stop-4: 71.34%;
    --stop-5: 85.76%;
    color: #374151;
    text-decoration: none !important;
  }

  /* Link Gradient Button - Komplett überarbeitet als minimalistischer Link-Style */
  .gradient-button-link {
    background: transparent !important;
    color: #4338ca;
    border: none;
    text-decoration: none;
    padding: 12px 24px;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .gradient-button-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
  }

  .gradient-button-link:hover {
    color: #4338ca;
    text-decoration: none !important;
    transform: translateX(4px);
  }

  .gradient-button-link:hover::before {
    left: 100%;
  }

}

/* Custom Utilities */
@layer utilities {
  /* Versteckt die Scrollbar für verschiedene Browser */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
  }

  /* Versteckt die Scrollbar für Webkit-Browser (Chrome, Safari) */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Quechua-inspirierte Animationen */
  .quechua-hover-lift {
    transition: transform var(--quechua-transition-medium),
                box-shadow var(--quechua-transition-medium);
  }

  .quechua-hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px var(--quechua-shadow-medium);
  }

  .quechua-focus-ring {
    transition: box-shadow var(--quechua-transition-fast);
  }

  .quechua-focus-ring:focus-visible {
    box-shadow: 0 0 0 2px var(--quechua-medium-green);
    outline: none;
  }
}

@layer base {
  :root {
    /* Base colors - Using Modern color palette with a different background */
    --background: 220 17% 95%; /* Light gray background with slight blue tint */
    --foreground: 215 25% 27%; /* Dark gray for text */

    --card: 0 0% 100%; /* White */
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 217 91% 60%; /* Vibrant blue - primary color */
    --primary-foreground: 0 0% 100%;

    --secondary: 160 84% 39%; /* Secondary green */
    --secondary-foreground: 0 0% 100%;

    --muted: 214 32% 91%; /* Light gray for muted elements */
    --muted-foreground: 215 16% 47%;

    --accent: 262 83% 58%; /* Purple accent */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%; /* Red for destructive actions */
    --destructive-foreground: 0 0% 100%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;

    --radius: 0.5rem; /* 8px border radius */

    /* Modern theme specific variables */
    --modern-border-glow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    --modern-button-transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1),
                               box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                               border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    /* Dark mode color scheme */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 14%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 14%;
    --popover-foreground: 210 40% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 156 72% 67%;
    --secondary-foreground: 222 47% 11%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 261 73% 63%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 25%;
    --input: 217 33% 25%;
    --ring: 224 76% 48%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: var(--font-montserrat), var(--ym-font-sans);
    transition: background-color var(--ym-transition-normal),
                color var(--ym-transition-normal);
  }

  /* Remove all underlines from links and interactive elements */
  a, button, [role="button"], .gradient-button, .gradient-button-link {
    text-decoration: none !important;
  }

  a:hover, button:hover, [role="button"]:hover, .gradient-button:hover, .gradient-button-link:hover {
    text-decoration: none !important;
  }

  /* Ensure all navigation links always use cream color */
  nav a, .navigation-menu a, [data-slot="navigation-menu"] a,
  header nav a, .nav-link-custom,
  [data-slot="navigation-menu-link"],
  .navigation-menu-link {
    color: #F8E7D1 !important;
  }

  nav a:hover, .navigation-menu a:hover, [data-slot="navigation-menu"] a:hover,
  header nav a:hover, .nav-link-custom:hover,
  [data-slot="navigation-menu-link"]:hover,
  .navigation-menu-link:hover {
    color: #F8E7D1 !important;
  }

  /* Override any primary color usage in navigation */
  nav a.text-primary, nav a:hover.text-primary {
    color: #F8E7D1 !important;
  }

  /* Force remove underlines from all gradient buttons and their variants */
  .gradient-button, .gradient-button-link, .gradient-button-variant,
  .gradient-button-destructive, .gradient-button-outline, .gradient-button-ghost {
    text-decoration: none !important;
  }

  .gradient-button:hover, .gradient-button-link:hover, .gradient-button-variant:hover,
  .gradient-button-destructive:hover, .gradient-button-outline:hover, .gradient-button-ghost:hover {
    text-decoration: none !important;
  }

  /* Universal underline removal - catch all cases */
  * {
    text-decoration: none !important;
  }

  *:hover, *:focus, *:active, *:visited {
    text-decoration: none !important;
  }

  /* Specifically target any remaining underline sources */
  [class*="gradient-button"], [class*="button"], [class*="link"] {
    text-decoration: none !important;
  }

  [class*="gradient-button"]:hover, [class*="button"]:hover, [class*="link"]:hover {
    text-decoration: none !important;
  }

  /* Maximum priority underline removal for gradient buttons */
  .gradient-button, .gradient-button * {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
  }

  .gradient-button:hover, .gradient-button:hover * {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
  }

  /* Maximum priority underline removal for footer links */
  footer a, footer a * {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
  }

  footer a:hover, footer a:hover * {
    text-decoration: none !important;
    text-decoration-line: none !important;
    text-decoration-style: none !important;
    text-decoration-color: transparent !important;
  }

  /* Header button text color override */
  header .gradient-button {
    color: #FFFFFF !important;
  }

  header .gradient-button:hover {
    color: #FFFFFF !important;
  }

  header .gradient-button a {
    color: #FFFFFF !important;
  }

  header .gradient-button:hover a {
    color: #FFFFFF !important;
  }
  main {
    flex-grow: 1;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-playfair), var(--ym-font-serif);
    font-weight: var(--ym-font-bold);
    color: hsl(var(--foreground));
    margin-bottom: 0.5em;
    line-height: var(--ym-leading-tight);
  }
  h1 {
    font-size: var(--ym-text-3xl);
    margin-bottom: 1rem;
  }
  @media (min-width: 768px) {
    h1 {
      font-size: var(--ym-text-4xl);
    }
  }
  @media (min-width: 1024px) {
    h1 {
      font-size: var(--ym-text-5xl);
    }
  }
  h2 {
    font-size: var(--ym-text-2xl);
    margin-bottom: 0.75rem;
  }
  @media (min-width: 768px) {
    h2 {
      font-size: var(--ym-text-3xl);
    }
  }
  h3 {
    font-size: var(--ym-text-xl);
    margin-bottom: 0.5rem;
  }
  @media (min-width: 768px) {
    h3 {
      font-size: var(--ym-text-2xl);
    }
  }
  p {
    font-size: var(--ym-text-base);
    line-height: var(--ym-leading-normal);
    margin-bottom: 1rem;
  }
  @media (min-width: 768px) {
    p {
      font-size: var(--ym-text-lg);
    }
  }

  a {
    color: hsl(var(--primary));
    text-decoration: none;
    transition: color var(--ym-transition-fast);
  }

  a:hover {
    color: hsl(var(--primary-foreground));
    text-decoration: none;
  }

  /* Background patterns */
  .bg-ym-grid {
    background-image:
      linear-gradient(to right, rgba(37, 99, 235, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(37, 99, 235, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .bg-ym-dots {
    background-image: radial-gradient(
      rgba(37, 99, 235, 0.2) 1px,
      transparent 1px
    );
    background-size: 20px 20px;
  }

  .bg-ym-stripes {
    background: repeating-linear-gradient(
      45deg,
      hsl(var(--background)),
      hsl(var(--background)) 10px,
      rgba(37, 99, 235, 0.05) 10px,
      rgba(37, 99, 235, 0.05) 20px
    );
  }

  /* Font utility classes */
  .font-montserrat {
    font-family: var(--font-montserrat), var(--ym-font-sans);
  }

  .font-playfair {
    font-family: var(--font-playfair), var(--ym-font-serif);
  }

  /* YoungMobility utility classes */
  .ym-border {
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
  }

  .ym-shadow-sm {
    box-shadow: var(--ym-shadow-sm);
  }

  .ym-shadow-md {
    box-shadow: var(--ym-shadow-md);
  }

  .ym-shadow-lg {
    box-shadow: var(--ym-shadow-lg);
  }
}

/* Shadcn UI Components with YoungMobility Styling */
@layer components {
  /* Card with YoungMobility styling */
  .ym-ui-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Button with YoungMobility styling */
  .ym-ui-button {
    @apply bg-primary text-white font-medium py-2 px-4 rounded-md
           transition-all duration-300 hover:bg-primary/90 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary/20;
  }

  /* Secondary button */
  .ym-ui-button-secondary {
    @apply bg-secondary text-white font-medium py-2 px-4 rounded-md
           transition-all duration-300 hover:bg-secondary/90 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-secondary/20;
  }

  /* Outline button */
  .ym-ui-button-outline {
    @apply bg-transparent border border-primary text-primary font-medium py-2 px-4 rounded-md
           transition-all duration-300 hover:bg-primary/10 focus:outline-none focus:ring-2 focus:ring-primary/20;
  }

  /* Ghost button */
  .ym-ui-button-ghost {
    @apply bg-transparent text-gray-700 font-medium py-2 px-4 rounded-md
           transition-all duration-300 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200;
  }

  /* Input with YoungMobility styling */
  .ym-ui-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary;
  }

  /* Horizontal scroller with improved styling */
  .ym-horizontal-scroller {
    @apply relative py-6 overflow-hidden;
  }

  .ym-horizontal-scroller-track {
    @apply flex overflow-x-auto space-x-6 py-6 px-2 scrollbar-hide cursor-grab active:cursor-grabbing;
  }

  .ym-horizontal-scroller-indicator {
    @apply flex justify-center mt-4 space-x-2;
  }

  .ym-horizontal-scroller-dot {
    @apply h-1 w-3 rounded-full transition-all duration-300;
  }

  .ym-horizontal-scroller-dot-active {
    @apply w-12 bg-primary;
  }

  .ym-horizontal-scroller-dot-inactive {
    @apply bg-gray-300;
  }

  /* Modern UI Components with Enhanced Styling */
  .modern-ui-card {
    @apply bg-white rounded-lg shadow-md border border-gray-200
           transition-all duration-300 hover:shadow-lg hover:-translate-y-1
           hover:border-blue-200;
  }

  .modern-ui-button {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium py-3 px-6 rounded-md
           transition-all duration-300 hover:from-blue-600 hover:to-blue-700
           hover:shadow-lg hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-blue-500/30
           border-2 border-transparent hover:border-blue-300/50;
  }

  .modern-ui-button-secondary {
    @apply bg-gradient-to-r from-green-500 to-green-600 text-white font-medium py-3 px-6 rounded-md
           transition-all duration-300 hover:from-green-600 hover:to-green-700
           hover:shadow-lg hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-green-500/30
           border-2 border-transparent hover:border-green-300/50;
  }

  .modern-ui-button-outline {
    @apply bg-transparent border-2 border-blue-500 text-blue-500 font-medium py-3 px-6 rounded-md
           transition-all duration-300 hover:bg-blue-50 hover:border-blue-600 hover:-translate-y-1
           focus:outline-none focus:ring-2 focus:ring-blue-500/30;
  }

  .modern-ui-input {
    @apply w-full px-4 py-3 border-2 border-gray-300 rounded-md
           focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500
           transition-all duration-200 hover:border-gray-400;
  }
}

/* Grid Animation */
@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* ===== GRADIENT BADGES ===== */

/* Base Gradient Badge Styles - Adapted from gradient buttons */
.gradient-badge {
  @apply relative appearance-none;
  background: radial-gradient(
    var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
    var(--color-1) var(--stop-1),
    var(--color-2) var(--stop-2),
    var(--color-3) var(--stop-3),
    var(--color-4) var(--stop-4),
    var(--color-5) var(--stop-5)
  );
  transition:
    --pos-x 0.3s,
    --pos-y 0.3s,
    --spread-x 0.3s,
    --spread-y 0.3s,
    --color-1 0.3s,
    --color-2 0.3s,
    --color-3 0.3s,
    --color-4 0.3s,
    --color-5 0.3s,
    --stop-1 0.3s,
    --stop-2 0.3s,
    --stop-3 0.3s,
    --stop-4 0.3s,
    --stop-5 0.3s;

  /* Default gradient properties - same as default button */
  --pos-x: 11.14%;
  --pos-y: 140%;
  --spread-x: 150%;
  --spread-y: 180.06%;
  --color-1: #000;
  --color-2: #08012c;
  --color-3: #4e1e40;
  --color-4: #70464e;
  --color-5: #88394c;
  --stop-1: 37.35%;
  --stop-2: 61.36%;
  --stop-3: 78.42%;
  --stop-4: 89.52%;
  --stop-5: 100%;
}

/* Default Badge - Dark gradient (same as default button) */
.gradient-badge-default {
  --color-1: #000;
  --color-2: #08012c;
  --color-3: #4e1e40;
  --color-4: #70464e;
  --color-5: #88394c;
  color: #ffffff;
}

.gradient-badge-default:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #c96287;
  --color-2: #c66c64;
  --color-3: #cc7d23;
  --color-4: #37140a;
  --color-5: #000;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
}

/* Secondary Badge - Blue/Teal gradient (same as variant button) */
.gradient-badge-secondary {
  --color-1: #000022;
  --color-2: #1f3f6d;
  --color-3: #469396;
  --color-4: #f1ffa5;
  --color-5: #469396;
  color: #ffffff;
}

.gradient-badge-secondary:hover {
  --pos-x: 0%;
  --pos-y: 95.51%;
  --spread-x: 110.24%;
  --spread-y: 110.2%;
  --color-1: #000020;
  --color-2: #f1ffa5;
  --color-3: #469396;
  --color-4: #1f3f6d;
  --color-5: #000;
  --stop-1: 0%;
  --stop-2: 10%;
  --stop-3: 35.44%;
  --stop-4: 71.34%;
  --stop-5: 90.76%;
}

/* Destructive Badge - Red gradient (same as destructive button) */
.gradient-badge-destructive {
  --color-1: #7f1d1d;
  --color-2: #991b1b;
  --color-3: #b91c1c;
  --color-4: #dc2626;
  --color-5: #ef4444;
  color: #ffffff;
}

.gradient-badge-destructive:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #fca5a5;
  --color-2: #f87171;
  --color-3: #fb7185;
  --color-4: #fda4af;
  --color-5: #fecaca;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  color: #000000;
}

/* Outline Badge - Transparent with gradient border (adapted from outline button) */
.gradient-badge-outline {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  color: #000000 !important;
  position: relative !important;
  border: 2px solid #000 !important;

  /* Default gradient variables for border */
  --pos-x: 11.14%;
  --pos-y: 140%;
  --spread-x: 150%;
  --spread-y: 180.06%;
  --color-1: #000;
  --color-2: #08012c;
  --color-3: #4e1e40;
  --color-4: #70464e;
  --color-5: #88394c;
  --stop-1: 37.35%;
  --stop-2: 61.36%;
  --stop-3: 78.42%;
  --stop-4: 89.52%;
  --stop-5: 100%;
}

/* Create gradient border using ::after pseudo-element */
.gradient-badge-outline::after {
  content: '' !important;
  position: absolute !important;
  inset: -2px !important;
  background: radial-gradient(
    var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
    var(--color-1) var(--stop-1),
    var(--color-2) var(--stop-2),
    var(--color-3) var(--stop-3),
    var(--color-4) var(--stop-4),
    var(--color-5) var(--stop-5)
  ) !important;
  border-radius: 9999px !important;
  z-index: -1 !important;
  pointer-events: none !important;
  display: block !important;
}

.gradient-badge-outline:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #c96287;
  --color-2: #c66c64;
  --color-3: #cc7d23;
  --color-4: #37140a;
  --color-5: #000;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  border-color: #c96287 !important;

  /* Gradient text effect */
  background: radial-gradient(
    var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
    var(--color-1) var(--stop-1),
    var(--color-2) var(--stop-2),
    var(--color-3) var(--stop-3),
    var(--color-4) var(--stop-4),
    var(--color-5) var(--stop-5)
  ) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
}

/* Additional Badge Variants - Success, Warning, Info, Ghost */

/* Success Badge - Green gradient */
.gradient-badge-success {
  --color-1: #064e3b;
  --color-2: #065f46;
  --color-3: #047857;
  --color-4: #059669;
  --color-5: #10b981;
  color: #ffffff;
}

.gradient-badge-success:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #059669;
  --color-2: #10b981;
  --color-3: #34d399;
  --color-4: #6ee7b7;
  --color-5: #a7f3d0;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  color: #000000;
}

/* Warning Badge - Orange/Yellow gradient */
.gradient-badge-warning {
  --color-1: #92400e;
  --color-2: #b45309;
  --color-3: #d97706;
  --color-4: #f59e0b;
  --color-5: #fbbf24;
  color: #ffffff;
}

.gradient-badge-warning:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #d97706;
  --color-2: #f59e0b;
  --color-3: #fbbf24;
  --color-4: #fcd34d;
  --color-5: #fde68a;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  color: #000000;
}

/* Info Badge - Blue gradient */
.gradient-badge-info {
  --color-1: #1e3a8a;
  --color-2: #1e40af;
  --color-3: #2563eb;
  --color-4: #3b82f6;
  --color-5: #60a5fa;
  color: #ffffff;
}

.gradient-badge-info:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #2563eb;
  --color-2: #3b82f6;
  --color-3: #60a5fa;
  --color-4: #93c5fd;
  --color-5: #bfdbfe;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  color: #000000;
}

/* Ghost Badge - Gray gradient (same as ghost button) */
.gradient-badge-ghost {
  --color-1: #374151;
  --color-2: #4b5563;
  --color-3: #6b7280;
  --color-4: #9ca3af;
  --color-5: #d1d5db;
  color: #ffffff;
}

.gradient-badge-ghost:hover {
  --pos-x: 0%;
  --pos-y: 91.51%;
  --spread-x: 120.24%;
  --spread-y: 103.18%;
  --color-1: #6b7280;
  --color-2: #9ca3af;
  --color-3: #d1d5db;
  --color-4: #e5e7eb;
  --color-5: #f3f4f6;
  --stop-1: 0%;
  --stop-2: 8.8%;
  --stop-3: 21.44%;
  --stop-4: 71.34%;
  --stop-5: 85.76%;
  color: #374151;
}

/* Custom Navigation Link Colors */
.nav-link-custom {
  color: #F8E7D1 !important;
  transition: color 0.3s ease;
}

.nav-link-custom:hover {
  color: #F8E7D1 !important;
}
