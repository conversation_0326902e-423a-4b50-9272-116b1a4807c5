"use client";

import { useState } from "react";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { GradientButton } from "@/components/ui/gradient-button";
import { User, LogOut } from "lucide-react";
import { NotificationCenter } from "@/components/notifications/notification-center";

export function UserButton() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const handleSignOut = async () => {
    setIsLoading(true);
    await signOut({ callbackUrl: "/" });
  };

  if (!session?.user) {
    return (
      <GradientButton variant="variant" asChild className="text-sm py-1.5 px-3 min-w-[80px]">
        <Link href="/login">Sign In</Link>
      </GradientButton>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <NotificationCenter />
      <GradientButton variant="variant" asChild className="text-sm py-1.5 px-3 min-w-[80px]">
        <Link href="/driver/profile">
          <User className="mr-2 h-4 w-4" />
          Profile
        </Link>
      </GradientButton>
      <GradientButton
        className="text-sm py-1.5 px-3 min-w-[80px]"
        onClick={handleSignOut}
        disabled={isLoading}
      >
        <LogOut className="mr-2 h-4 w-4" />
        {isLoading ? "Signing out..." : "Sign out"}
      </GradientButton>
    </div>
  );
}
