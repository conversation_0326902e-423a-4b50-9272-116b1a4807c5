'use client';

import { useEffect, useState } from 'react';

interface JsonLdProps {
  data: Record<string, any>;
}

/**
 * JsonLd-Komponente für strukturierte Daten
 * Rendert strukturierte Daten im JSON-LD-Format für SEO
 */
export default function JsonLd({ data }: JsonLdProps) {
  const [jsonString, setJsonString] = useState<string>('');

  useEffect(() => {
    // Konvertiere das Datenobjekt in einen JSON-String
    // und setze es nur auf der Client-Seite, um Hydration-Fehler zu vermeiden
    setJsonString(JSON.stringify(data));
  }, [data]);

  // Wenn kein JSON-String vorhanden ist (Server-Rendering), rendere nichts
  if (!jsonString) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: jsonString }}
    />
  );
}
