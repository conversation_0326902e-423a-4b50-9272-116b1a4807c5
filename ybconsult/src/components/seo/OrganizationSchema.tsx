import JsonLd from './JsonLd';

/**
 * OrganizationSchema-Komponente
 * Rendert strukturierte Daten für die Organisation im JSON-LD-Format
 */
export default function OrganizationSchema() {
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'YoungMobility',
    url: 'https://youngmobility.com',
    logo: 'https://youngmobility.com/images/logo.png',
    sameAs: [
      'https://www.facebook.com/youngmobility',
      'https://www.twitter.com/youngmobility',
      'https://www.linkedin.com/company/youngmobility',
      'https://www.instagram.com/youngmobility',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+49-**********',
      contactType: 'customer service',
      availableLanguage: ['German', 'English'],
    },
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Musterstraße 123',
      addressLocality: 'Berlin',
      postalCode: '10115',
      addressCountry: 'DE',
    },
    description: 'YoungMobility verbindet Geschäftskunden mit qualifizierten Fahrern für effiziente, transparente und kostengünstige Fahrzeugüberführungen.',
  };

  return <JsonLd data={organizationData} />;
}
