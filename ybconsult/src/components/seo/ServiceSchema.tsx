import JsonLd from './JsonLd';

/**
 * ServiceSchema-Komponente
 * Rendert strukturierte Daten für den Service im JSON-LD-Format
 */
export default function ServiceSchema() {
  const serviceData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: 'Fahrzeugüberführungen von YoungMobility',
    provider: {
      '@type': 'Organization',
      name: 'YoungMobility',
      url: 'https://youngmobility.com',
    },
    serviceType: 'Fahrzeugüberführung',
    areaServed: {
      '@type': 'Country',
      name: 'Deutschland',
    },
    description: 'Professionelle Fahrzeugüberführungen für Autovermietungen, Leasing-Unternehmen, Autohändler und Flottenmanagement.',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'EUR',
      priceSpecification: {
        '@type': 'PriceSpecification',
        description: 'Die Preise variieren je nach Entfernung, Fahrzeugtyp und Dringlichkeit.',
      },
    },
  };

  return <JsonLd data={serviceData} />;
}
