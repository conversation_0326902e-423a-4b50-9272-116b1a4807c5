import JsonLd from './JsonLd';

interface BlogPostSchemaProps {
  title: string;
  description: string;
  datePublished: string;
  dateModified?: string;
  author: {
    name: string;
    url?: string;
  };
  image: string;
  url: string;
}

/**
 * BlogPostSchema-Komponente
 * Rendert strukturierte Daten für Blog-Beiträge im JSON-LD-Format
 */
export default function BlogPostSchema({
  title,
  description,
  datePublished,
  dateModified,
  author,
  image,
  url,
}: BlogPostSchemaProps) {
  const blogPostData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: title,
    description: description,
    image: image,
    datePublished: datePublished,
    dateModified: dateModified || datePublished,
    author: {
      '@type': 'Person',
      name: author.name,
      url: author.url,
    },
    publisher: {
      '@type': 'Organization',
      name: 'YoungMobility',
      logo: {
        '@type': 'ImageObject',
        url: 'https://youngmobility.com/images/logo.png',
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url,
    },
  };

  return <JsonLd data={blogPostData} />;
}
