"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface FeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
  orderTitle: string;
  type: "rejection" | "assignment";
}

export default function FeedbackDialog({ isOpen, onClose, orderId, orderTitle, type }: FeedbackDialogProps) {
  const [feedback, setFeedback] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error("Bitte geben Sie ein Feedback ein");
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/driver/feedback`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId,
          feedback,
          type,
        }),
      });
      
      if (!response.ok) {
        throw new Error("Fehler beim Senden des Feedbacks");
      }
      
      toast.success("Feedback erfolgreich gesendet");
      setFeedback("");
      onClose();
      
    } catch (error) {
      console.error("Fehler beim Senden des Feedbacks:", error);
      toast.error("Fehler beim Senden des Feedbacks. Bitte versuchen Sie es später erneut.");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {type === "rejection" 
              ? "Feedback zur Ablehnung" 
              : "Feedback zur Auftragszuweisung"}
          </DialogTitle>
          <DialogDescription>
            {type === "rejection"
              ? "Wir möchten verstehen, warum dieser Auftrag nicht für Sie geeignet war. Ihr Feedback hilft uns, zukünftige Zuweisungen zu verbessern."
              : "Teilen Sie uns mit, wie Sie die Auftragszuweisung empfunden haben. Ihr Feedback hilft uns, den Prozess zu verbessern."}
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <h4 className="text-sm font-medium mb-2">Auftrag: {orderTitle}</h4>
          <Textarea
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            placeholder={type === "rejection"
              ? "Warum war dieser Auftrag nicht für Sie geeignet? (z.B. Zeitliche Gründe, Entfernung, etc.)"
              : "Wie haben Sie die Auftragszuweisung empfunden? War der Prozess klar und transparent?"}
            rows={5}
          />
        </div>
        
        <DialogFooter className="flex space-x-2 justify-end">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Abbrechen
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Wird gesendet...
              </>
            ) : (
              "Feedback senden"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
