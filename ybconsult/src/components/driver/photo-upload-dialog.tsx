"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Camera, Upload, X } from "lucide-react";

interface PhotoUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
  photoType: "PICKUP" | "DELIVERY";
  onSuccess?: () => void;
}

export function PhotoUploadDialog({
  open,
  onOpenChange,
  orderId,
  photoType,
  onSuccess,
}: PhotoUploadDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [notes, setNotes] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  // Mutation für das Hochladen des Fotos
  const uploadPhotoMutation = useMutation({
    mutationFn: async (photoUrl: string) => {
      const response = await fetch(`/api/driver/orders/${orderId}/photos`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          photoType,
          photoUrl,
          notes,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler beim Hochladen des Fotos");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Foto erfolgreich hochgeladen");
      resetForm();
      onSuccess?.();
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
      setIsUploading(false);
    },
  });

  // Datei auswählen
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Überprüfen, ob es sich um ein Bild handelt
    if (!file.type.startsWith("image/")) {
      toast.error("Bitte wählen Sie eine Bilddatei aus");
      return;
    }

    setSelectedFile(file);

    // Vorschau erstellen
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Foto hochladen
  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Bitte wählen Sie ein Foto aus");
      return;
    }

    setIsUploading(true);

    try {
      // In einer echten Anwendung würde hier das Foto zu einem Speicherdienst (z.B. S3, Cloudinary) hochgeladen werden
      // und die zurückgegebene URL an die API übergeben werden.
      // Für dieses Beispiel simulieren wir das mit einer Verzögerung und verwenden die Vorschau-URL.

      // Simuliere Upload zu einem Speicherdienst
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In einer echten Anwendung würde hier die URL des hochgeladenen Fotos zurückgegeben werden
      const mockUploadedUrl = previewUrl || "";

      // Foto-URL an die API senden
      await uploadPhotoMutation.mutateAsync(mockUploadedUrl);
    } catch (error) {
      console.error("Fehler beim Hochladen:", error);
      setIsUploading(false);
      toast.error("Beim Hochladen ist ein Fehler aufgetreten");
    }
  };

  // Formular zurücksetzen
  const resetForm = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setNotes("");
    setIsUploading(false);
  };

  // Dialog schließen
  const handleClose = () => {
    if (!isUploading) {
      resetForm();
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            Foto für {photoType === "PICKUP" ? "Abholung" : "Lieferung"} hochladen
          </DialogTitle>
          <DialogDescription>
            Laden Sie ein Foto hoch, um den {photoType === "PICKUP" ? "Abholvorgang" : "Liefervorgang"} zu dokumentieren.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {previewUrl ? (
            <div className="relative">
              <img
                src={previewUrl}
                alt="Vorschau"
                className="w-full h-auto max-h-64 object-contain rounded-md"
              />
              <Button
                variant="outline"
                size="icon"
                className="absolute top-2 right-2 h-8 w-8 rounded-full bg-white/80"
                onClick={() => {
                  setSelectedFile(null);
                  setPreviewUrl(null);
                }}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-md p-8">
              <Camera className="h-10 w-10 text-muted-foreground mb-4" />
              <p className="text-sm text-muted-foreground mb-2">
                Klicken Sie, um ein Foto auszuwählen oder ziehen Sie es hierher
              </p>
              <Label
                htmlFor="photo-upload"
                className="cursor-pointer bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm font-medium"
              >
                Foto auswählen
              </Label>
              <input
                id="photo-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
                disabled={isUploading}
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Anmerkungen (optional)</Label>
            <Textarea
              id="notes"
              placeholder="Zusätzliche Informationen zum Foto..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={isUploading}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isUploading}
          >
            Abbrechen
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
          >
            {isUploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                Wird hochgeladen...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Hochladen
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
