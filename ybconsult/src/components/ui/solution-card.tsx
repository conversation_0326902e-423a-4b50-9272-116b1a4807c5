"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { ChevronRightIcon } from './icons';

interface SolutionCardProps {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  delay?: number;
}

export const SolutionCard: React.FC<SolutionCardProps> = ({
  id,
  title,
  description,
  imageUrl,
  delay = 0
}) => {
  return (
    <motion.div
      className="bg-black rounded-xl overflow-hidden shadow-2xl hover:shadow-3xl transition-all duration-300 relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <GlowingEffect
        disabled={false}
        proximity={100}
        spread={25}
        blur={1.5}
        className="rounded-xl"
      />
      <div className="relative h-48">
        <Image
          src={imageUrl}
          alt={title}
          width={400}
          height={200}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="p-6 relative z-10">
        <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
        <p className="text-[#F8E7D1] text-sm mb-4">{description}</p>
        <Link
          href={`/solutions/${id}`}
          className="text-[#F8E7D1] font-medium text-sm flex items-center transition-colors hover:text-white"
        >
          Mehr erfahren
          <ChevronRightIcon className="w-4 h-4 ml-1" />
        </Link>
      </div>
    </motion.div>
  );
};
