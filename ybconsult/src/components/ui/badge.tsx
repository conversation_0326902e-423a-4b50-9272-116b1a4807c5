import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 gradient-badge",
  {
    variants: {
      variant: {
        default: "gradient-badge-default",
        secondary: "gradient-badge-secondary",
        destructive: "gradient-badge-destructive",
        outline: "gradient-badge-outline",
        success: "gradient-badge-success",
        warning: "gradient-badge-warning",
        info: "gradient-badge-info",
        ghost: "gradient-badge-ghost",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
