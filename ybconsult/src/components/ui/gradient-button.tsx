"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const gradientButtonVariants = cva(
  [
    "gradient-button",
    "inline-flex items-center justify-center",
    "rounded-[11px] min-w-[132px] px-9 py-4",
    "text-base leading-[19px] font-[500]",
    "font-sans font-bold",
    "focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
    "disabled:pointer-events-none disabled:opacity-50",
    "no-underline",
  ],
  {
    variants: {
      variant: {
        default: "text-white",
        variant: "text-white gradient-button-variant",
        destructive: "text-white gradient-button-destructive",
        outline: "text-white gradient-button-outline",
        ghost: "text-white gradient-button-ghost",
        link: "gradient-button-link",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface GradientButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof gradientButtonVariants> {
  asChild?: boolean
  style?: React.CSSProperties
  onMouseEnter?: (e: React.MouseEvent<HTMLButtonElement>) => void
  onMouseLeave?: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
  ({ className, variant, asChild = false, style, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"

    // Force no text decoration with inline styles
    const forceNoUnderlineStyle = {
      textDecoration: 'none',
      ...style
    }

    return (
      <Comp
        className={cn(gradientButtonVariants({ variant, className }))}
        style={forceNoUnderlineStyle}
        ref={ref}
        onMouseEnter={(e) => {
          e.currentTarget.style.textDecoration = 'none';
          if (props.onMouseEnter) props.onMouseEnter(e);
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.textDecoration = 'none';
          if (props.onMouseLeave) props.onMouseLeave(e);
        }}
        {...props}
      />
    )
  }
)
GradientButton.displayName = "GradientButton"

export { GradientButton, gradientButtonVariants }