/**
 * Unit Tests für GradientButton
 * 
 * Diese Datei enthält Tests für die GradientButton-Komponente.
 * Teil der Phase 8: Comprehensive Automated Testing (YM-805)
 */

import React from "react";
import { customRender, screen, userEvent } from "@/utils/test-utils";
import { GradientButton } from "./gradient-button";

describe("GradientButton", () => {
  it("renders correctly with default props", () => {
    customRender(<GradientButton>Test Button</GradientButton>);
    
    const button = screen.getByRole("button", { name: /test button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("gradient-button");
    expect(button).not.toHaveClass("gradient-button-variant");
  });
  
  it("renders with variant class when variant prop is provided", () => {
    customRender(<GradientButton variant="variant"><PERSON><PERSON><PERSON></GradientButton>);
    
    const button = screen.getByRole("button", { name: /variant button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("gradient-button-variant");
  });
  
  it("applies additional className when provided", () => {
    customRender(<GradientButton className="custom-class">Custom Button</GradientButton>);
    
    const button = screen.getByRole("button", { name: /custom button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("custom-class");
  });
  
  it("calls onClick handler when clicked", async () => {
    const handleClick = vi.fn();
    customRender(<GradientButton onClick={handleClick}>Click Me</GradientButton>);
    
    const button = screen.getByRole("button", { name: /click me/i });
    await userEvent.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it("is disabled when disabled prop is true", async () => {
    const handleClick = vi.fn();
    customRender(
      <GradientButton onClick={handleClick} disabled>
        Disabled Button
      </GradientButton>
    );
    
    const button = screen.getByRole("button", { name: /disabled button/i });
    expect(button).toBeDisabled();
    
    await userEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });
  
  it("renders as a link when asChild and Link are used", () => {
    const Link = ({ children, ...props }: any) => (
      <a href="/test" {...props}>
        {children}
      </a>
    );
    
    customRender(
      <GradientButton asChild>
        <Link>Link Button</Link>
      </GradientButton>
    );
    
    const link = screen.getByRole("link", { name: /link button/i });
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute("href", "/test");
    expect(link).toHaveClass("gradient-button");
  });
  
  it("applies quechua-gradient-button class when using Quechua theme", () => {
    customRender(<GradientButton className="quechua-gradient-button">Quechua Button</GradientButton>);
    
    const button = screen.getByRole("button", { name: /quechua button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("quechua-gradient-button");
  });
  
  it("applies quechua-gradient-button-variant class when using Quechua theme with variant", () => {
    customRender(
      <GradientButton 
        variant="variant" 
        className="quechua-gradient-button-variant"
      >
        Quechua Variant Button
      </GradientButton>
    );
    
    const button = screen.getByRole("button", { name: /quechua variant button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("quechua-gradient-button-variant");
  });
});
