"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { CardSpotlight } from '@/components/ui/card-spotlight';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  delay = 0
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <CardSpotlight className="h-full">
        <div className="relative z-50" style={{ transform: 'translate3d(0,0,0)' }}>
          <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center mb-6" style={{ transform: 'translate3d(0,0,0)' }}>
            <div className="w-6 h-6 ml-3 flex items-center justify-center">
              {icon}
            </div>
          </div>
          <h3 className="text-xl font-semibold text-white mb-3">{title}</h3>
          <p className="text-gray-300">{description}</p>
        </div>
      </CardSpotlight>
    </motion.div>
  );
};
