"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface OverlayCardProps {
  title: string;
  description: string;
  imageUrl: string;
  linkUrl: string;
  linkText: string;
  buttonClassName?: string;
  delay?: number;
}

export const OverlayCard: React.FC<OverlayCardProps> = ({
  title,
  description,
  imageUrl,
  linkUrl,
  linkText,
  buttonClassName = "ym-ui-button",
  delay = 0
}) => {
  return (
    <motion.div 
      className="relative rounded-xl overflow-hidden shadow-lg group"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70 z-10"></div>
      <Image
        src={imageUrl}
        alt={title}
        width={600}
        height={400}
        className="w-full h-[300px] object-cover transition-transform duration-500 group-hover:scale-105"
      />
      <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
        <h3 className="text-2xl font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-200 mb-4">
          {description}
        </p>
        <Link href={linkUrl} className={`${buttonClassName} inline-block`}>
          {linkText}
        </Link>
      </div>
    </motion.div>
  );
};
