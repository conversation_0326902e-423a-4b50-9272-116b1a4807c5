"use client"

import * as React from "react"
import { <PERSON>, <PERSON>, Palette } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

type Theme = "light" | "dark" | "modern"

interface ThemeToggleProps {
  className?: string
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const [theme, setTheme] = React.useState<Theme>("light")

  React.useEffect(() => {
    // Get theme from localStorage or default to light
    const savedTheme = localStorage.getItem("theme") as Theme || "light"
    setTheme(savedTheme)
    applyTheme(savedTheme)
  }, [])

  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement
    
    // Remove existing theme classes
    root.classList.remove("light", "dark", "modern")
    
    // Apply new theme
    root.classList.add(newTheme)
    
    // Update CSS variables based on theme
    if (newTheme === "modern") {
      root.style.setProperty("--background", "220 17% 95%")
      root.style.setProperty("--foreground", "215 25% 27%")
      root.style.setProperty("--primary", "217 91% 60%")
      root.style.setProperty("--card", "0 0% 100%")
      root.style.setProperty("--border", "214 32% 91%")
    } else if (newTheme === "dark") {
      root.style.setProperty("--background", "222 47% 11%")
      root.style.setProperty("--foreground", "210 40% 98%")
      root.style.setProperty("--primary", "217 91% 60%")
      root.style.setProperty("--card", "222 47% 14%")
      root.style.setProperty("--border", "217 33% 25%")
    } else {
      // Light theme
      root.style.setProperty("--background", "0 0% 100%")
      root.style.setProperty("--foreground", "215 25% 27%")
      root.style.setProperty("--primary", "221 83% 53%")
      root.style.setProperty("--card", "0 0% 100%")
      root.style.setProperty("--border", "214 32% 91%")
    }
    
    // Save to localStorage
    localStorage.setItem("theme", newTheme)
  }

  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme)
    applyTheme(newTheme)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className={className}>
          {theme === "light" && <Sun className="h-[1.2rem] w-[1.2rem]" />}
          {theme === "dark" && <Moon className="h-[1.2rem] w-[1.2rem]" />}
          {theme === "modern" && <Palette className="h-[1.2rem] w-[1.2rem]" />}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleThemeChange("light")}>
          <Sun className="mr-2 h-4 w-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleThemeChange("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleThemeChange("modern")}>
          <Palette className="mr-2 h-4 w-4" />
          <span>Modern</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
