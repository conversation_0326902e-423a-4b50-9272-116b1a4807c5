"use client";
import React from "react";
import { SparklesCore } from "./sparkles";

/**
 * Example usage of the SparklesCore component
 * This file demonstrates various ways to use the sparkles effect
 */

// Basic usage example
export const BasicSparklesExample = () => {
  return (
    <div className="relative h-96 w-full bg-black rounded-lg overflow-hidden">
      <SparklesCore
        id="basic-sparkles"
        className="h-full w-full"
        background="transparent"
        particleColor="#ffffff"
        particleDensity={120}
        minSize={1}
        maxSize={3}
        speed={4}
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <h2 className="text-white text-2xl font-bold">Basic Sparkles</h2>
      </div>
    </div>
  );
};

// Hero section with sparkles background
export const HeroSparklesExample = () => {
  return (
    <div className="relative h-screen w-full bg-black overflow-hidden">
      <SparklesCore
        id="hero-sparkles"
        className="h-full w-full"
        background="transparent"
        particleColor="#F8E7D1" // Cream color matching YoungMobility theme
        particleDensity={100}
        minSize={0.8}
        maxSize={2.5}
        speed={3}
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
        <h1 className="text-white text-6xl font-bold mb-4">
          Welcome to YoungMobility
        </h1>
        <p className="text-cream text-xl max-w-2xl">
          Experience the magic of premium transportation with our sparkling interface
        </p>
      </div>
    </div>
  );
};

// Card with sparkles effect
export const CardSparklesExample = () => {
  return (
    <div className="relative w-96 h-64 bg-gradient-to-br from-gray-900 to-black rounded-xl overflow-hidden border border-gray-700">
      <SparklesCore
        id="card-sparkles"
        className="h-full w-full"
        background="transparent"
        particleColor="#ffd700" // Gold color
        particleDensity={80}
        minSize={0.5}
        maxSize={2}
        speed={2}
      />
      <div className="absolute inset-0 p-6 flex flex-col justify-between">
        <div>
          <h3 className="text-white text-xl font-semibold mb-2">Premium Service</h3>
          <p className="text-gray-300 text-sm">
            Luxury transportation with sparkling attention to detail
          </p>
        </div>
        <button className="bg-gold text-black px-4 py-2 rounded-lg font-medium hover:bg-yellow-400 transition-colors">
          Learn More
        </button>
      </div>
    </div>
  );
};

// Subtle background sparkles
export const SubtleSparklesExample = () => {
  return (
    <div className="relative min-h-96 w-full bg-gray-900 p-8">
      <SparklesCore
        id="subtle-sparkles"
        className="absolute inset-0"
        background="transparent"
        particleColor="#ffffff"
        particleDensity={50}
        minSize={0.3}
        maxSize={1}
        speed={1}
      />
      <div className="relative z-10">
        <h2 className="text-white text-3xl font-bold mb-6">Subtle Background Effect</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-black/50 p-6 rounded-lg backdrop-blur-sm">
            <h3 className="text-white text-xl font-semibold mb-3">Feature One</h3>
            <p className="text-gray-300">
              This content sits above the subtle sparkles background effect.
            </p>
          </div>
          <div className="bg-black/50 p-6 rounded-lg backdrop-blur-sm">
            <h3 className="text-white text-xl font-semibold mb-3">Feature Two</h3>
            <p className="text-gray-300">
              The sparkles add a magical touch without being distracting.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Interactive sparkles (responds to clicks)
export const InteractiveSparklesExample = () => {
  return (
    <div className="relative h-96 w-full bg-gradient-to-br from-purple-900 to-black rounded-lg overflow-hidden">
      <SparklesCore
        id="interactive-sparkles"
        className="h-full w-full"
        background="transparent"
        particleColor="#a855f7" // Purple color
        particleDensity={100}
        minSize={1}
        maxSize={4}
        speed={3}
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-white text-2xl font-bold mb-4">Interactive Sparkles</h2>
          <p className="text-purple-200 mb-4">Click anywhere to add more particles!</p>
          <div className="text-sm text-purple-300">
            The sparkles component supports click interactions
          </div>
        </div>
      </div>
    </div>
  );
};
