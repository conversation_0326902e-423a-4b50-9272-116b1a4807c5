import type { Meta, StoryObj } from '@storybook/react';
import { Input } from '@/components/ui/input'; // Pfad zu Ihrer Shadcn Input Komponente

/**
 * Meta-Informationen für die Input-Komponente in Storybook.
 */
const meta: Meta<typeof Input> = {
  title: 'UI/Shadcn/Input',
  component: Input,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'text',
      description: 'Der Typ des Input-Feldes (z.B. text, password, email).',
    },
    placeholder: {
      control: 'text',
      description: 'Der Platzhaltertext für das Input-Feld.',
    },
    disabled: {
      control: 'boolean',
      description: 'Ob das Input-Feld deaktiviert ist.',
    },
    value: {
      control: 'text',
      description: 'Der Wert des Input-Feldes (für kontrollierte Komponente).',
    }
  },
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

/**
 * Standard-Input-Story.
 */
export const Default: Story = {
  args: {
    type: 'text',
    placeholder: 'Enter text...',
  },
};

/**
 * Deaktivierte Input-Story.
 */
export const Disabled: Story = {
  args: {
    type: 'text',
    placeholder: 'Disabled input',
    disabled: true,
  },
};

/**
 * Passwort-Input-Story.
 */
export const Password: Story = {
  args: {
    type: 'password',
    placeholder: 'Enter password',
  },
};

/**
 * Input-Story mit einem vordefinierten Wert.
 */
export const WithValue: Story = {
  args: {
    type: 'text',
    placeholder: 'Enter text...',
    value: 'Initial value',
  },
};