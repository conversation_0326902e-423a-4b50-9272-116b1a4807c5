"use client";

import { motion } from "framer-motion";

interface FloatingPathsProps {
  position?: number;
  className?: string;
}

export function FloatingPaths({ position = 1, className = "" }: FloatingPathsProps) {
  const paths = Array.from({ length: 36 }, (_, i) => ({
    id: i,
    d: `M-${380 - i * 5 * position} -${289 + i * 6}C-${
      380 - i * 5 * position
    } -${289 + i * 6} -${312 - i * 5 * position} ${116 - i * 6} ${
      152 - i * 5 * position
    } ${243 - i * 6}C${616 - i * 5 * position} ${370 - i * 6} ${
      684 - i * 5 * position
    } ${775 - i * 6} ${684 - i * 5 * position} ${775 - i * 6}`,
    color: `rgba(15,23,42,${0.1 + i * 0.03})`,
    width: 0.5 + i * 0.03,
  }));

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      <svg
        className="w-full h-full"
        viewBox="0 0 696 316"
        fill="none"
      >
        <title>Background Paths</title>
        {paths.map((path) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke="#F8E7D1"
            strokeWidth={path.width}
            strokeOpacity={0.08 + path.id * 0.02}
            initial={{ pathLength: 0.3, opacity: 0.4 }}
            animate={{
              pathLength: 1,
              opacity: [0.2, 0.4, 0.2],
              pathOffset: [0, 1, 0],
            }}
            transition={{
              duration: 20 + Math.random() * 10,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        ))}
      </svg>
    </div>
  );
}
