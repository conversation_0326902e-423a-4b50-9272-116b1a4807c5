import type { Meta, StoryObj } from '@storybook/react';
import { SparklesCore } from './sparkles';

const meta: Meta<typeof SparklesCore> = {
  title: 'UI/SparklesCore',
  component: SparklesCore,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    background: {
      control: 'color',
      description: 'Background color of the particles container',
    },
    particleColor: {
      control: 'color',
      description: 'Color of the particles',
    },
    particleDensity: {
      control: { type: 'range', min: 10, max: 300, step: 10 },
      description: 'Number of particles',
    },
    minSize: {
      control: { type: 'range', min: 0.5, max: 5, step: 0.1 },
      description: 'Minimum particle size',
    },
    maxSize: {
      control: { type: 'range', min: 1, max: 10, step: 0.1 },
      description: 'Maximum particle size',
    },
    speed: {
      control: { type: 'range', min: 1, max: 10, step: 0.5 },
      description: 'Animation speed',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#000000',
    particleColor: '#ffffff',
    particleDensity: 120,
    minSize: 1,
    maxSize: 3,
    speed: 4,
  },
};

export const BlueTheme: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#0d47a1',
    particleColor: '#64b5f6',
    particleDensity: 80,
    minSize: 0.5,
    maxSize: 2,
    speed: 3,
  },
};

export const GoldTheme: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#1a1a1a',
    particleColor: '#ffd700',
    particleDensity: 150,
    minSize: 1,
    maxSize: 4,
    speed: 2,
  },
};

export const CreamTheme: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#000000',
    particleColor: '#F8E7D1',
    particleDensity: 100,
    minSize: 0.8,
    maxSize: 2.5,
    speed: 5,
  },
};

export const HighDensity: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#000000',
    particleColor: '#ffffff',
    particleDensity: 250,
    minSize: 0.5,
    maxSize: 1.5,
    speed: 6,
  },
};

export const LargeSparks: Story = {
  args: {
    className: 'h-96 w-full',
    background: '#1a1a2e',
    particleColor: '#e94560',
    particleDensity: 50,
    minSize: 3,
    maxSize: 8,
    speed: 1,
  },
};

export const FullScreen: Story = {
  args: {
    className: 'h-screen w-full fixed inset-0',
    background: 'transparent',
    particleColor: '#ffffff',
    particleDensity: 120,
    minSize: 1,
    maxSize: 3,
    speed: 4,
  },
};
