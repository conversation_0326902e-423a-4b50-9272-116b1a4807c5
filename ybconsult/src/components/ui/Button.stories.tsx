import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from '@/components/ui/button'; // Pfad zu Ihrer Shadcn Button Komponente

/**
 * Meta-Informationen für die Button-Komponente in Storybook.
 * Definier<PERSON> den Titel, die Komponente selbst, Tags für Autodocs und argTypes für die Steuerung der Props.
 */
const meta: Meta<typeof Button> = {
  title: 'UI/Shadcn/Button', // Kategorie und Name in der Storybook UI
  component: Button,
  tags: ['autodocs'], // Aktiviert automatische Dokumentationsgenerierung
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
      description: 'Der Stilvariante des Buttons.',
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'sm', 'lg', 'icon'],
      description: 'Die Größe des Buttons.',
    },
    children: {
      control: 'text',
      description: 'Der Inhalt des Buttons (Text oder andere Elemente).',
    },
    asChild: {
      control: 'boolean',
      description: 'Ob der Button als Kind-Komponente gerendert werden soll, um dessen Props zu übernehmen.',
    }
  },
  parameters: {
    layout: 'centered', // Zentriert die Komponente im Canvas
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

/**
 * Standard-Button-Story. Zeigt den Button mit Standard-Props.
 */
export const Default: Story = {
  args: {
    variant: 'default',
    children: 'Button',
  },
};

/**
 * Destructive-Button-Story. Zeigt den Button für destruktive Aktionen.
 */
export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Destructive',
  },
};

/**
 * Outline-Button-Story. Zeigt den Button mit Outline-Stil.
 */
export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline',
  },
};

/**
 * Secondary-Button-Story. Zeigt den Button im sekundären Stil.
 */
export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary',
  },
};

/**
 * Ghost-Button-Story. Zeigt den Button im Ghost-Stil.
 */
export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost',
  },
};

/**
 * Link-Button-Story. Zeigt den Button, der wie ein Link aussieht.
 */
export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Link',
  },
};

/**
 * Icon-Button-Story. Zeigt einen Button, der nur ein Icon enthält (Icon muss als Kind übergeben werden).
 */
export const IconButton: Story = {
  args: {
    variant: 'outline',
    size: 'icon',
    children: '📷', // Beispiel-Icon, ersetzen Sie dies durch eine SVG-Icon-Komponente
  },
};