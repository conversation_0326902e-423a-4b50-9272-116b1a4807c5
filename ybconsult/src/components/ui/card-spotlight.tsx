"use client";

import { useMotionValue, motion, useMotionTemplate } from "framer-motion";
import React, { MouseEvent as ReactMouseEvent, useState } from "react";
import { CanvasRevealEffect } from "@/components/ui/canvas-reveal-effect";
import { cn } from "@/lib/utils";

export const CardSpotlight = ({
  children,
  radius = 300,
  color = "#262626",
  className,
  ...props
}: {
  radius?: number;
  color?: string;
  children: React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const [isHovering, setIsHovering] = useState(false);

  // Create motion templates that will update with mouse movement
  const spotlightBackground = useMotionTemplate`
    radial-gradient(
      ${radius}px circle at ${mouseX}px ${mouseY}px,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.08),
      transparent 70%
    )
  `;

  const maskImage = useMotionTemplate`
    radial-gradient(
      ${radius}px circle at ${mouseX}px ${mouseY}px,
      white,
      transparent 80%
    )
  `;

  function handleMouseMove({
    currentTarget,
    clientX,
    clientY,
  }: ReactMouseEvent<HTMLDivElement>) {
    let { left, top } = currentTarget.getBoundingClientRect();
    mouseX.set(clientX - left);
    mouseY.set(clientY - top);
  }

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  return (
    <div
      className={cn(
        "group/spotlight p-10 rounded-md relative border bg-black overflow-hidden",
        className
      )}
      style={{ borderColor: '#ffffff', borderWidth: '0.8px' }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      {/* Spotlight effect that follows mouse */}
      <motion.div
        className="pointer-events-none absolute inset-0 rounded-md z-0"
        style={{
          background: spotlightBackground,
          transform: 'translateZ(0)', // Force hardware acceleration
        }}
        animate={{
          opacity: isHovering ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
      />

      {/* Canvas effect masked to spotlight area */}
      <motion.div
        className="pointer-events-none absolute inset-0 rounded-md z-10"
        style={{
          maskImage: maskImage,
          WebkitMaskImage: maskImage, // Safari support
          transform: 'translateZ(0)', // Force hardware acceleration
          willChange: 'opacity', // Optimize for opacity changes
        }}
        animate={{
          opacity: isHovering ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
      >
        {isHovering && (
          <CanvasRevealEffect
            animationSpeed={3}
            containerClassName="absolute inset-0 pointer-events-none"
            colors={[
              [255, 255, 255],
              [200, 200, 200],
            ]}
            dotSize={3}
            showGradient={false}
          />
        )}
      </motion.div>

      {children}
    </div>
  );
};
