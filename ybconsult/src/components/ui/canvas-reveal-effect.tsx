"use client";
import { cn } from "@/lib/utils";
import React from "react";
import { motion } from "framer-motion";

export const CanvasRevealEffect = ({
  animationSpeed = 0.4,
  opacities = [0.3, 0.3, 0.3, 0.5, 0.5, 0.5, 0.8, 0.8, 0.8, 1],
  colors = [[59, 130, 246], [139, 92, 246]],
  containerClassName,
  dotSize = 3,
  showGradient = true,
}: {
  animationSpeed?: number;
  opacities?: number[];
  colors?: number[][];
  containerClassName?: string;
  dotSize?: number;
  showGradient?: boolean;
}) => {
  // Create a grid of dots
  const gridSize = 20;
  const dots = [];

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      dots.push({
        id: `${i}-${j}`,
        x: (i / gridSize) * 100,
        y: (j / gridSize) * 100,
        delay: Math.random() * 2,
        opacity: opacities[Math.floor(Math.random() * opacities.length)],
        color: colors[Math.floor(Math.random() * colors.length)],
      });
    }
  }

  return (
    <div className={cn("h-full relative w-full", containerClassName)}>
      <div className="h-full w-full relative overflow-hidden">
        {dots.map((dot) => (
          <motion.div
            key={dot.id}
            className="absolute rounded-full"
            style={{
              left: `${dot.x}%`,
              top: `${dot.y}%`,
              width: `${dotSize}px`,
              height: `${dotSize}px`,
              backgroundColor: `rgb(${dot.color[0]}, ${dot.color[1]}, ${dot.color[2]})`,
            }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{
              opacity: [0, dot.opacity, 0],
              scale: [0, 1, 0],
            }}
            transition={{
              duration: 2 / animationSpeed,
              delay: dot.delay,
              repeat: Infinity,
              repeatDelay: Math.random() * 3,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
      {showGradient && (
        <div className="absolute inset-0 bg-gradient-to-t from-gray-950 to-[84%] pointer-events-none" />
      )}
    </div>
  );
};


