"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { RatingForm } from "./rating-form";
import { RatingDisplay } from "./rating-display";

interface RatingContainerProps {
  orderId: string;
  className?: string;
}

export function RatingContainer({ orderId, className = "" }: RatingContainerProps) {
  const { data: session } = useSession();
  const [showRatingForm, setShowRatingForm] = useState(false);
  
  // Auftragsdaten abrufen, um den zu bewertenden Benutzer zu bestimmen
  const { data: orderData, isLoading, error } = useQuery({
    queryKey: ["order", orderId],
    queryFn: async () => {
      const response = await fetch(`/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Auftragsdaten");
      }
      return response.json();
    },
  });
  
  // Überprüfen, ob der Benutzer bereits eine Bewertung abgegeben hat
  const { data: ratingData, isLoading: isLoadingRating } = useQuery({
    queryKey: ["user-rating", orderId, session?.user?.id],
    queryFn: async () => {
      if (!session?.user?.id || !orderData?.order) return null;
      
      const order = orderData.order;
      const isClient = session.user.id === order.clientId;
      const ratedUserId = isClient ? order.assignment?.driverId : order.clientId;
      
      if (!ratedUserId) return null;
      
      const response = await fetch(`/api/ratings/check?orderId=${orderId}&raterId=${session.user.id}&ratedUserId=${ratedUserId}`);
      if (!response.ok) {
        if (response.status === 404) return { hasRated: false };
        throw new Error("Fehler beim Überprüfen der Bewertung");
      }
      return response.json();
    },
    enabled: !!session?.user?.id && !!orderData?.order,
  });
  
  if (isLoading || isLoadingRating) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }
  
  if (error || !orderData?.order) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Bewertungen</CardTitle>
          <CardDescription>Fehler beim Laden der Auftragsdaten</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  const order = orderData.order;
  
  // Überprüfen, ob der Auftrag abgeschlossen ist
  const isCompleted = order.status === "COMPLETED";
  
  if (!isCompleted) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Bewertungen</CardTitle>
          <CardDescription>
            Bewertungen können erst nach Abschluss des Auftrags abgegeben werden.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  // Bestimmen, ob der aktuelle Benutzer der Kunde oder der Fahrer ist
  const isClient = session?.user?.id === order.clientId;
  const isDriver = session?.user?.id === order.assignment?.driverId;
  
  if (!isClient && !isDriver) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Bewertungen</CardTitle>
          <CardDescription>
            Sie haben keine Berechtigung, auf diese Bewertungen zuzugreifen.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  // Zu bewertenden Benutzer bestimmen
  const ratedUserId = isClient ? order.assignment?.driverId : order.clientId;
  const ratedUserName = isClient
    ? `${order.assignment?.driver?.driverProfile?.firstName} ${order.assignment?.driver?.driverProfile?.lastName}`
    : order.clientProfile?.companyName || "Kunde";
  
  // Überprüfen, ob der Benutzer bereits eine Bewertung abgegeben hat
  const hasRated = ratingData?.hasRated || false;
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Bewertungen</CardTitle>
        <CardDescription>
          Bewertungen für diesen Auftrag
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Bewertungen des Fahrers anzeigen */}
        {order.assignment?.driverId && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Fahrerbewertungen</h3>
            <RatingDisplay userId={order.assignment.driverId} limit={3} />
          </div>
        )}
        
        {/* Bewertungen des Kunden anzeigen */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Kundenbewertungen</h3>
          <RatingDisplay userId={order.clientId} limit={3} />
        </div>
        
        {/* Bewertungsformular oder Button zum Anzeigen des Formulars */}
        {!hasRated && ratedUserId && (
          <div className="mt-6">
            {showRatingForm ? (
              <RatingForm
                orderId={orderId}
                ratedUserId={ratedUserId}
                ratedUserName={ratedUserName}
                onSuccess={() => setShowRatingForm(false)}
              />
            ) : (
              <Button onClick={() => setShowRatingForm(true)}>
                Bewertung abgeben
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
