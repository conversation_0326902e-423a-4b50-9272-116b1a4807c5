"use client";

import { useQuery } from "@tanstack/react-query";
import { formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";
import { Star } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Rating {
  id: string;
  score: number;
  comment: string | null;
  createdAt: string;
  rater: {
    id: string;
    email: string;
    role: string;
    clientProfile?: {
      companyName: string;
      contactPersonName: string;
    };
    driverProfile?: {
      firstName: string;
      lastName: string;
    };
  };
  order: {
    id: string;
    title: string;
    status: string;
    createdAt: string;
  };
}

interface RatingDisplayProps {
  userId: string;
  className?: string;
  limit?: number;
}

export function RatingDisplay({ userId, className = "", limit = 5 }: RatingDisplayProps) {
  // Bewertungen abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["ratings", userId],
    queryFn: async () => {
      const response = await fetch(`/api/ratings?userId=${userId}&limit=${limit}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Bewertungen");
      }
      return response.json();
    },
  });
  
  // Benutzernamen formatieren
  const formatUserName = (user: Rating["rater"]) => {
    if (user.role === "BUSINESS_CLIENT" && user.clientProfile) {
      return `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`;
    } else if (user.role === "DRIVER" && user.driverProfile) {
      return `${user.driverProfile.firstName} ${user.driverProfile.lastName}`;
    } else if (user.role === "ADMIN") {
      return "Administrator";
    }
    return user.email;
  };
  
  // Avatar-Fallback erstellen
  const createAvatarFallback = (user: Rating["rater"]) => {
    if (user.role === "BUSINESS_CLIENT" && user.clientProfile) {
      return user.clientProfile.companyName.substring(0, 2).toUpperCase();
    } else if (user.role === "DRIVER" && user.driverProfile) {
      return `${user.driverProfile.firstName.charAt(0)}${user.driverProfile.lastName.charAt(0)}`;
    } else if (user.role === "ADMIN") {
      return "AD";
    }
    return user.email.substring(0, 2).toUpperCase();
  };
  
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full mb-4" />
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }
  
  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Bewertungen</CardTitle>
          <CardDescription>Fehler beim Laden der Bewertungen</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  const ratings: Rating[] = data?.ratings || [];
  const averageRating = data?.averageRating || { score: 0, count: 0 };
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Bewertungen
          <div className="flex items-center text-sm font-normal">
            <span className="text-yellow-400 mr-1">{averageRating.score.toFixed(1)}</span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((value) => (
                <Star
                  key={value}
                  className={`h-4 w-4 ${
                    value <= Math.round(averageRating.score)
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-muted-foreground"
                  }`}
                />
              ))}
            </div>
            <span className="ml-1 text-muted-foreground">
              ({averageRating.count} {averageRating.count === 1 ? "Bewertung" : "Bewertungen"})
            </span>
          </div>
        </CardTitle>
        <CardDescription>
          Die neuesten Bewertungen
        </CardDescription>
      </CardHeader>
      <CardContent>
        {ratings.length === 0 ? (
          <p className="text-center text-muted-foreground py-4">
            Noch keine Bewertungen vorhanden
          </p>
        ) : (
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-4">
              {ratings.map((rating) => (
                <div key={rating.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" />
                        <AvatarFallback>{createAvatarFallback(rating.rater)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{formatUserName(rating.rater)}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(rating.createdAt), {
                            addSuffix: true,
                            locale: de,
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((value) => (
                        <Star
                          key={value}
                          className={`h-4 w-4 ${
                            value <= rating.score
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-muted-foreground"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                  {rating.comment && (
                    <p className="mt-2 text-sm">{rating.comment}</p>
                  )}
                  <p className="mt-2 text-xs text-muted-foreground">
                    Auftrag: {rating.order.title || `#${rating.order.id.substring(0, 8)}`}
                  </p>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
