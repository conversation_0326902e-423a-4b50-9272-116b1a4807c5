"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

interface RatingFormProps {
  orderId: string;
  ratedUserId: string;
  ratedUserName: string;
  onSuccess?: () => void;
  className?: string;
}

export function RatingForm({
  orderId,
  ratedUserId,
  ratedUserName,
  onSuccess,
  className = "",
}: RatingFormProps) {
  const [score, setScore] = useState(0);
  const [hoverScore, setHoverScore] = useState(0);
  const [comment, setComment] = useState("");
  const queryClient = useQueryClient();
  
  // Mutation für das Erstellen einer Bewertung
  const createRatingMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/ratings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId,
          ratedUserId,
          score,
          comment,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler beim Erstellen der Bewertung");
      }
      
      return response.json();
    },
    onSuccess: () => {
      toast.success("Bewertung erfolgreich abgegeben");
      queryClient.invalidateQueries({ queryKey: ["ratings", ratedUserId] });
      queryClient.invalidateQueries({ queryKey: ["order", orderId] });
      
      // Formular zurücksetzen
      setScore(0);
      setComment("");
      
      // Callback aufrufen, wenn vorhanden
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });
  
  // Bewertung abgeben
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (score === 0) {
      toast.error("Bitte geben Sie eine Bewertung ab (1-5 Sterne)");
      return;
    }
    
    createRatingMutation.mutate();
  };
  
  return (
    <Card className={className}>
      <form onSubmit={handleSubmit}>
        <CardHeader>
          <CardTitle>Bewertung abgeben</CardTitle>
          <CardDescription>
            Bewerten Sie {ratedUserName} für diesen Auftrag
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-center">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((value) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => setScore(value)}
                  onMouseEnter={() => setHoverScore(value)}
                  onMouseLeave={() => setHoverScore(0)}
                  className="p-1 focus:outline-none"
                >
                  <Star
                    className={`h-8 w-8 ${
                      (hoverScore || score) >= value
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-muted-foreground"
                    } transition-colors`}
                  />
                </button>
              ))}
            </div>
          </div>
          
          <div>
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Kommentar (optional)"
              className="min-h-[100px] resize-none"
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button
            type="submit"
            disabled={score === 0 || createRatingMutation.isPending}
          >
            {createRatingMutation.isPending ? "Wird gesendet..." : "Bewertung abgeben"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
