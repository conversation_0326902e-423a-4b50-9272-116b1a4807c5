"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { GradientButton } from "@/components/ui/gradient-button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { UserButton } from "@/components/auth/user-button";

export function DriverNavigation() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname?.startsWith(path);
  };

  return (
    <div className="border-b bg-white">
      <div className="container flex h-16 items-center justify-between px-4">
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <Link href="/driver/dashboard" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    isActive("/driver/dashboard") &&
                      "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                  )}
                >
                  Dashboard
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/driver/jobs" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    isActive("/driver/jobs") &&
                      "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                  )}
                >
                  Find Jobs
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/driver/my-jobs/active" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    isActive("/driver/my-jobs") &&
                      "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                  )}
                >
                  My Jobs
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/driver/earnings" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    isActive("/driver/earnings") &&
                      "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                  )}
                >
                  Earnings
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <Link href="/driver/profile" legacyBehavior passHref>
                <NavigationMenuLink
                  className={cn(
                    navigationMenuTriggerStyle(),
                    isActive("/driver/profile") &&
                      "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                  )}
                >
                  Profile
                </NavigationMenuLink>
              </Link>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
        <UserButton />
      </div>
    </div>
  );
}
