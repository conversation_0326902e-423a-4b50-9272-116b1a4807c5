"use client";

import Link from "next/link";
import Image from "next/image";
import { GradientButton } from "@/components/ui/gradient-button";
import React from "react";

/**
 * Header-Komponente für die YoungMobility-Plattform.
 * Enthält das Logo und die Hauptnavigation.
 * @returns {React.ReactElement} Die gerenderte Header-Komponente.
 */
export function Header(): React.ReactElement {
  // Loggt den Workflow beim Rendern des Headers
  console.log("Rendering Header component");

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-800 bg-black shadow-sm backdrop-blur-sm">
      <div className="w-full flex h-18 md:h-20 items-center px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 relative">
        {/* Left Side - Logo */}
        <div className="flex-shrink-0 w-auto">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/YoungMobility Logo Navigationsleiste.png"
              alt="YoungMobility Logo"
              width={320}
              height={80}
              className="h-14 md:h-[68px] w-auto"
              priority
            />
          </Link>
        </div>

        {/* Center Navigation - Absolutely centered */}
        <nav className="hidden lg:flex absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="flex items-center space-x-12">
            <Link
              href="/solutions"
              className="text-sm font-medium transition-colors duration-300"
              style={{ color: '#F8E7D1', textDecoration: 'none' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              Lösungen
            </Link>
            <Link
              href="/pricing"
              className="text-sm font-medium transition-colors duration-300"
              style={{ color: '#F8E7D1', textDecoration: 'none' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              Preise
            </Link>
            <Link
              href="/youngmovers"
              className="text-sm font-medium transition-colors duration-300"
              style={{ color: '#F8E7D1', textDecoration: 'none' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              YoungMovers
            </Link>
            <Link
              href="/about"
              className="text-sm font-medium transition-colors duration-300"
              style={{ color: '#F8E7D1', textDecoration: 'none' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              Über Uns
            </Link>
            <Link
              href="/contact"
              className="text-sm font-medium transition-colors duration-300"
              style={{ color: '#F8E7D1', textDecoration: 'none' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#F8E7D1';
                e.currentTarget.style.textDecoration = 'none';
              }}
            >
              Kontakt
            </Link>
          </div>
        </nav>

        {/* Right Side - Login & Register */}
        <div className="ml-auto flex items-center space-x-3">
          <GradientButton
            variant="variant"
            asChild
            className="text-sm py-1 px-3 w-[70px] flex justify-center"
            style={{ color: '#FFFFFF' }}
          >
            <Link href="/login">
              Login
            </Link>
          </GradientButton>
          <GradientButton
            asChild
            className="text-sm py-1 px-2 w-[70px] flex justify-center"
            style={{ color: '#FFFFFF' }}
          >
            <Link href="/register-client">
              Register
            </Link>
          </GradientButton>
        </div>
      </div>
    </header>
  );
}