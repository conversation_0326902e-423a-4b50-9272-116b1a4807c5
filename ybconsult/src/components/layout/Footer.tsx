"use client";

import React from "react";
import Link from "next/link";
import { TwitterIcon, LinkedInIcon, InstagramIcon, FacebookIcon } from "@/components/ui/icons";

/**
 * Footer-Komponente für die YoungMobility-Plattform.
 * Enthält Copyright-Informationen und weitere Links.
 * @returns {React.ReactElement} Die gerenderte Footer-Komponente.
 */
export function Footer(): React.ReactElement {
  // Loggt den Workflow beim Rendern des Footers
  console.log("Rendering Footer component");
  const currentYear = new Date().getFullYear();

  return (
    <footer className="w-full bg-gray-950 border-t border-gray-800">
      <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-7xl mx-auto">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">YoungMobility</h3>
            <p className="text-sm text-gray-300 max-w-xs">
              Innovative Fahrzeugüberführungen für Geschäftskunden und Privatpersonen.
            </p>
          </div>

          <div>
            <h4 className="text-sm font-semibold text-white mb-4">Unternehmen</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/about"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Über uns
                </Link>
              </li>
              <li>
                <Link
                  href="/careers"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Karriere
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Kontakt
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-sm font-semibold text-white mb-4">Leistungen</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/solutions/business"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Für Unternehmen
                </Link>
              </li>
              <li>
                <Link
                  href="/solutions/drivers"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Für Fahrer
                </Link>
              </li>
              <li>
                <Link
                  href="/pricing"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Preise
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-sm font-semibold text-white mb-4">Rechtliches</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/privacy-policy"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Datenschutz
                </Link>
              </li>
              <li>
                <Link
                  href="/terms-of-service"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  AGB
                </Link>
              </li>
              <li>
                <Link
                  href="/imprint"
                  className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  style={{ textDecoration: 'none' }}
                  onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
                  onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                >
                  Impressum
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center max-w-7xl mx-auto">
          <p className="text-sm text-gray-300">
            &copy; {currentYear} YoungMobility GmbH. Alle Rechte vorbehalten.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a
              href="https://twitter.com/youngmobility"
              className="text-gray-400 hover:text-blue-400 transition-colors"
              aria-label="Twitter"
              style={{ textDecoration: 'none' }}
              onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
              onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
            >
              <TwitterIcon size={20} />
            </a>
            <a
              href="https://linkedin.com/company/youngmobility"
              className="text-gray-400 hover:text-blue-400 transition-colors"
              aria-label="LinkedIn"
              style={{ textDecoration: 'none' }}
              onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
              onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
            >
              <LinkedInIcon size={20} />
            </a>
            <a
              href="https://instagram.com/youngmobility"
              className="text-gray-400 hover:text-blue-400 transition-colors"
              aria-label="Instagram"
              style={{ textDecoration: 'none' }}
              onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
              onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
            >
              <InstagramIcon size={20} />
            </a>
            <a
              href="https://facebook.com/youngmobility"
              className="text-gray-400 hover:text-blue-400 transition-colors"
              aria-label="Facebook"
              style={{ textDecoration: 'none' }}
              onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'none'}
              onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
            >
              <FacebookIcon size={20} />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}