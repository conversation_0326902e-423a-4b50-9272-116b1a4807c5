"use client"; // NavigationMenu verwendet Client-Komponenten-Features

import * as React from "react";
import Link from "next/link";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink, // Beachte: Dies ist die Komponente von Shadcn/Radix
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";

/**
 * @function MinimalistNavigation
 * @description Minimalistische Navigationskomponente, die das Shadcn UI NavigationMenu verwendet.
 *              Stellt die Hauptnavigationslinks der Anwendung dar.
 *              Diese Komponente ist als Client Component ('use client') deklariert,
 *              da die interaktiven Elemente von NavigationMenu dies erfordern.
 * @returns {React.ReactElement} Die gerenderte Navigationskomponente.
 */
export function MinimalistNavigation(): React.ReactElement {
  // Loggt den Workflow beim Rendern der Navigation.
  // Gemäß den Projektrichtlinien sollte hier idealerweise ein strukturierter Logger wie Winston verwendet werden,
  // um detailliertere und kontextbezogene Log-Informationen zu erfassen.
  // Beispiel: logger.info('Rendering MinimalistNavigation component', { component: 'MinimalistNavigation' });
  console.log("Rendering MinimalistNavigation component");

  return (
    <NavigationMenu>
      <NavigationMenuList className="flex space-x-1">
        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link
              href="/solutions"
              className={cn(
                navigationMenuTriggerStyle(),
                "bg-transparent font-medium text-sm"
              )}
              style={{ color: '#F8E7D1' }}
            >
              Lösungen
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link
              href="/pricing"
              className={cn(
                navigationMenuTriggerStyle(),
                "bg-transparent font-medium text-sm"
              )}
              style={{ color: '#F8E7D1' }}
            >
              Preise
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link
              href="/youngmovers"
              className={cn(
                navigationMenuTriggerStyle(),
                "bg-transparent font-medium text-sm"
              )}
              style={{ color: '#F8E7D1' }}
            >
              YoungMovers
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link
              href="/about"
              className={cn(
                navigationMenuTriggerStyle(),
                "bg-transparent font-medium text-sm"
              )}
              style={{ color: '#F8E7D1' }}
            >
              Über Uns
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <NavigationMenuLink asChild>
            <Link
              href="/contact"
              className={cn(
                navigationMenuTriggerStyle(),
                "bg-transparent font-medium text-sm"
              )}
              style={{ color: '#F8E7D1' }}
            >
              Kontakt
            </Link>
          </NavigationMenuLink>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}