"use client";

import { useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  readAt: string | null;
  createdAt: string;
  senderId: string;
  receiverId: string;
  sender: {
    id: string;
    email: string;
    role: string;
    clientProfile?: {
      companyName: string;
      contactPersonName: string;
    };
    driverProfile?: {
      firstName: string;
      lastName: string;
    };
  };
  receiver: {
    id: string;
    email: string;
    role: string;
    clientProfile?: {
      companyName: string;
      contactPersonName: string;
    };
    driverProfile?: {
      firstName: string;
      lastName: string;
    };
  };
}

interface MessageListProps {
  orderId: string;
  className?: string;
}

export function MessageList({ orderId, className = "" }: MessageListProps) {
  const { data: session } = useSession();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Nachrichten abrufen
  const { data, isLoading, error } = useQuery({
    queryKey: ["messages", orderId],
    queryFn: async () => {
      const response = await fetch(`/api/messages?orderId=${orderId}`);
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Nachrichten");
      }
      return response.json();
    },
    refetchInterval: 10000, // Alle 10 Sekunden aktualisieren
  });
  
  // Automatisch zum Ende der Nachrichtenliste scrollen
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [data]);
  
  // Benutzernamen formatieren
  const formatUserName = (user: Message["sender"]) => {
    if (user.role === "BUSINESS_CLIENT" && user.clientProfile) {
      return `${user.clientProfile.companyName} (${user.clientProfile.contactPersonName})`;
    } else if (user.role === "DRIVER" && user.driverProfile) {
      return `${user.driverProfile.firstName} ${user.driverProfile.lastName}`;
    } else if (user.role === "ADMIN") {
      return "Administrator";
    }
    return user.email;
  };
  
  // Avatar-Fallback erstellen
  const createAvatarFallback = (user: Message["sender"]) => {
    if (user.role === "BUSINESS_CLIENT" && user.clientProfile) {
      return user.clientProfile.companyName.substring(0, 2).toUpperCase();
    } else if (user.role === "DRIVER" && user.driverProfile) {
      return `${user.driverProfile.firstName.charAt(0)}${user.driverProfile.lastName.charAt(0)}`;
    } else if (user.role === "ADMIN") {
      return "AD";
    }
    return user.email.substring(0, 2).toUpperCase();
  };
  
  if (isLoading) {
    return (
      <div className={`flex flex-col space-y-4 p-4 ${className}`}>
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-12 w-3/4 ml-auto" />
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-12 w-3/4 ml-auto" />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
        <p className="text-red-500">Fehler beim Laden der Nachrichten</p>
      </div>
    );
  }
  
  const messages: Message[] = data?.messages || [];
  
  return (
    <ScrollArea className={`h-[400px] p-4 ${className}`}>
      {messages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-muted-foreground">Noch keine Nachrichten</p>
        </div>
      ) : (
        <div className="flex flex-col space-y-4">
          {messages.map((message) => {
            const isSender = message.senderId === session?.user?.id;
            
            return (
              <div
                key={message.id}
                className={`flex ${isSender ? "justify-end" : "justify-start"}`}
              >
                <div className={`flex ${isSender ? "flex-row-reverse" : "flex-row"} items-start gap-2 max-w-[80%]`}>
                  {!isSender && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="" />
                      <AvatarFallback>{createAvatarFallback(message.sender)}</AvatarFallback>
                    </Avatar>
                  )}
                  
                  <div>
                    <div
                      className={`rounded-lg p-3 ${
                        isSender
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted"
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
                    </div>
                    
                    <div className={`flex mt-1 text-xs text-muted-foreground ${isSender ? "justify-end" : "justify-start"}`}>
                      <span>
                        {formatDistanceToNow(new Date(message.createdAt), {
                          addSuffix: true,
                          locale: de,
                        })}
                      </span>
                      {!isSender && (
                        <>
                          <span className="mx-1">•</span>
                          <span>{formatUserName(message.sender)}</span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  {isSender && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="" />
                      <AvatarFallback>{createAvatarFallback(message.sender)}</AvatarFallback>
                    </Avatar>
                  )}
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      )}
    </ScrollArea>
  );
}
