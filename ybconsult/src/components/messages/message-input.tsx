"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface MessageInputProps {
  orderId: string;
  receiverId: string;
  className?: string;
}

export function MessageInput({ orderId, receiverId, className = "" }: MessageInputProps) {
  const [message, setMessage] = useState("");
  const queryClient = useQueryClient();
  
  // Mutation für das Senden einer Nachricht
  const sendMessageMutation = useMutation({
    mutationFn: async (content: string) => {
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId,
          receiverId,
          content,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Fehler beim Senden der Nachricht");
      }
      
      return response.json();
    },
    onSuccess: () => {
      setMessage("");
      queryClient.invalidateQueries({ queryKey: ["messages", orderId] });
    },
    onError: (error) => {
      toast.error(`Fehler: ${error.message}`);
    },
  });
  
  // Nachricht senden
  const handleSendMessage = () => {
    if (!message.trim()) return;
    
    sendMessageMutation.mutate(message);
  };
  
  // Nachricht bei Enter-Taste senden (Shift+Enter für neue Zeile)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  return (
    <div className={`flex items-end gap-2 ${className}`}>
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Nachricht eingeben..."
        className="min-h-[80px] resize-none"
      />
      <Button
        type="button"
        onClick={handleSendMessage}
        disabled={!message.trim() || sendMessageMutation.isPending}
        size="icon"
      >
        <Send className="h-4 w-4" />
        <span className="sr-only">Senden</span>
      </Button>
    </div>
  );
}
