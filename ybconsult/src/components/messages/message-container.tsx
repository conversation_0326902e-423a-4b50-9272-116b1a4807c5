"use client";

import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageList } from "./message-list";
import { MessageInput } from "./message-input";
import { Skeleton } from "@/components/ui/skeleton";

interface MessageContainerProps {
  orderId: string;
  className?: string;
}

export function MessageContainer({ orderId, className = "" }: MessageContainerProps) {
  const { data: session } = useSession();
  
  // Auftragsdaten abrufen, um den Empfänger zu bestimmen
  const { data: orderData, isLoading, error } = useQuery({
    queryKey: ["order", orderId],
    queryFn: async () => {
      const response = await fetch(`/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error("<PERSON><PERSON> beim Laden der Auftragsdaten");
      }
      return response.json();
    },
  });
  
  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[400px] w-full" />
          <Skeleton className="h-20 w-full mt-4" />
        </CardContent>
      </Card>
    );
  }
  
  if (error || !orderData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Nachrichten</CardTitle>
          <CardDescription>Fehler beim Laden der Auftragsdaten</CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  const order = orderData.order;
  
  // Bestimmen, ob der aktuelle Benutzer der Kunde oder der Fahrer ist
  const isClient = session?.user?.id === order.clientId;
  const isDriver = session?.user?.id === order.assignment?.driverId;
  
  // Empfänger-ID bestimmen (wenn Kunde, dann Fahrer und umgekehrt)
  let receiverId = "";
  if (isClient && order.assignment) {
    receiverId = order.assignment.driverId;
  } else if (isDriver) {
    receiverId = order.clientId;
  }
  
  // Wenn keine Zuweisung vorhanden ist oder der Benutzer weder Kunde noch Fahrer ist
  if (!receiverId) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Nachrichten</CardTitle>
          <CardDescription>
            {!order.assignment
              ? "Diesem Auftrag wurde noch kein Fahrer zugewiesen."
              : "Sie haben keine Berechtigung, auf diese Nachrichten zuzugreifen."}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Nachrichten</CardTitle>
        <CardDescription>
          Kommunikation zwischen Kunde und Fahrer für Auftrag #{order.id.substring(0, 8)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <MessageList orderId={orderId} />
        <MessageInput orderId={orderId} receiverId={receiverId} />
      </CardContent>
    </Card>
  );
}
