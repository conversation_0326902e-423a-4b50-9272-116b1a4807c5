"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { Bell, Check, X, Info, AlertTriangle, CheckCircle, Truck, MessageSquare } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  isRead: boolean;
  readAt: string | null;
  relatedEntityType: string | null;
  relatedEntityId: string | null;
  createdAt: string;
}

export function NotificationCenter() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("unread");

  // Abrufen der Benachrichtigungen
  const { data, isLoading, error } = useQuery({
    queryKey: ["notifications"],
    queryFn: async () => {
      const response = await fetch("/api/notifications");
      if (!response.ok) {
        throw new Error("Fehler beim Laden der Benachrichtigungen");
      }
      return response.json();
    },
    refetchInterval: 60000, // Alle 60 Sekunden aktualisieren
  });

  // Mutation zum Markieren einer Benachrichtigung als gelesen
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: "POST",
      });
      if (!response.ok) {
        throw new Error("Fehler beim Markieren der Benachrichtigung als gelesen");
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
  });

  // Mutation zum Markieren aller Benachrichtigungen als gelesen
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/notifications/read-all", {
        method: "POST",
      });
      if (!response.ok) {
        throw new Error("Fehler beim Markieren aller Benachrichtigungen als gelesen");
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
  });

  // Benachrichtigungen filtern
  const unreadNotifications = data?.filter((notification: Notification) => !notification.isRead) || [];
  const readNotifications = data?.filter((notification: Notification) => notification.isRead) || [];
  const allNotifications = data || [];

  // Icon für den Benachrichtigungstyp auswählen
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "ORDER_CREATED":
      case "ORDER_UPDATED":
      case "ORDER_STATUS_UPDATE":
        return <Truck className="h-5 w-5 text-blue-500" />;
      case "DRIVER_ASSIGNED":
      case "DRIVER_ACCEPTED":
      case "DRIVER_DECLINED":
        return <Truck className="h-5 w-5 text-indigo-500" />;
      case "NEW_BID":
        return <Info className="h-5 w-5 text-green-500" />;
      case "PAYMENT_PROCESSED":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "NEW_MESSAGE":
        return <MessageSquare className="h-5 w-5 text-purple-500" />;
      case "WARNING":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Benachrichtigung als gelesen markieren und ggf. zur entsprechenden Seite navigieren
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsReadMutation.mutate(notification.id);
    }

    // Zur entsprechenden Seite navigieren, falls vorhanden
    if (notification.relatedEntityType && notification.relatedEntityId) {
      switch (notification.relatedEntityType) {
        case "Order":
          router.push(`/client/orders/${notification.relatedEntityId}`);
          break;
        case "Assignment":
          router.push(`/driver/my-jobs/active`);
          break;
        case "Message":
          router.push(`/messages/${notification.relatedEntityId}`);
          break;
        default:
          // Keine Navigation
          break;
      }
    }

    setOpen(false);
  };

  // Formatierung des Zeitstempels
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return formatDistanceToNow(date, { addSuffix: true, locale: de });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadNotifications.length > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white"
              variant="destructive"
            >
              {unreadNotifications.length > 9 ? "9+" : unreadNotifications.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-medium">Benachrichtigungen</h3>
          {unreadNotifications.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => markAllAsReadMutation.mutate()}
              disabled={markAllAsReadMutation.isPending}
              className="text-xs h-8"
            >
              <Check className="h-3.5 w-3.5 mr-1" />
              Alle als gelesen markieren
            </Button>
          )}
        </div>
        <Tabs defaultValue="unread" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full grid grid-cols-3">
            <TabsTrigger value="unread" className="text-xs">
              Ungelesen {unreadNotifications.length > 0 && `(${unreadNotifications.length})`}
            </TabsTrigger>
            <TabsTrigger value="all" className="text-xs">Alle</TabsTrigger>
            <TabsTrigger value="read" className="text-xs">Gelesen</TabsTrigger>
          </TabsList>
          <TabsContent value="unread" className="m-0">
            <ScrollArea className="h-[300px]">
              {isLoading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Benachrichtigungen werden geladen...
                </div>
              ) : unreadNotifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Keine ungelesenen Benachrichtigungen
                </div>
              ) : (
                unreadNotifications.map((notification: Notification) => (
                  <div 
                    key={notification.id}
                    className="p-3 border-b last:border-0 hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-xs text-muted-foreground line-clamp-2">{notification.message}</p>
                        <p className="text-xs text-muted-foreground mt-1">{formatTimestamp(notification.createdAt)}</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </ScrollArea>
          </TabsContent>
          <TabsContent value="all" className="m-0">
            <ScrollArea className="h-[300px]">
              {isLoading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Benachrichtigungen werden geladen...
                </div>
              ) : allNotifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Keine Benachrichtigungen
                </div>
              ) : (
                allNotifications.map((notification: Notification) => (
                  <div 
                    key={notification.id}
                    className={`p-3 border-b last:border-0 hover:bg-muted/50 cursor-pointer transition-colors ${notification.isRead ? 'opacity-70' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-xs text-muted-foreground line-clamp-2">{notification.message}</p>
                        <p className="text-xs text-muted-foreground mt-1">{formatTimestamp(notification.createdAt)}</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </ScrollArea>
          </TabsContent>
          <TabsContent value="read" className="m-0">
            <ScrollArea className="h-[300px]">
              {isLoading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Benachrichtigungen werden geladen...
                </div>
              ) : readNotifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Keine gelesenen Benachrichtigungen
                </div>
              ) : (
                readNotifications.map((notification: Notification) => (
                  <div 
                    key={notification.id}
                    className="p-3 border-b last:border-0 hover:bg-muted/50 cursor-pointer transition-colors opacity-70"
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{notification.title}</p>
                        <p className="text-xs text-muted-foreground line-clamp-2">{notification.message}</p>
                        <p className="text-xs text-muted-foreground mt-1">{formatTimestamp(notification.createdAt)}</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}
