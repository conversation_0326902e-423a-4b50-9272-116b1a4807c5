"use client";

import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  ConsentCategory,
  ConsentStatus,
  DEFAULT_CONSENT_DATA,
  loadConsent,
  saveConsent,
  updateAllConsent,
} from "@/utils/gdpr";

/**
 * GDPR Consent Manager Component
 * Teil der Phase 8: Security Hardening & GDPR Compliance Audit (YM-803)
 */
export function ConsentManager() {
  const [isOpen, setIsOpen] = useState(false);
  const [consentData, setConsentData] = useState(DEFAULT_CONSENT_DATA);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Lade gespeicherte Einwilligungen
    const savedConsent = loadConsent();
    
    // <PERSON><PERSON><PERSON> Banner, wenn keine Einwilligungen vorhanden sind
    if (!savedConsent) {
      setIsOpen(true);
    } else {
      setConsentData(savedConsent);
    }
  }, []);

  const handleAcceptAll = () => {
    const updatedConsent = {
      ...consentData,
      [ConsentCategory.FUNCTIONAL]: ConsentStatus.GRANTED,
      [ConsentCategory.ANALYTICS]: ConsentStatus.GRANTED,
      [ConsentCategory.MARKETING]: ConsentStatus.GRANTED,
      [ConsentCategory.PERSONALIZATION]: ConsentStatus.GRANTED,
      timestamp: Date.now(),
    };
    
    setConsentData(updatedConsent);
    saveConsent(updatedConsent);
    setIsOpen(false);
  };

  const handleRejectAll = () => {
    const updatedConsent = {
      ...consentData,
      [ConsentCategory.FUNCTIONAL]: ConsentStatus.DENIED,
      [ConsentCategory.ANALYTICS]: ConsentStatus.DENIED,
      [ConsentCategory.MARKETING]: ConsentStatus.DENIED,
      [ConsentCategory.PERSONALIZATION]: ConsentStatus.DENIED,
      timestamp: Date.now(),
    };
    
    setConsentData(updatedConsent);
    saveConsent(updatedConsent);
    setIsOpen(false);
  };

  const handleSavePreferences = () => {
    saveConsent(consentData);
    setIsOpen(false);
  };

  const handleToggleConsent = (category: ConsentCategory) => {
    if (category === ConsentCategory.ESSENTIAL) return;
    
    setConsentData(prev => ({
      ...prev,
      [category]:
        prev[category] === ConsentStatus.GRANTED
          ? ConsentStatus.DENIED
          : ConsentStatus.GRANTED,
    }));
  };

  const handleOpenConsentManager = () => {
    setIsOpen(true);
  };

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white dark:bg-gray-800 shadow-lg border-t border-gray-200 dark:border-gray-700"
          >
            <div className="container mx-auto">
              <div className="flex flex-col space-y-4">
                <div>
                  <h2 className="text-xl font-bold text-quechua-dark-green">Cookie-Einstellungen</h2>
                  <p className="mt-2">
                    Wir verwenden Cookies und ähnliche Technologien, um Ihnen ein optimales Nutzungserlebnis zu bieten und unsere Dienste zu verbessern. 
                    Einige Cookies sind technisch notwendig, während andere uns helfen, Ihre Erfahrung zu personalisieren.
                  </p>
                </div>

                {showDetails && (
                  <div className="space-y-4 border-t border-b py-4 border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Notwendige Cookies</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Diese Cookies sind für die Funktionalität der Website erforderlich und können nicht deaktiviert werden.
                        </p>
                      </div>
                      <Switch
                        checked={true}
                        disabled
                        id="essential-cookies"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Funktionale Cookies</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung.
                        </p>
                      </div>
                      <Switch
                        checked={consentData[ConsentCategory.FUNCTIONAL] === ConsentStatus.GRANTED}
                        onCheckedChange={() => handleToggleConsent(ConsentCategory.FUNCTIONAL)}
                        id="functional-cookies"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Analyse-Cookies</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Diese Cookies helfen uns zu verstehen, wie Besucher mit unserer Website interagieren.
                        </p>
                      </div>
                      <Switch
                        checked={consentData[ConsentCategory.ANALYTICS] === ConsentStatus.GRANTED}
                        onCheckedChange={() => handleToggleConsent(ConsentCategory.ANALYTICS)}
                        id="analytics-cookies"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Marketing-Cookies</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Diese Cookies werden verwendet, um Werbung relevanter für Sie zu gestalten.
                        </p>
                      </div>
                      <Switch
                        checked={consentData[ConsentCategory.MARKETING] === ConsentStatus.GRANTED}
                        onCheckedChange={() => handleToggleConsent(ConsentCategory.MARKETING)}
                        id="marketing-cookies"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">Personalisierungs-Cookies</h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Diese Cookies ermöglichen es uns, Ihnen personalisierte Inhalte anzuzeigen.
                        </p>
                      </div>
                      <Switch
                        checked={consentData[ConsentCategory.PERSONALIZATION] === ConsentStatus.GRANTED}
                        onCheckedChange={() => handleToggleConsent(ConsentCategory.PERSONALIZATION)}
                        id="personalization-cookies"
                      />
                    </div>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row justify-between gap-2">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowDetails(!showDetails)}
                      className="border-quechua-light-green text-quechua-dark-green"
                    >
                      {showDetails ? "Details ausblenden" : "Details anzeigen"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleRejectAll}
                      className="border-quechua-light-green text-quechua-dark-green"
                    >
                      Alle ablehnen
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    {showDetails && (
                      <Button
                        variant="outline"
                        onClick={handleSavePreferences}
                        className="border-quechua-light-green text-quechua-dark-green"
                      >
                        Auswahl speichern
                      </Button>
                    )}
                    <GradientButton onClick={handleAcceptAll}>
                      Alle akzeptieren
                    </GradientButton>
                  </div>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Durch die Nutzung dieser Website stimmen Sie unserer{" "}
                  <a href="/privacy-policy" className="underline">
                    Datenschutzerklärung
                  </a>{" "}
                  zu. Sie können Ihre Cookie-Einstellungen jederzeit ändern.
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Cookie-Einstellungen Button */}
      {!isOpen && (
        <button
          onClick={handleOpenConsentManager}
          className="fixed bottom-6 left-6 z-40 p-2 bg-quechua-light-green text-quechua-dark-green rounded-full shadow-lg hover:bg-quechua-medium-green transition-colors"
          aria-label="Cookie-Einstellungen öffnen"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5" />
            <path d="M8.5 8.5v.01" />
            <path d="M16 15.5v.01" />
            <path d="M12 12v.01" />
            <path d="M11 17v.01" />
            <path d="M7 14v.01" />
          </svg>
        </button>
      )}
    </>
  );
}
