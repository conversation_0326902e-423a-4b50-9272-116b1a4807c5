"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { detectBrowser } from "@/utils/browser-compatibility";

// Feedback-Schema mit Zod
const feedbackSchema = z.object({
  rating: z.number().min(1).max(5),
  category: z.enum(["ui", "ux", "performance", "bug", "feature", "other"]),
  description: z.string().min(10, "Bitte geben Sie mindestens 10 Zeichen ein."),
  browserInfo: z.string().optional(),
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein.").optional(),
});

type FeedbackFormData = z.infer<typeof feedbackSchema>;

interface UATFeedbackFormProps {
  /** Ob das Feedback-Formular als Floating-Button angezeigt werden soll */
  floating?: boolean;
  /** Callback, der aufgerufen wird, wenn Feedback abgesendet wurde */
  onFeedbackSubmitted?: (feedback: FeedbackFormData) => void;
  /** Optionale Kategorie, die vorausgewählt sein soll */
  defaultCategory?: "ui" | "ux" | "performance" | "bug" | "feature" | "other";
}

/**
 * Komponente für User Acceptance Testing (UAT) Feedback
 * Teil der Phase 8: UI/UX Polish & Quechua Design Consistency Review (YM-801)
 */
export function UATFeedbackForm({
  floating = true,
  onFeedbackSubmitted,
  defaultCategory = "ui"
}: UATFeedbackFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const { register, handleSubmit, formState: { errors }, reset, setValue } = useForm<FeedbackFormData>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      rating: 0,
      category: defaultCategory,
      description: "",
      browserInfo: JSON.stringify(detectBrowser()),
    }
  });

  const toggleForm = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setIsSubmitted(false);
    }
  };

  const handleRatingClick = (rating: number) => {
    setSelectedRating(rating);
    setValue("rating", rating);
  };

  const onSubmit = async (data: FeedbackFormData) => {
    setIsSubmitting(true);

    try {
      // Hier würde normalerweise ein API-Aufruf stattfinden
      console.log("Feedback submitted:", data);

      // Simuliere API-Aufruf
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(data);
      }

      setIsSubmitted(true);
      reset();
      setSelectedRating(0);

      // Schließe das Formular nach 3 Sekunden
      setTimeout(() => {
        if (floating) {
          setIsOpen(false);
        }
      }, 3000);
    } catch (error) {
      console.error("Error submitting feedback:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3 } }
  };

  const buttonVariants = {
    hidden: { scale: 0.9, opacity: 0 },
    visible: { scale: 1, opacity: 1, transition: { duration: 0.2 } }
  };

  if (floating) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={formVariants}
              className="quechua-card p-6 mb-4 w-80 md:w-96 shadow-lg"
            >
              {!isSubmitted ? (
                <form onSubmit={handleSubmit(onSubmit)}>
                  <h3 className="text-lg font-bold mb-4 text-quechua-dark-green">UAT Feedback</h3>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Bewertung</label>
                    <div className="flex space-x-2">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          onClick={() => handleRatingClick(rating)}
                          className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                            selectedRating >= rating
                              ? 'bg-quechua-medium-green text-white'
                              : 'bg-gray-100 text-gray-400'
                          }`}
                        >
                          {rating}
                        </button>
                      ))}
                    </div>
                    {errors.rating && (
                      <p className="text-red-500 text-xs mt-1">Bitte wählen Sie eine Bewertung</p>
                    )}
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Kategorie</label>
                    <select
                      {...register("category")}
                      className="quechua-input w-full"
                    >
                      <option value="ui">UI Design</option>
                      <option value="ux">Benutzerfreundlichkeit</option>
                      <option value="performance">Performance</option>
                      <option value="bug">Fehler/Bug</option>
                      <option value="feature">Feature-Vorschlag</option>
                      <option value="other">Sonstiges</option>
                    </select>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Beschreibung</label>
                    <Textarea
                      {...register("description")}
                      className="quechua-input"
                      placeholder="Beschreiben Sie Ihr Feedback..."
                      rows={4}
                    />
                    {errors.description && (
                      <p className="text-red-500 text-xs mt-1">{errors.description.message}</p>
                    )}
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">E-Mail (optional)</label>
                    <input
                      type="email"
                      {...register("email")}
                      className="quechua-input w-full"
                      placeholder="Ihre E-Mail für Rückfragen"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
                    )}
                  </div>

                  <div className="flex justify-between">
                    <GradientButton
                      type="button"
                      variant="variant"
                      onClick={toggleForm}
                      className="border-quechua-light-green text-quechua-dark-green"
                    >
                      Abbrechen
                    </GradientButton>
                    <GradientButton
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Wird gesendet..." : "Feedback senden"}
                    </GradientButton>
                  </div>
                </form>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-6"
                >
                  <div className="text-quechua-dark-green text-5xl mb-4">✓</div>
                  <h3 className="text-lg font-bold mb-2 text-quechua-dark-green">Vielen Dank!</h3>
                  <p className="text-gray-600 mb-4">Ihr Feedback hilft uns, die Plattform zu verbessern.</p>
                  <GradientButton
                    onClick={toggleForm}
                    variant="variant"
                    className="border-quechua-light-green text-quechua-dark-green"
                  >
                    Schließen
                  </GradientButton>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        <motion.button
          initial="visible"
          animate="visible"
          variants={buttonVariants}
          onClick={toggleForm}
          className="quechua-gradient-button rounded-full w-14 h-14 flex items-center justify-center shadow-lg"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isOpen ? "×" : "?"}
        </motion.button>
      </div>
    );
  }

  // Nicht-floating Version (eingebettet)
  return (
    <div className="quechua-card p-6 w-full max-w-lg mx-auto">
      {!isSubmitted ? (
        <form onSubmit={handleSubmit(onSubmit)}>
          <h3 className="text-xl font-bold mb-6 text-quechua-dark-green">UAT Feedback</h3>

          {/* Gleicher Inhalt wie oben, nur ohne Animation und Floating-Button */}
          {/* ... */}

          <div className="flex justify-end">
            <GradientButton
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Wird gesendet..." : "Feedback senden"}
            </GradientButton>
          </div>
        </form>
      ) : (
        <div className="text-center py-8">
          <div className="text-quechua-dark-green text-6xl mb-4">✓</div>
          <h3 className="text-xl font-bold mb-2 text-quechua-dark-green">Vielen Dank!</h3>
          <p className="text-gray-600 mb-6">Ihr Feedback hilft uns, die Plattform zu verbessern.</p>
          <Button
            onClick={() => setIsSubmitted(false)}
            variant="outline"
            className="border-quechua-light-green text-quechua-dark-green"
          >
            Neues Feedback geben
          </Button>
        </div>
      )}
    </div>
  );
}
