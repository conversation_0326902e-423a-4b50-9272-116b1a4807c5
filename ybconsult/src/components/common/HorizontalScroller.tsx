"use client";

import React, { useRef, useEffect, useState } from "react";
import { motion } from "framer-motion";

interface HorizontalScrollerProps {
  children: React.ReactNode;
  /** Geschwindigkeit des Scrollens, Standard ist 1. Höhere Werte = schneller. */
  speed?: number;
  /** Optionaler Titel für den Scroller */
  title?: string;
  /** Ob der Scroll-Hinweis angezeigt werden soll */
  showScrollHint?: boolean;
}

/**
 * Eine verbesserte Komponente, die horizontales Scrollen für ihre Kinder ermöglicht,
 * mit Mausrad-Interaktion und Quechua-inspiriertem Design.
 * @param {HorizontalScrollerProps} props - Die Props für die Komponente.
 * @returns {React.ReactElement} Die gerenderte HorizontalScroller-Komponente.
 */
export function HorizontalScroller({
  children,
  speed = 1,
  title,
  showScrollHint = true
}: HorizontalScrollerProps): React.ReactElement {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Mausrad-Steuerung für horizontales Scrollen
  useEffect(() => {
    const el = scrollRef.current;
    if (el) {
      // Berechne den maximalen Scroll-Wert
      setMaxScroll(el.scrollWidth - el.clientWidth);

      const onWheel = (e: WheelEvent) => {
        if (e.deltaY === 0) return;
        // Verhindert vertikales Scrollen der Seite, während horizontal gescrollt wird
        e.preventDefault();

        const newScrollLeft = el.scrollLeft + e.deltaY * speed;
        el.scrollTo({
          left: newScrollLeft,
          behavior: "smooth",
        });

        setIsScrolling(true);
        setScrollPosition(newScrollLeft);

        // Setze isScrolling nach einer kurzen Verzögerung zurück
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        scrollTimeoutRef.current = setTimeout(() => {
          setIsScrolling(false);
        }, 150);
      };

      const onScroll = () => {
        setScrollPosition(el.scrollLeft);
      };

      el.addEventListener("wheel", onWheel);
      el.addEventListener("scroll", onScroll);

      return () => {
        el.removeEventListener("wheel", onWheel);
        el.removeEventListener("scroll", onScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [speed, children]);

  // Berechne die Scroll-Position in Prozent
  const scrollPercentage = maxScroll > 0 ? (scrollPosition / maxScroll) * 100 : 0;

  return (
    <div className="quechua-horizontal-scroller">
      {/* Titel und Scroll-Hinweis */}
      <div className="flex justify-between items-center mb-4">
        {title && (
          <h3 className="text-quechua-dark-green font-playfair">{title}</h3>
        )}

        {showScrollHint && (
          <motion.div
            className="text-sm text-gray-500 flex items-center gap-2"
            initial={{ opacity: 0.7 }}
            animate={{
              opacity: isScrolling ? 1 : 0.7,
              x: isScrolling ? [0, 5, 0, -5, 0] : 0
            }}
            transition={{ duration: 0.5 }}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Horizontal scrollen</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.div>
        )}
      </div>

      {/* Scroll-Container mit Quechua-inspirierten Stilelementen */}
      <motion.div
        ref={scrollRef}
        className="quechua-horizontal-scroller-track"
        drag="x"
        dragConstraints={{ left: -maxScroll, right: 0 }}
        dragElastic={0.2}
        dragTransition={{ bounceStiffness: 300, bounceDamping: 20 }}
        whileTap={{ cursor: "grabbing" }}
        onDragStart={() => setIsScrolling(true)}
        onDragEnd={() => setIsScrolling(false)}
      >
        {children}
      </motion.div>

      {/* Scroll-Indikator im Quechua-Stil */}
      <div className="quechua-horizontal-scroller-indicator">
        <motion.div
          className="quechua-horizontal-scroller-dot quechua-horizontal-scroller-dot-active"
          style={{ width: `${Math.max(12, 60 * (scrollPercentage / 100))}px` }}
        />
        <motion.div
          className="quechua-horizontal-scroller-dot quechua-horizontal-scroller-dot-inactive"
          style={{ width: `${Math.max(3, 30 * (1 - scrollPercentage / 100))}px` }}
        />
        <motion.div
          className="quechua-horizontal-scroller-dot quechua-horizontal-scroller-dot-inactive"
          style={{ width: `${Math.max(3, 15 * (1 - scrollPercentage / 100))}px` }}
        />
      </div>
    </div>
  );
}