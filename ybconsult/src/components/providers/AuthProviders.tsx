// /Users/<USER>/Coding/ybconsulting/ybconsult/src/components/providers/AuthProviders.tsx
"use client";

import { SessionProvider } from "next-auth/react";
import React from "react";

/**
 * @interface AuthProvidersProps
 * @description Props for the AuthProviders component.
 * @property {React.ReactNode} children - The child components to be wrapped by the SessionProvider.
 * @property {any} [session] - Optional session object to initialize the SessionProvider.
 */
interface AuthProvidersProps {
  children: React.ReactNode;
  session?: any; // You might want to type this more strictly based on your session structure
}

/**
 * AuthProviders component.
 * Wraps its children with NextAuth's SessionProvider to make session data available throughout the app.
 * @param {AuthProvidersProps} props - The props for the component.
 * @returns {JSX.Element} The SessionProvider wrapping the children.
 */
export default function AuthProviders({ children, session }: AuthProvidersProps): JSX.Element {
  console.log("AuthProviders: Rendering with session (client-side)", session);
  return <SessionProvider session={session}>{children}</SessionProvider>;
}