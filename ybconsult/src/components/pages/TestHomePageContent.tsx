"use client";

import React, { useEffect, useRef, useState } from 'react';
import { Suspense, lazy } from 'react';
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { GradientButton } from "@/components/ui/gradient-button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckIcon,
  ClockIcon,
  ShieldIcon,
  CameraIcon,
  ArrowRightIcon,
  StarIcon,
  TruckIcon,
  UsersIcon,
  MapPinIcon,
  PhoneIcon,
  MailIcon
} from "@/components/ui/icons";

const Spline = lazy(() => import('@splinetool/react-spline'));

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Feature data
const features = [
  {
    icon: <TruckIcon className="w-8 h-8 text-primary" />,
    title: "Schnelle Überführung",
    description: "Professionelle Fahrzeugüberführung in Rekordzeit mit unserem erfahrenen Fahrernetzwerk.",
    badge: "Neu"
  },
  {
    icon: <ShieldIcon className="w-8 h-8 text-primary" />,
    title: "100% Sicher",
    description: "Vollversicherte Transporte mit GPS-Tracking und regelmäßigen Statusupdates.",
    badge: "Garantiert"
  },
  {
    icon: <UsersIcon className="w-8 h-8 text-primary" />,
    title: "Geprüfte Fahrer",
    description: "Alle Fahrer durchlaufen ein strenges Auswahlverfahren und werden kontinuierlich bewertet.",
    badge: "Qualität"
  },
  {
    icon: <ClockIcon className="w-8 h-8 text-primary" />,
    title: "24/7 Support",
    description: "Unser Kundenservice steht Ihnen rund um die Uhr für alle Fragen zur Verfügung.",
    badge: "Service"
  }
];

// Statistics data
const stats = [
  { number: "10,000+", label: "Erfolgreiche Überführungen" },
  { number: "500+", label: "Aktive Fahrer" },
  { number: "98%", label: "Kundenzufriedenheit" },
  { number: "24h", label: "Durchschnittliche Lieferzeit" }
];

// Spline Background Component
function HeroSplineBackground() {
  return (
    <div style={{
      position: 'relative',
      width: '100%',
      height: '100vh',
      pointerEvents: 'auto',
      overflow: 'hidden',
    }}>
      <Suspense fallback={
        <div className="w-full h-full bg-gradient-to-br from-primary/20 via-accent/10 to-secondary/20 flex items-center justify-center">
          <div className="text-white text-lg">Loading 3D Scene...</div>
        </div>
      }>
        <Spline
          style={{
            width: '100%',
            height: '100vh',
            pointerEvents: 'auto',
          }}
          scene="https://prod.spline.design/us3ALejTXl6usHZ7/scene.splinecode"
        />
      </Suspense>
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100vh',
          background: `
            linear-gradient(to right, rgba(0, 0, 0, 0.8), transparent 30%, transparent 70%, rgba(0, 0, 0, 0.8)),
            linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.9))
          `,
          pointerEvents: 'none',
        }}
      />
    </div>
  );
}

// YoungMobility Hero Content
function YoungMobilityHeroContent() {
  return (
    <div className="text-left text-white max-w-5xl">
      <div className="space-y-6">
        <Badge variant="outline" className="w-fit bg-gray-900/60 border-gray-600/50 text-white backdrop-blur-sm">
          🚀 Revolutionäre Fahrzeuglogistik
        </Badge>

        <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold mb-4 leading-tight tracking-wide">
          Die Zukunft der{" "}
          <br className="sm:hidden" />
          <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
            Fahrzeugüberführungen
          </span>
        </h1>

        <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-gray-200 max-w-2xl">
          Erleben Sie modernste Technologie, nahtlose Prozesse und außergewöhnlichen Service
          in unserem revolutionären Fahrzeugüberführungssystem. Professionell, sicher und effizient.
        </p>

        <div className="flex pointer-events-auto flex-col sm:flex-row items-start space-y-3 sm:space-y-0 sm:space-x-4">
          <GradientButton asChild className="w-full sm:w-auto">
            <Link href="/register-client">
              Jetzt starten
              <ArrowRightIcon className="ml-2 w-4 h-4" />
            </Link>
          </GradientButton>

          <GradientButton variant="outline" asChild className="w-full sm:w-auto">
            <Link href="/about">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
              Demo ansehen
            </Link>
          </GradientButton>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-8 pointer-events-none">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="text-center bg-gray-900/60 backdrop-blur-sm rounded-lg p-3 border border-gray-600/30 hover:border-gray-500/50 transition-colors"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <div className="text-xl md:text-2xl font-bold text-white">
                {stat.number}
              </div>
              <div className="text-xs md:text-sm text-gray-300">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Screenshot Section Component
function ScreenshotSection({ screenshotRef }: { screenshotRef: React.RefObject<HTMLDivElement | null> }) {
  return (
    <section className="relative z-10 w-full bg-black py-8 md:py-12">
      <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16">
        <div ref={screenshotRef} className="bg-black rounded-xl overflow-hidden shadow-2xl border border-gray-700/50 w-full max-w-7xl mx-auto">
          <div>
            <Image
              src="/YoungMobility Logo Car.png"
              alt="YoungMobility Dashboard"
              width={1920}
              height={1080}
              className="w-full h-auto block rounded-lg mx-auto"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  );
}

export function TestHomePageContent() {
  const screenshotRef = useRef<HTMLDivElement>(null);
  const heroContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (screenshotRef.current && heroContentRef.current) {
        requestAnimationFrame(() => {
          const scrollPosition = window.pageYOffset;
          if (screenshotRef.current) {
            screenshotRef.current.style.transform = `translateY(-${scrollPosition * 0.5}px)`;
          }

          const maxScroll = 400;
          const opacity = 1 - Math.min(scrollPosition / maxScroll, 1);
          if (heroContentRef.current) {
            heroContentRef.current.style.opacity = opacity.toString();
          }
        });
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-black text-white">
      {/* New Spline Hero Section */}
      <div className="relative">
        <div className="relative min-h-screen">
          <div className="absolute inset-0 z-0 pointer-events-auto">
            <HeroSplineBackground />
          </div>

          <div ref={heroContentRef} style={{
            position: 'absolute', top: 0, left: 0, width: '100%', height: '100vh',
            display: 'flex', justifyContent: 'flex-start', alignItems: 'center', zIndex: 10, pointerEvents: 'none'
          }}>
            <div className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 pt-16 sm:pt-24 md:pt-32">
              <YoungMobilityHeroContent />
            </div>
          </div>
        </div>

        <div className="bg-black relative z-10" style={{ marginTop: '-15vh' }}>
          <ScreenshotSection screenshotRef={screenshotRef} />
        </div>
      </div>
