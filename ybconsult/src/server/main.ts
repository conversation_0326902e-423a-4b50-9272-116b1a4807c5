// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
// import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'; // Für <PERSON> (später)

/**
 * Bootstraps die NestJS-Anwendung.
 * Initialisiert die Anwendung, setzt globale Konfigurationen wie API-Präfix und Validierungs-Pipes
 * und startet den HTTP-Server.
 */
async function bootstrap() {
  // Erstellt eine Instanz der NestJS-Anwendung mit dem AppModule als Wurzelmodul.
  const app = await NestFactory.create(AppModule);

  // Winston Logger als Standardlogger setzen (später zu implementieren)
  // app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  // console.log('main.ts: <PERSON> logger (placeholder) would be set here.');

  // Setzt ein globales Präfix für alle Routen in der Anwendung (z.B. /api).
  // Dadurch werden alle Endpunkte unter /api/... erreichbar.
  // Beispiel: Ein Controller mit @Controller('auth') und einer Methode @Post('register')
  // wird unter /api/auth/register erreichbar sein.
  app.setGlobalPrefix('api');
  console.log('main.ts: Global API prefix set to /api.');

  // Aktiviert eine globale ValidationPipe.
  // Diese Pipe verwendet class-validator und class-transformer, um eingehende Anfragen
  // automatisch anhand der DTOs (Data Transfer Objects) zu validieren.
  // - whitelist: Entfernt alle Eigenschaften, die nicht im DTO definiert sind.
  // - forbidNonWhitelisted: Wirft einen Fehler, wenn nicht-whitelisted Eigenschaften vorhanden sind.
  // - transform: Versucht, eingehende Daten in den DTO-Typ umzuwandeln (z.B. String zu Number).
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true, // Erlaubt implizite Typumwandlungen
      },
    }),
  );
  console.log('main.ts: Global ValidationPipe enabled.');

  // Port für den Server, standardmäßig 3001 oder aus Umgebungsvariablen.
  const port = process.env.NEST_PORT || 3001;
  
  // Startet den HTTP-Server und lauscht auf dem definierten Port.
  await app.listen(port);
  console.log(`main.ts: NestJS application successfully started. Listening on port ${port}.`);
  console.log(`main.ts: API available at http://localhost:${port}/api`);
}

// Ruft die Bootstrap-Funktion auf, um die Anwendung zu starten.
bootstrap().catch(err => {
  // Loggt kritische Fehler beim Start der Anwendung.
  console.error('main.ts: Critical error during application bootstrap:', err);
  process.exit(1); // Beendet den Prozess bei einem kritischen Fehler
});