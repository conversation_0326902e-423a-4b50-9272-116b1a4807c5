// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/app.controller.ts
import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

/**
 * @class AppController
 * Der Hauptcontroller der Anwendung.
 * Kann für einfache Routen wie Health-Checks oder eine Willkommensnachricht verwendet werden.
 */
@Controller()
export class AppController {
  /**
   * <PERSON>nstruk<PERSON>, der den AppService injiziert.
   * @param {AppService} appService - Der Hauptdienst der Anwendung.
   */
  constructor(private readonly appService: AppService) {}

  /**
   * Definiert eine GET-Route auf dem Wurzelpfad (/).
   * @returns {string} Eine Willkommensnachricht vom AppService.
   */
  @Get()
  getHello(): string {
    // Log: Anfrage an getHello erhalten
    console.log('AppController: Received request for getHello');
    return this.appService.getHello();
  }
}