// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/user/user.controller.ts
import { Controller, Post, Body, ValidationPipe, UsePipes, HttpCode, HttpStatus } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { User } from '@prisma/client';

/**
 * @class UserController
 * Definiert die API-Endpunkte für Benutzeroperationen, insbesondere die Registrierung.
 */
@Controller('user') // Basis-Pfad für diesen Controller, z.B. /api/user
export class UserController {
  /**
   * Konstruktor, der den UserService injiziert.
   * @param {UserService} userService - Der Dienst für Benutzerlogik.
   */
  constructor(private readonly userService: UserService) {}

  /**
   * Endpunkt für die Benutzerregistrierung.
   * Nimmt E-Mail und Passwort entgegen, validiert sie und erstellt einen neuen Benutzer.
   * Der Pfad wird zu `/api/register` durch die Kombination mit einem globalen API-Präfix und der Pfaddefinition im AuthController (falls vorhanden) oder direkt hier.
   * Für eine dedizierte `/api/register` Route, wäre es besser, dies in einem `AuthController` zu platzieren oder den Controller-Pfad anzupassen.
   * Hier gehen wir davon aus, dass ein globales Präfix `/api` existiert und dieser Controller unter `/api/user/register` erreichbar wäre.
   * Um genau `/api/register` zu treffen, müsste der Controller-Pfad leer sein und die Post-Methode 'register' oder der Controller `auth` heißen.
   * Wir passen dies an, um `/auth/register` zu entsprechen, was gängiger ist.
   *
   * @Post('register') // Definiert diesen Endpunkt als POST /auth/register
   * @HttpCode(HttpStatus.CREATED) // Setzt den HTTP-Statuscode für erfolgreiche Erstellung auf 201
   * @UsePipes(new ValidationPipe({ transform: true, whitelist: true })) // Aktiviert die automatische Validierung von DTOs
   * @param {CreateUserDto} createUserDto - Das DTO mit den Benutzerdaten.
   * @returns {Promise<Omit<User, 'password'>>} Das erstellte Benutzerobjekt ohne Passwort.
   */
  @Post('register') // Wird zu /api/user/register, wenn globales Prefix /api ist
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  async register(@Body() createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    // Log: Anfrage zum Registrieren eines Benutzers erhalten
    console.log(`UserController: Received request to register user: ${createUserDto.email}`);
    const user = await this.userService.createUser(createUserDto);
    // Log: Benutzer erfolgreich registriert
    console.log(`UserController: User ${user.email} registered successfully.`);
    return user;
  }
}