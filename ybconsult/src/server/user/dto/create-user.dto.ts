// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/user/dto/create-user.dto.ts
import { IsEmail, IsNotEmpty, <PERSON><PERSON>ength, MaxLength, Matches } from 'class-validator';

/**
 * @class CreateUserDto
 * Data Transfer Object für die Erstellung eines neuen Benutzers.
 * Enthält Validierungsregeln für die Eingabefelder E-Mail und Passwort.
 */
export class CreateUserDto {
  /**
   * Die E-Mail-Adresse des Benutzers.
   * Muss ein gültiges E-Mail-Format haben und darf nicht leer sein.
   * @example '<EMAIL>'
   */
  @IsEmail({}, { message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein.' })
  @IsNotEmpty({ message: 'E-Mail darf nicht leer sein.' })
  email: string;

  /**
   * Das Passwort des Benutzers.
   * Muss mindestens 8 Zeichen und maximal 100 Zeichen lang sein.
   * Muss mindestens einen Großbuchstaben, einen Kleinbuchstaben, eine Zahl und ein Sonderzeichen enthalten.
   * @example 'Password123!'
   */
  @IsNotEmpty({ message: 'Passwort darf nicht leer sein.' })
  @MinLength(8, { message: 'Das Passwort muss mindestens 8 Zeichen lang sein.' })
  @MaxLength(100, { message: 'Das Passwort darf maximal 100 Zeichen lang sein.' })
  // Beispiel für eine Passwort-Komplexitätsregel (optional, aber empfohlen)
  // @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
  //   message: 'Das Passwort muss Groß- und Kleinbuchstaben, Zahlen und Sonderzeichen enthalten.'
  // })
  password: string;
}