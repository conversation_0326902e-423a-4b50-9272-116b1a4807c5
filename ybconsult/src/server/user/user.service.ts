// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/user/user.service.ts
import { Injectable, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import * as bcrypt from 'bcrypt';
import { PrismaClient, User } from '@prisma/client'; // Import User type and PrismaClient from Prisma

/**
 * @class UserService
 * Verantwortlich für die Geschäftslogik im Zusammenhang mit Benutzern.
 * Beinhaltet Methoden zur Erstellung von Benutzern und zur Passwortverschlüsselung.
 */
@Injectable()
export class UserService {
  /**
   * Die Anzahl der Salt-Runden für bcrypt.
   * @private
   * @readonly
   */
  private readonly saltRounds = 10;

  /**
   * Konstruktor, der den PrismaService injiziert.
   * @param {PrismaService} prisma - Der Dienst für Datenbankinteraktionen.
   */
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Erstellt einen neuen Benutzer in der Datenbank.
   * Überprüft zuerst, ob bereits ein Benutzer mit der angegebenen E-Mail-Adresse existiert.
   * Verschlüsselt das Passwort des Benutzers vor dem Speichern.
   *
   * @param {CreateUserDto} createUserDto - Die Daten des zu erstellenden Benutzers (E-Mail und Passwort).
   * @returns {Promise<Omit<User, 'password'>>} Das erstellte Benutzerobjekt ohne das Passwort.
   * @throws {ConflictException} Wenn ein Benutzer mit dieser E-Mail bereits existiert.
   * @throws {InternalServerErrorException} Wenn ein unerwarteter Fehler beim Hashen des Passworts oder beim Speichern des Benutzers auftritt.
   */
  async createUser(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    const { email, password } = createUserDto;

    // Log: Start der Benutzererstellung
    console.log(`UserService: Attempting to create user with email: ${email}`);

    // Überprüfen, ob der Benutzer bereits existiert
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      // Log: Benutzer existiert bereits
      console.warn(`UserService: User with email ${email} already exists.`);
      throw new ConflictException('Ein Benutzer mit dieser E-Mail-Adresse existiert bereits.');
    }

    // Passwort hashen
    let hashedPassword;
    try {
      hashedPassword = await bcrypt.hash(password, this.saltRounds);
      // Log: Passwort erfolgreich gehasht
      console.log(`UserService: Password for ${email} hashed successfully.`);
    } catch (error) {
      // Log: Fehler beim Hashen des Passworts
      console.error(`UserService: Error hashing password for ${email}:`, error);
      throw new InternalServerErrorException('Fehler beim Verarbeiten der Registrierung.');
    }

    // Benutzer in der Datenbank erstellen
    try {
      const newUser = await this.prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          // Standardrolle kann hier zugewiesen werden, z.B. 'driver' oder 'user'
          // role: 'USER', // Beispiel: Standardrolle 'USER'
        },
      });

      // Log: Benutzer erfolgreich erstellt
      console.log(`UserService: User ${email} created successfully with ID: ${newUser.id}`);

      // Passwort aus dem zurückgegebenen Objekt entfernen
      const { password: _, ...userWithoutPassword } = newUser;
      return userWithoutPassword;
    } catch (error: any) { // Explizite Typisierung des Fehlerobjekts als any zur Behebung des TS-Fehlers
      // Log: Fehler beim Speichern des Benutzers in der Datenbank
      console.error(`UserService: Error creating user ${email} in database:`, error);
      // Prüfen, ob es sich um einen Prisma Unique Constraint Fehler handelt
      // Es ist wichtig, die Struktur des Fehlerobjekts von Prisma genau zu kennen oder sicher zu prüfen
      if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002' && 
          'meta' in error && error.meta && typeof error.meta === 'object' && 'target' in error.meta && 
          Array.isArray((error.meta as any).target) && (error.meta as any).target.includes('email')) {
        throw new ConflictException('Ein Benutzer mit dieser E-Mail-Adresse existiert bereits.');
      }
      throw new InternalServerErrorException('Benutzer konnte nicht erstellt werden.');
    }
  }
}