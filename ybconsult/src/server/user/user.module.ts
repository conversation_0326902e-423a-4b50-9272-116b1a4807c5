// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/user/user.module.ts
import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { PrismaModule } from '../prisma/prisma.module'; // Importiere PrismaModule

/**
 * @module UserModule
 * Dieses Modul kapselt alle Funktionalitäten, die Benutzer betreffen.
 * Es importiert das PrismaModule, um Datenbankzugriff zu ermöglichen,
 * und deklariert UserController und UserService.
 */
@Module({
  imports: [PrismaModule], // Stelle PrismaService für UserService bereit
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService], // Exportiere UserService, falls er von anderen Modulen benötigt wird (z.B. AuthModule)
})
export class UserModule {}