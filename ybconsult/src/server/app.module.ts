// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/app.module.ts
import { Module } from '@nestjs/common';
import { AppController } from './app.controller'; // Wird später erstellt, falls benötigt
import { AppService } from './app.service';   // Wird später erstellt, falls benötigt
import { PrismaModule } from './prisma/prisma.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { ConfigModule } from '@nestjs/config'; // Import für Konfigurationsmanagement

/**
 * @module AppModule
 * Das Wurzelmodul der NestJS-Anwendung.
 * Es importiert alle Hauptmodule der Anwendung wie PrismaModule, UserModule und AuthModule.
 * Es konfiguriert auch das ConfigModule für den Zugriff auf Umgebungsvariablen.
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // Macht Konfigurationsvariablen global verfügbar
      envFilePath: '.env', // Spezifiziert den Pfad zur .env Datei
    }),
    PrismaModule, // Stellt Prisma-Dienste bereit
    UserModule,   // Stellt Benutzerbezogene Dienste und Controller bereit
    AuthModule,   // Stellt Authentifizierungsdienste und Controller bereit
  ],
  controllers: [AppController], // Standard-Controller, kann für Health-Checks etc. verwendet werden
  providers: [AppService],   // Standard-Service
})
export class AppModule {}