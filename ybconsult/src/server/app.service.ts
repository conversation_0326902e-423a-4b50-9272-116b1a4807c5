// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/app.service.ts
import { Injectable } from '@nestjs/common';

/**
 * @class AppService
 * Der Hauptdienst der Anwendung.
 * Kann für einfache Logik wie das Bereitstellen einer Willkommensnachricht verwendet werden.
 */
@Injectable()
export class AppService {
  /**
   * Gibt eine einfache Willkommensnachricht zurück.
   * @returns {string} Die Willkommensnachricht.
   */
  getHello(): string {
    // Log: getHello im AppService aufgerufen
    console.log('AppService: getHello method called');
    return 'Hello from YoungMobility NestJS Backend!';
  }
}