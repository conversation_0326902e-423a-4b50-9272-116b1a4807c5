// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { UserModule } from '../user/user.module'; // Importiere UserModule, um UserService nutzen zu können
import { PrismaModule } from '../prisma/prisma.module'; // Importiere PrismaModule für direkten Zugriff, falls benötigt
// import { JwtModule } from '@nestjs/jwt'; // Wird für JWT-basierte Authentifizierung benötigt
// import { PassportModule } from '@nestjs/passport'; // Wird für verschiedene Authentifizierungsstrategien benötigt
// import { JwtStrategy } from './strategies/jwt.strategy'; // Beispiel für eine JWT-Strategie

/**
 * @module AuthModule
 * Dieses Modul ist verantwortlich für Authentifizierungs- und Autorisierungslogik.
 * Es importiert das UserModule, um auf den UserService für Operationen wie die Benutzerregistrierung zuzugreifen.
 * Zukünftig wird es auch die JWT-Konfiguration und Authentifizierungsstrategien beinhalten.
 */
@Module({
  imports: [
    PrismaModule, // Stellt sicher, dass Prisma-Dienste verfügbar sind, falls direkt benötigt
    UserModule,   // Macht UserService für AuthController verfügbar
    // PassportModule.register({ defaultStrategy: 'jwt' }), // Registriere Passport mit JWT als Standardstrategie
    // JwtModule.register({
    //   secret: process.env.JWT_SECRET, // Lade Secret aus Umgebungsvariablen
    //   signOptions: { expiresIn: '60m' }, // Token-Gültigkeitsdauer
    // }),
  ],
  controllers: [AuthController],
  providers: [
    // UserService wird bereits vom UserModule bereitgestellt und exportiert, daher hier nicht erneut nötig
    // JwtStrategy, // Füge JWT-Strategie zu den Providern hinzu
  ],
  exports: [
    // JwtModule, // Exportiere JwtModule, falls andere Module es benötigen
    // PassportModule, // Exportiere PassportModule
  ]
})
export class AuthModule {}