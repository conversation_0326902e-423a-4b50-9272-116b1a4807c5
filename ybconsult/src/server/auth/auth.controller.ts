// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/auth/auth.controller.ts
import { Controller, Post, Body, ValidationPipe, UsePipes, HttpCode, HttpStatus } from '@nestjs/common';
import { UserService } from '../user/user.service'; // Import UserService
import { CreateUserDto } from '../user/dto/create-user.dto'; // Import CreateUserDto
import { User } from '@prisma/client';

/**
 * @class AuthController
 * Definiert die API-Endpunkte für Authentifizierungsoperationen, einschließlich der Registrierung.
 * Der Basispfad für diesen Controller ist `/api/auth` (wenn ein globales Präfix `/api` gesetzt ist).
 */
@Controller('auth')
export class AuthController {
  /**
   * Konstruktor, der den UserService injiziert.
   * @param {UserService} userService - Der Dienst für Benutzerlogik.
   */
  constructor(private readonly userService: UserService) {}

  /**
   * Endpunkt für die Benutzerregistrierung.
   * Nimmt E-Mail und Passwort entgegen, validiert sie und erstellt einen neuen Benutzer.
   * Erreichbar unter POST /api/auth/register.
   *
   * @param {CreateUserDto} createUserDto - Das DTO mit den Benutzerdaten.
   * @returns {Promise<Omit<User, 'password'>>} Das erstellte Benutzerobjekt ohne Passwort.
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
  async register(@Body() createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    // Log: Anfrage zum Registrieren eines Benutzers im AuthController erhalten
    console.log(`AuthController: Received request to register user: ${createUserDto.email}`);
    const user = await this.userService.createUser(createUserDto);
    // Log: Benutzer erfolgreich über AuthController registriert
    console.log(`AuthController: User ${user.email} registered successfully.`);
    return user;
  }

  // Hier könnten später Login-, Logout-, Token-Refresh-Endpunkte etc. hinzugefügt werden.
  // Zum Beispiel:
  // @Post('login')
  // @HttpCode(HttpStatus.OK)
  // async login(@Body() loginUserDto: LoginUserDto) { // LoginUserDto müsste definiert werden
  //   // Logik für Login
  // }
}