// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/prisma/prisma.service.ts
import { Injectable, OnModuleInit, OnApplicationShutdown } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

/**
 * @class PrismaService
 * Erweitert den PrismaClient und verwaltet dessen Lebenszyklus innerhalb der NestJS-Anwendung.
 * Stellt eine injizierbare Instanz des PrismaClient für andere Dienste bereit.
 */
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnApplicationShutdown {
  /**
   * Stellt sicher, dass beim Initialisieren des Moduls eine Verbindung zur Datenbank hergestellt wird.
   * @async
   */
  async onModuleInit() {
    // Prisma verbindet sich standardmäßig lazy, aber ein expliziter Connect-Aufruf kann hier erfolgen, falls erforder<PERSON>.
    // await this.$connect();
    // console.log('PrismaService: Database connection established onModuleInit.');
  }

  /**
   * St<PERSON>t sicher, dass die Datenbankverbindung beim Herunterfahren der Anwendung ordnungsgemäß geschlossen wird.
   * @async
   */
  async onApplicationShutdown(signal?: string) {
    // console.log(`PrismaService: Closing database connection on application shutdown (signal: ${signal}).`);
    await this.$disconnect();
  }
}