// /Users/<USER>/Coding/ybconsulting/ybconsult/src/server/prisma/prisma.module.ts
import { Module } from '@nestjs/common';
import { PrismaService } from './prisma.service';

/**
 * @module PrismaModule
 * Dieses Modul exportiert den PrismaService, um ihn in anderen Teilen der NestJS-Anwendung
 * über Dependency Injection verfügbar zu machen.
 */
@Module({
  providers: [PrismaService],
  exports: [PrismaService], // Exportiere PrismaService, damit er in anderen Modulen importiert werden kann
})
export class PrismaModule {}