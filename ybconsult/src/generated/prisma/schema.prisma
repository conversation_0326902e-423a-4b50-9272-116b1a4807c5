// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  BUSINESS_CLIENT
  DRIVER
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  password      String
  role          UserRole  @default(BUSINESS_CLIENT)
  emailVerified DateTime? @map("email_verified")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  clientProfile UserProfileClient? // Back-relation, FK is in UserProfileClient (userId)
  driverProfile UserProfileDriver? // Back-relation, FK is in UserProfileDriver (userId)

  @@map("users")
}

model UserProfileClient {
  id            String   @id @default(cuid())
  companyName   String?  @map("company_name")
  contactPerson String?  @map("contact_person")
  address       String?
  userId        String   @unique @map("user_id")
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("user_profiles_client")
}

model UserProfileDriver {
  id          String     @id @default(cuid())
  firstName   String?    @map("first_name")
  lastName    String?    @map("last_name")
  phoneNumber String?    @map("phone_number")
  address     String?
  userId      String     @unique @map("user_id")
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  documents   Document[]
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")

  @@map("user_profiles_driver")
}

model Document {
  id        String    @id @default(cuid())
  type      String // e.g., DRIVING_LICENSE, ID_CARD
  url       String // URL to the stored document (e.g., S3)
  verified  Boolean   @default(false)
  expiresAt DateTime? @map("expires_at")

  driverProfileId String            @map("driver_profile_id")
  driverProfile   UserProfileDriver @relation(fields: [driverProfileId], references: [id], onDelete: Cascade)

  uploadedAt DateTime @default(now()) @map("uploaded_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@map("documents")
}
