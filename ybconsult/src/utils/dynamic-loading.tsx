"use client";

import React, { ComponentType, lazy, Suspense, useState, useEffect } from "react";
import { motion } from "framer-motion";

/**
 * Dynamic Loading Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für dynamisches Laden von Komponenten.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

/**
 * Standardmäßiger Fallback für dynamisch geladene Komponenten
 */
export function DefaultLoadingFallback() {
  return (
    <div className="flex items-center justify-center p-4 min-h-[100px]">
      <motion.div
        className="w-8 h-8 border-4 border-quechua-medium-green border-t-transparent rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      />
    </div>
  );
}

/**
 * Skelett-Fallback für Karten
 */
export function CardSkeletonFallback() {
  return (
    <div className="border rounded-lg p-4 shadow-sm">
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
      <div className="h-10 bg-gray-200 rounded w-1/3"></div>
    </div>
  );
}

/**
 * Dynamisch geladene Komponente mit Suspense und Fallback
 * @param importFunc Funktion, die die Komponente importiert
 * @param fallback Fallback-Komponente während des Ladens
 * @returns Dynamisch geladene Komponente
 */
export function dynamicComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback: React.ReactNode = <DefaultLoadingFallback />
) {
  const LazyComponent = lazy(importFunc);

  return function DynamicComponent(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

/**
 * Hook zum verzögerten Laden von Komponenten, wenn sie im Viewport sind
 * @param importFunc Funktion, die die Komponente importiert
 * @param options Optionen für das verzögerte Laden
 * @returns Komponente und Ladestatus
 */
export function useLazyComponent<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: {
    preload?: boolean;
    delay?: number;
    fallback?: React.ReactNode;
  } = {}
) {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;
    let timer: NodeJS.Timeout | null = null;

    const loadComponent = async () => {
      try {
        if (options.delay) {
          await new Promise((resolve) => {
            timer = setTimeout(resolve, options.delay);
          });
        }

        const module = await importFunc();
        
        if (isMounted) {
          setComponent(module.default);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error("Error loading component:", err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
        }
      }
    };

    if (options.preload) {
      loadComponent();
    }

    return () => {
      isMounted = false;
      if (timer) clearTimeout(timer);
    };
  }, [importFunc, options.delay, options.preload]);

  const load = () => {
    if (!Component && !loading) {
      setLoading(true);
      importFunc()
        .then((module) => {
          setComponent(module.default);
          setLoading(false);
        })
        .catch((err) => {
          console.error("Error loading component:", err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
        });
    }
  };

  const LazyComponent = Component
    ? (props: React.ComponentProps<T>) => <Component {...props} />
    : () => (options.fallback || <DefaultLoadingFallback />);

  return { Component: LazyComponent, loading, error, load };
}

/**
 * Komponente, die ihren Inhalt nur rendert, wenn er im Viewport ist
 */
export function ViewportLoader({
  children,
  fallback = <DefaultLoadingFallback />,
  threshold = 0.1,
  rootMargin = "100px",
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    observer.observe(currentRef);

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold, rootMargin]);

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  );
}
