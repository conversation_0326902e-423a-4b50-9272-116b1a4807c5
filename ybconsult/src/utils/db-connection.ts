/**
 * Database Connection Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für die Datenbankverbindung mit Connection Pooling.
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */

import { PrismaClient } from "@prisma/client";
import { Pool } from "pg";

// Globale Variablen für Singleton-Instanzen
let prismaInstance: PrismaClient | undefined;
let pgPool: Pool | undefined;

/**
 * Konfiguration für den PostgreSQL-Verbindungspool
 */
const PG_POOL_CONFIG = {
  max: process.env.NODE_ENV === "production" ? 20 : 5, // Maximale Anzahl von Verbindungen
  min: process.env.NODE_ENV === "production" ? 5 : 2,  // Minimale Anzahl von Verbindungen
  idleTimeoutMillis: 30000,                           // Zeit in ms, nach der inaktive Verbindungen geschlossen werden
  connectionTimeoutMillis: 2000,                      // Zeit in ms, nach der ein Verbindungsversuch abgebrochen wird
};

/**
 * Gibt eine Singleton-Instanz des PrismaClient zurück
 * @returns PrismaClient-Instanz
 */
export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = new PrismaClient({
      log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
      // Erweiterte Konfiguration für Verbindungspooling
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });
    
    // Event-Handler für Verbindungsprobleme
    prismaInstance.$on("query", (e) => {
      if (process.env.DEBUG_DB === "true") {
        console.log("Query: " + e.query);
        console.log("Params: " + e.params);
        console.log("Duration: " + e.duration + "ms");
      }
    });
    
    prismaInstance.$on("error", (e) => {
      console.error("Prisma Error:", e);
    });
  }
  
  return prismaInstance;
}

/**
 * Gibt eine Singleton-Instanz des PostgreSQL-Verbindungspools zurück
 * @returns PostgreSQL-Pool-Instanz
 */
export function getPgPool(): Pool {
  if (!pgPool) {
    pgPool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ...PG_POOL_CONFIG,
    });
    
    // Event-Handler für Verbindungsprobleme
    pgPool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
    
    pgPool.on("connect", (client) => {
      if (process.env.DEBUG_DB === "true") {
        console.log("New client connected to PostgreSQL pool");
      }
      
      // Setze Statement-Timeout für alle Verbindungen
      client.query("SET statement_timeout = 10000"); // 10 Sekunden
    });
  }
  
  return pgPool;
}

/**
 * Führt eine Abfrage mit dem PostgreSQL-Pool aus
 * @param query SQL-Abfrage
 * @param params Parameter für die Abfrage
 * @returns Ergebnis der Abfrage
 */
export async function executeQuery<T>(query: string, params: any[] = []): Promise<T[]> {
  const pool = getPgPool();
  const client = await pool.connect();
  
  try {
    const result = await client.query(query, params);
    return result.rows as T[];
  } finally {
    client.release();
  }
}

/**
 * Führt eine Transaktion mit dem PostgreSQL-Pool aus
 * @param callback Callback-Funktion, die die Transaktion ausführt
 * @returns Ergebnis der Transaktion
 */
export async function executeTransaction<T>(
  callback: (client: any) => Promise<T>
): Promise<T> {
  const pool = getPgPool();
  const client = await pool.connect();
  
  try {
    await client.query("BEGIN");
    const result = await callback(client);
    await client.query("COMMIT");
    return result;
  } catch (error) {
    await client.query("ROLLBACK");
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Schließt alle Datenbankverbindungen
 */
export async function closeAllConnections(): Promise<void> {
  if (prismaInstance) {
    await prismaInstance.$disconnect();
    prismaInstance = undefined;
  }
  
  if (pgPool) {
    await pgPool.end();
    pgPool = undefined;
  }
}

/**
 * Prüft die Gesundheit der Datenbankverbindung
 * @returns true, wenn die Verbindung gesund ist, sonst false
 */
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  responseTime: number;
  connectionCount: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const pool = getPgPool();
    const client = await pool.connect();
    
    try {
      // Führe eine einfache Abfrage aus
      await client.query("SELECT 1");
      
      // Hole Statistiken über den Pool
      const poolStats = await client.query(`
        SELECT count(*) as connection_count
        FROM pg_stat_activity
        WHERE application_name = $1
      `, ["postgres"]);
      
      const connectionCount = parseInt(poolStats.rows[0].connection_count, 10);
      
      return {
        healthy: true,
        responseTime: Date.now() - startTime,
        connectionCount,
      };
    } finally {
      client.release();
    }
  } catch (error) {
    return {
      healthy: false,
      responseTime: Date.now() - startTime,
      connectionCount: 0,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
