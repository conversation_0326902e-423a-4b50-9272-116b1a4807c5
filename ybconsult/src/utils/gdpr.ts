/**
 * GDPR Compliance Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für die GDPR-Compliance der Anwendung.
 * Teil der Phase 8: Security Hardening & GDPR Compliance Audit (YM-803)
 */

import { z } from "zod";
import { anonymizeEmail, anonymizeIp } from "./security";

/**
 * Typen für Einwilligungskategorien
 */
export enum ConsentCategory {
  ESSENTIAL = "essential",
  FUNCTIONAL = "functional",
  ANALYTICS = "analytics",
  MARKETING = "marketing",
  PERSONALIZATION = "personalization",
}

/**
 * Typen für Einwilligungsstatus
 */
export enum ConsentStatus {
  GRANTED = "granted",
  DENIED = "denied",
  PENDING = "pending",
}

/**
 * Interface für Einwilligungsdaten
 */
export interface ConsentData {
  [ConsentCategory.ESSENTIAL]: ConsentStatus.GRANTED; // Immer gewährt
  [ConsentCategory.FUNCTIONAL]: ConsentStatus;
  [ConsentCategory.ANALYTICS]: ConsentStatus;
  [ConsentCategory.MARKETING]: ConsentStatus;
  [ConsentCategory.PERSONALIZATION]: ConsentStatus;
  timestamp: number;
  version: string;
}

/**
 * Zod-Schema für Einwilligungsdaten
 */
export const consentSchema = z.object({
  [ConsentCategory.ESSENTIAL]: z.literal(ConsentStatus.GRANTED),
  [ConsentCategory.FUNCTIONAL]: z.nativeEnum(ConsentStatus),
  [ConsentCategory.ANALYTICS]: z.nativeEnum(ConsentStatus),
  [ConsentCategory.MARKETING]: z.nativeEnum(ConsentStatus),
  [ConsentCategory.PERSONALIZATION]: z.nativeEnum(ConsentStatus),
  timestamp: z.number(),
  version: z.string(),
});

/**
 * Aktuelle Version der Einwilligungsdaten
 */
export const CURRENT_CONSENT_VERSION = "1.0.0";

/**
 * Standardwerte für Einwilligungsdaten
 */
export const DEFAULT_CONSENT_DATA: ConsentData = {
  [ConsentCategory.ESSENTIAL]: ConsentStatus.GRANTED,
  [ConsentCategory.FUNCTIONAL]: ConsentStatus.PENDING,
  [ConsentCategory.ANALYTICS]: ConsentStatus.PENDING,
  [ConsentCategory.MARKETING]: ConsentStatus.PENDING,
  [ConsentCategory.PERSONALIZATION]: ConsentStatus.PENDING,
  timestamp: Date.now(),
  version: CURRENT_CONSENT_VERSION,
};

/**
 * Speichert Einwilligungsdaten im localStorage
 * @param consentData Einwilligungsdaten
 */
export function saveConsent(consentData: ConsentData): void {
  if (typeof window === "undefined") return;
  
  try {
    localStorage.setItem("gdpr_consent", JSON.stringify(consentData));
  } catch (error) {
    console.error("Error saving consent data:", error);
  }
}

/**
 * Lädt Einwilligungsdaten aus dem localStorage
 * @returns Einwilligungsdaten oder null, wenn keine vorhanden sind
 */
export function loadConsent(): ConsentData | null {
  if (typeof window === "undefined") return null;
  
  try {
    const storedConsent = localStorage.getItem("gdpr_consent");
    if (!storedConsent) return null;
    
    const parsedConsent = JSON.parse(storedConsent);
    const validatedConsent = consentSchema.safeParse(parsedConsent);
    
    if (!validatedConsent.success) {
      console.error("Invalid consent data:", validatedConsent.error);
      return null;
    }
    
    // Prüfe, ob die Version aktuell ist
    if (validatedConsent.data.version !== CURRENT_CONSENT_VERSION) {
      // Bei Versionsänderung sollten die Einwilligungen neu eingeholt werden
      return null;
    }
    
    return validatedConsent.data;
  } catch (error) {
    console.error("Error loading consent data:", error);
    return null;
  }
}

/**
 * Prüft, ob eine Einwilligung für eine bestimmte Kategorie vorliegt
 * @param category Einwilligungskategorie
 * @returns true, wenn die Einwilligung vorliegt, sonst false
 */
export function hasConsent(category: ConsentCategory): boolean {
  if (category === ConsentCategory.ESSENTIAL) return true;
  
  const consentData = loadConsent();
  if (!consentData) return false;
  
  return consentData[category] === ConsentStatus.GRANTED;
}

/**
 * Aktualisiert Einwilligungsdaten für eine bestimmte Kategorie
 * @param category Einwilligungskategorie
 * @param status Einwilligungsstatus
 */
export function updateConsent(
  category: ConsentCategory,
  status: ConsentStatus
): void {
  if (category === ConsentCategory.ESSENTIAL && status !== ConsentStatus.GRANTED) {
    console.error("Essential cookies cannot be denied");
    return;
  }
  
  const consentData = loadConsent() || { ...DEFAULT_CONSENT_DATA };
  consentData[category] = status;
  consentData.timestamp = Date.now();
  
  saveConsent(consentData);
}

/**
 * Aktualisiert alle Einwilligungsdaten auf einmal
 * @param statuses Einwilligungsstatus für alle Kategorien
 */
export function updateAllConsent(statuses: {
  [ConsentCategory.FUNCTIONAL]?: ConsentStatus;
  [ConsentCategory.ANALYTICS]?: ConsentStatus;
  [ConsentCategory.MARKETING]?: ConsentStatus;
  [ConsentCategory.PERSONALIZATION]?: ConsentStatus;
}): void {
  const consentData = loadConsent() || { ...DEFAULT_CONSENT_DATA };
  
  Object.entries(statuses).forEach(([category, status]) => {
    if (category !== ConsentCategory.ESSENTIAL) {
      consentData[category as ConsentCategory] = status;
    }
  });
  
  consentData.timestamp = Date.now();
  saveConsent(consentData);
}

/**
 * Löscht alle Einwilligungsdaten
 */
export function clearConsent(): void {
  if (typeof window === "undefined") return;
  
  try {
    localStorage.removeItem("gdpr_consent");
  } catch (error) {
    console.error("Error clearing consent data:", error);
  }
}

/**
 * Typen für personenbezogene Daten
 */
export interface PersonalData {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  ip?: string;
  dateOfBirth?: string;
  [key: string]: any;
}

/**
 * Anonymisiert personenbezogene Daten
 * @param data Personenbezogene Daten
 * @returns Anonymisierte Daten
 */
export function anonymizePersonalData(data: PersonalData): PersonalData {
  const anonymized: PersonalData = {};
  
  Object.entries(data).forEach(([key, value]) => {
    if (value === null || value === undefined) {
      anonymized[key] = value;
      return;
    }
    
    switch (key) {
      case "email":
        anonymized[key] = anonymizeEmail(value as string);
        break;
      case "ip":
        anonymized[key] = anonymizeIp(value as string);
        break;
      case "name":
        anonymized[key] = "***";
        break;
      case "phone":
        anonymized[key] = "***-***-" + (value as string).slice(-4);
        break;
      case "address":
        anonymized[key] = "***";
        break;
      case "dateOfBirth":
        anonymized[key] = "****-**-**";
        break;
      default:
        // Für unbekannte Felder: Wert beibehalten, wenn es kein String ist
        if (typeof value !== "string") {
          anonymized[key] = value;
        } else {
          // Für Strings: Anonymisieren, wenn es wie PII aussieht
          if (value.length > 5) {
            anonymized[key] = value.charAt(0) + "*".repeat(value.length - 2) + value.charAt(value.length - 1);
          } else {
            anonymized[key] = "***";
          }
        }
    }
  });
  
  return anonymized;
}

/**
 * Generiert einen Datenschutzhinweis für eine bestimmte Datenverarbeitung
 * @param purpose Zweck der Datenverarbeitung
 * @param dataCategories Kategorien der verarbeiteten Daten
 * @param retention Aufbewahrungsdauer
 * @param legalBasis Rechtsgrundlage
 * @returns Datenschutzhinweis
 */
export function generatePrivacyNotice(
  purpose: string,
  dataCategories: string[],
  retention: string,
  legalBasis: string
): string {
  return `
    <div class="privacy-notice">
      <h3>Datenschutzhinweis</h3>
      <p><strong>Zweck der Datenverarbeitung:</strong> ${purpose}</p>
      <p><strong>Kategorien der verarbeiteten Daten:</strong> ${dataCategories.join(", ")}</p>
      <p><strong>Aufbewahrungsdauer:</strong> ${retention}</p>
      <p><strong>Rechtsgrundlage:</strong> ${legalBasis}</p>
      <p>Sie haben das Recht auf Auskunft, Berichtigung, Löschung und Einschränkung der Verarbeitung Ihrer Daten. Weitere Informationen finden Sie in unserer Datenschutzerklärung.</p>
    </div>
  `;
}
