/**
 * Browser Compatibility Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen zur Erkennung und Handhabung von Browser-Kompatibilitätsproblemen.
 * Teil der Phase 8: UI/UX Polish & Quechua Design Consistency Review (YM-801)
 */

/**
 * Erkennt den aktuellen Browser des Benutzers
 * @returns Ein Objekt mit Informationen über den Browser
 */
export function detectBrowser(): {
  name: string;
  version: string;
  isChrome: boolean;
  isFirefox: boolean;
  isSafari: boolean;
  isEdge: boolean;
  isIE: boolean;
  isMobile: boolean;
} {
  if (typeof window === 'undefined') {
    // Server-side rendering
    return {
      name: 'server',
      version: '0',
      isChrome: false,
      isFirefox: false,
      isSafari: false,
      isEdge: false,
      isIE: false,
      isMobile: false,
    };
  }

  const userAgent = window.navigator.userAgent;
  const browsers = {
    chrome: /chrome|chromium|crios/i,
    safari: /safari/i,
    firefox: /firefox|fxios/i,
    edge: /edg/i,
    ie: /msie|trident/i,
  };

  // Mobile-Erkennung
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  // Browser-Erkennung
  let name = 'unknown';
  let version = 'unknown';

  if (browsers.edge.test(userAgent)) {
    name = 'edge';
    version = userAgent.match(/Edge?\/(\d+)/i)?.[1] || 'unknown';
  } else if (browsers.chrome.test(userAgent) && browsers.safari.test(userAgent)) {
    name = 'chrome';
    version = userAgent.match(/(?:Chrome|Chromium|CriOS)\/(\d+)/i)?.[1] || 'unknown';
  } else if (browsers.firefox.test(userAgent)) {
    name = 'firefox';
    version = userAgent.match(/(?:Firefox|FxiOS)\/(\d+)/i)?.[1] || 'unknown';
  } else if (browsers.safari.test(userAgent) && !browsers.chrome.test(userAgent)) {
    name = 'safari';
    version = userAgent.match(/Version\/(\d+)/i)?.[1] || 'unknown';
  } else if (browsers.ie.test(userAgent)) {
    name = 'ie';
    version = userAgent.match(/(?:MSIE |rv:)(\d+)/i)?.[1] || 'unknown';
  }

  return {
    name,
    version,
    isChrome: name === 'chrome',
    isFirefox: name === 'firefox',
    isSafari: name === 'safari',
    isEdge: name === 'edge',
    isIE: name === 'ie',
    isMobile,
  };
}

/**
 * Prüft, ob der Browser bestimmte CSS-Funktionen unterstützt
 * @returns Ein Objekt mit Informationen über die CSS-Unterstützung
 */
export function detectCSSSupport(): {
  supportsGrid: boolean;
  supportsFlexbox: boolean;
  supportsCustomProperties: boolean;
  supportsBackdropFilter: boolean;
} {
  if (typeof window === 'undefined' || !window.CSS || !window.CSS.supports) {
    // Server-side rendering oder keine CSS.supports-Unterstützung
    return {
      supportsGrid: false,
      supportsFlexbox: false,
      supportsCustomProperties: false,
      supportsBackdropFilter: false,
    };
  }

  return {
    supportsGrid: CSS.supports('display', 'grid'),
    supportsFlexbox: CSS.supports('display', 'flex'),
    supportsCustomProperties: CSS.supports('(--custom-property: 0)'),
    supportsBackdropFilter: CSS.supports('backdrop-filter', 'blur(10px)') || 
                           CSS.supports('-webkit-backdrop-filter', 'blur(10px)'),
  };
}

/**
 * Wendet Browser-spezifische Fallbacks an, wenn bestimmte Funktionen nicht unterstützt werden
 * @param element Das DOM-Element, auf das Fallbacks angewendet werden sollen
 */
export function applyBrowserFallbacks(element: HTMLElement): void {
  const cssSupport = detectCSSSupport();
  const browser = detectBrowser();

  // Fallback für fehlende CSS-Custom-Properties-Unterstützung
  if (!cssSupport.supportsCustomProperties) {
    // Fallback-Farben für Quechua-Theme
    element.style.setProperty('background-color', '#FFFDF6'); // Fallback für var(--quechua-cream)
    
    // Weitere Fallbacks hier...
  }

  // Safari-spezifische Anpassungen
  if (browser.isSafari) {
    // Safari hat manchmal Probleme mit bestimmten Flexbox-Eigenschaften
    const flexContainers = element.querySelectorAll('.flex');
    flexContainers.forEach((container) => {
      (container as HTMLElement).style.display = 'flex';
      (container as HTMLElement).style.flexDirection = 'row';
    });
  }

  // IE und ältere Edge-spezifische Anpassungen
  if (browser.isIE || (browser.isEdge && parseInt(browser.version) < 79)) {
    // Fallbacks für ältere Browser, die keine Grid-Unterstützung haben
    if (!cssSupport.supportsGrid) {
      const gridContainers = element.querySelectorAll('.grid');
      gridContainers.forEach((container) => {
        (container as HTMLElement).style.display = 'block';
      });
    }
  }
}

/**
 * Hook für React-Komponenten, um Browser-Informationen zu erhalten
 * (Muss in einer Client-Komponente verwendet werden)
 */
export function useBrowserInfo() {
  if (typeof window === 'undefined') {
    return {
      browser: {
        name: 'server',
        version: '0',
        isChrome: false,
        isFirefox: false,
        isSafari: false,
        isEdge: false,
        isIE: false,
        isMobile: false,
      },
      cssSupport: {
        supportsGrid: false,
        supportsFlexbox: false,
        supportsCustomProperties: false,
        supportsBackdropFilter: false,
      }
    };
  }

  return {
    browser: detectBrowser(),
    cssSupport: detectCSSSupport(),
  };
}
