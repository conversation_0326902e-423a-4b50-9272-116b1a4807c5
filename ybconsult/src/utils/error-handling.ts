/**
 * Error Handling Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für die Fehlerbehandlung.
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */

import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror } from "zod";
import { PrismaClientKnownRequestError, PrismaClientValidationError } from "@prisma/client/runtime/library";

/**
 * Typen für API-Fehler
 */
export enum ErrorCode {
  BAD_REQUEST = "BAD_REQUEST",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  NOT_FOUND = "NOT_FOUND",
  CONFLICT = "CONFLICT",
  INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
  SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  DATABASE_ERROR = "DATABASE_ERROR",
}

/**
 * Interface für API-Fehler
 */
export interface ApiError {
  code: ErrorCode;
  message: string;
  details?: any;
  stack?: string;
}

/**
 * Erstellt einen API-Fehler
 * @param code Fehlercode
 * @param message Fehlermeldung
 * @param details Details zum Fehler
 * @returns API-Fehler
 */
export function createApiError(
  code: ErrorCode,
  message: string,
  details?: any
): ApiError {
  return {
    code,
    message,
    details,
    stack: process.env.NODE_ENV === "development" ? new Error().stack : undefined,
  };
}

/**
 * Erstellt eine Fehlerantwort für API-Anfragen
 * @param error API-Fehler
 * @param status HTTP-Statuscode
 * @returns NextResponse mit Fehler
 */
export function createErrorResponse(
  error: ApiError,
  status: number
): NextResponse {
  return NextResponse.json({ error }, { status });
}

/**
 * Behandelt Fehler in API-Routen
 * @param error Fehler
 * @returns NextResponse mit Fehler
 */
export function handleApiError(error: unknown): NextResponse {
  console.error("API Error:", error);
  
  // Zod-Validierungsfehler
  if (error instanceof ZodError) {
    return createErrorResponse(
      createApiError(
        ErrorCode.VALIDATION_ERROR,
        "Validation failed",
        error.errors
      ),
      400
    );
  }
  
  // Prisma-Fehler
  if (error instanceof PrismaClientKnownRequestError) {
    // Behandle bekannte Prisma-Fehler
    if (error.code === "P2002") {
      return createErrorResponse(
        createApiError(
          ErrorCode.CONFLICT,
          "A record with this data already exists",
          { target: error.meta?.target }
        ),
        409
      );
    }
    
    if (error.code === "P2025") {
      return createErrorResponse(
        createApiError(
          ErrorCode.NOT_FOUND,
          "Record not found",
          { details: error.meta }
        ),
        404
      );
    }
    
    return createErrorResponse(
      createApiError(
        ErrorCode.DATABASE_ERROR,
        "Database error",
        { code: error.code, meta: error.meta }
      ),
      500
    );
  }
  
  if (error instanceof PrismaClientValidationError) {
    return createErrorResponse(
      createApiError(
        ErrorCode.VALIDATION_ERROR,
        "Invalid data provided to database",
        { message: error.message }
      ),
      400
    );
  }
  
  // Standard-Fehler
  if (error instanceof Error) {
    return createErrorResponse(
      createApiError(
        ErrorCode.INTERNAL_SERVER_ERROR,
        error.message || "An unexpected error occurred",
        process.env.NODE_ENV === "development" ? { stack: error.stack } : undefined
      ),
      500
    );
  }
  
  // Unbekannte Fehler
  return createErrorResponse(
    createApiError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      "An unexpected error occurred"
    ),
    500
  );
}

/**
 * Middleware für die Fehlerbehandlung in API-Routen
 * @param handler API-Route-Handler
 * @returns API-Route-Handler mit Fehlerbehandlung
 */
export function withErrorHandling(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: any[]) => {
    try {
      return await handler(req, ...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

/**
 * Typen für Fehler-Logging
 */
export enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARN = "warn",
  ERROR = "error",
  FATAL = "fatal",
}

/**
 * Loggt einen Fehler
 * @param level Log-Level
 * @param message Nachricht
 * @param error Fehler
 * @param context Kontext
 */
export function logError(
  level: LogLevel,
  message: string,
  error?: Error | unknown,
  context?: Record<string, any>
): void {
  const timestamp = new Date().toISOString();
  const errorObject = error instanceof Error ? {
    name: error.name,
    message: error.message,
    stack: error.stack,
  } : error;
  
  const logEntry = {
    timestamp,
    level,
    message,
    error: errorObject,
    context,
  };
  
  // In Produktion würde hier ein Logging-Service wie Axiom oder Sentry verwendet werden
  if (level === LogLevel.ERROR || level === LogLevel.FATAL) {
    console.error(JSON.stringify(logEntry));
  } else if (level === LogLevel.WARN) {
    console.warn(JSON.stringify(logEntry));
  } else if (level === LogLevel.INFO) {
    console.info(JSON.stringify(logEntry));
  } else {
    console.log(JSON.stringify(logEntry));
  }
  
  // Hier könnte ein Logging-Service integriert werden
  // z.B. Sentry.captureException(error);
}

/**
 * Behandelt unbehandelte Fehler und Ablehnungen
 */
export function setupGlobalErrorHandlers(): void {
  if (typeof window === "undefined") {
    // Server-seitige Fehlerbehandlung
    process.on("uncaughtException", (error) => {
      logError(
        LogLevel.FATAL,
        "Uncaught Exception",
        error
      );
      // In Produktion sollte der Prozess neu gestartet werden
      if (process.env.NODE_ENV === "production") {
        process.exit(1);
      }
    });
    
    process.on("unhandledRejection", (reason) => {
      logError(
        LogLevel.ERROR,
        "Unhandled Promise Rejection",
        reason instanceof Error ? reason : new Error(String(reason))
      );
    });
  } else {
    // Client-seitige Fehlerbehandlung
    window.addEventListener("error", (event) => {
      logError(
        LogLevel.ERROR,
        "Uncaught Error",
        event.error,
        {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        }
      );
    });
    
    window.addEventListener("unhandledrejection", (event) => {
      logError(
        LogLevel.ERROR,
        "Unhandled Promise Rejection",
        event.reason instanceof Error ? event.reason : new Error(String(event.reason))
      );
    });
  }
}
