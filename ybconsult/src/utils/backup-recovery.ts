/**
 * Data Backup and Recovery Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für Datensicherung und -wiederherstellung.
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */

import { PrismaClient } from "@prisma/client";
import { executeQuery } from "./db-connection";
import { logError, LogLevel } from "./error-handling";
import fs from "fs";
import path from "path";
import { promisify } from "util";
import { exec } from "child_process";

const execAsync = promisify(exec);

/**
 * Interface für Backup-Metadaten
 */
export interface BackupMetadata {
  id: string;
  timestamp: string;
  type: "full" | "incremental";
  size: number;
  tables: string[];
  status: "success" | "failed" | "in_progress";
  error?: string;
}

/**
 * Erstellt ein Backup der Datenbank
 * @param type Typ des Backups
 * @param tables Zu sichernde Tabellen (optional)
 * @returns Backup-Metadaten
 */
export async function createDatabaseBackup(
  type: "full" | "incremental" = "full",
  tables?: string[]
): Promise<BackupMetadata> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const backupId = `backup-${type}-${timestamp}`;
  const backupDir = process.env.BACKUP_DIR || "./backups";
  
  // Stelle sicher, dass das Backup-Verzeichnis existiert
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const backupPath = path.join(backupDir, `${backupId}.sql`);
  
  const metadata: BackupMetadata = {
    id: backupId,
    timestamp: new Date().toISOString(),
    type,
    size: 0,
    tables: tables || [],
    status: "in_progress",
  };
  
  try {
    // In einer echten Implementierung würde hier ein pg_dump-Befehl ausgeführt werden
    // Hier nur als Beispiel:
    if (process.env.NODE_ENV === "production") {
      const databaseUrl = process.env.DATABASE_URL || "";
      const tableArgs = tables && tables.length > 0 ? tables.map(t => `-t ${t}`).join(" ") : "";
      
      await execAsync(
        `pg_dump ${databaseUrl} ${tableArgs} --format=custom --file=${backupPath}`
      );
    } else {
      // Im Entwicklungsmodus erstellen wir ein Dummy-Backup
      const prisma = new PrismaClient();
      const schema = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `;
      
      const dummyBackupContent = JSON.stringify(schema, null, 2);
      fs.writeFileSync(backupPath, dummyBackupContent);
    }
    
    // Aktualisiere Metadaten
    const stats = fs.statSync(backupPath);
    metadata.size = stats.size;
    metadata.status = "success";
    
    // Speichere Metadaten
    const metadataPath = path.join(backupDir, `${backupId}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    
    return metadata;
  } catch (error) {
    logError(
      LogLevel.ERROR,
      "Failed to create database backup",
      error,
      { backupId, type }
    );
    
    metadata.status = "failed";
    metadata.error = error instanceof Error ? error.message : String(error);
    
    // Speichere Metadaten auch bei Fehler
    const metadataPath = path.join(backupDir, `${backupId}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    
    throw error;
  }
}

/**
 * Stellt ein Backup wieder her
 * @param backupId ID des Backups
 * @returns true, wenn die Wiederherstellung erfolgreich war, sonst false
 */
export async function restoreDatabaseBackup(backupId: string): Promise<boolean> {
  const backupDir = process.env.BACKUP_DIR || "./backups";
  const backupPath = path.join(backupDir, `${backupId}.sql`);
  const metadataPath = path.join(backupDir, `${backupId}.json`);
  
  if (!fs.existsSync(backupPath) || !fs.existsSync(metadataPath)) {
    throw new Error(`Backup ${backupId} not found`);
  }
  
  try {
    // Lese Metadaten
    const metadataContent = fs.readFileSync(metadataPath, "utf-8");
    const metadata: BackupMetadata = JSON.parse(metadataContent);
    
    if (metadata.status !== "success") {
      throw new Error(`Cannot restore backup with status ${metadata.status}`);
    }
    
    // In einer echten Implementierung würde hier ein pg_restore-Befehl ausgeführt werden
    // Hier nur als Beispiel:
    if (process.env.NODE_ENV === "production") {
      const databaseUrl = process.env.DATABASE_URL || "";
      
      await execAsync(
        `pg_restore --clean --if-exists --dbname=${databaseUrl} ${backupPath}`
      );
    } else {
      // Im Entwicklungsmodus tun wir so, als ob wir das Backup wiederherstellen würden
      console.log(`[DEV] Simulating restore of backup ${backupId}`);
    }
    
    return true;
  } catch (error) {
    logError(
      LogLevel.ERROR,
      "Failed to restore database backup",
      error,
      { backupId }
    );
    
    throw error;
  }
}

/**
 * Listet alle verfügbaren Backups auf
 * @returns Array von Backup-Metadaten
 */
export function listBackups(): BackupMetadata[] {
  const backupDir = process.env.BACKUP_DIR || "./backups";
  
  if (!fs.existsSync(backupDir)) {
    return [];
  }
  
  const files = fs.readdirSync(backupDir);
  const metadataFiles = files.filter(file => file.endsWith(".json"));
  
  return metadataFiles.map(file => {
    const metadataPath = path.join(backupDir, file);
    const metadataContent = fs.readFileSync(metadataPath, "utf-8");
    return JSON.parse(metadataContent) as BackupMetadata;
  }).sort((a, b) => {
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });
}

/**
 * Löscht ein Backup
 * @param backupId ID des Backups
 * @returns true, wenn das Backup gelöscht wurde, sonst false
 */
export function deleteBackup(backupId: string): boolean {
  const backupDir = process.env.BACKUP_DIR || "./backups";
  const backupPath = path.join(backupDir, `${backupId}.sql`);
  const metadataPath = path.join(backupDir, `${backupId}.json`);
  
  let success = true;
  
  if (fs.existsSync(backupPath)) {
    try {
      fs.unlinkSync(backupPath);
    } catch (error) {
      logError(
        LogLevel.ERROR,
        "Failed to delete backup file",
        error,
        { backupId }
      );
      success = false;
    }
  }
  
  if (fs.existsSync(metadataPath)) {
    try {
      fs.unlinkSync(metadataPath);
    } catch (error) {
      logError(
        LogLevel.ERROR,
        "Failed to delete backup metadata file",
        error,
        { backupId }
      );
      success = false;
    }
  }
  
  return success;
}

/**
 * Bereinigt alte Backups
 * @param maxAge Maximales Alter der Backups in Tagen
 * @param keepCount Mindestanzahl der zu behaltenden Backups
 * @returns Anzahl der gelöschten Backups
 */
export function cleanupOldBackups(maxAge: number = 30, keepCount: number = 5): number {
  const backups = listBackups();
  
  if (backups.length <= keepCount) {
    return 0;
  }
  
  const now = new Date();
  const maxAgeMs = maxAge * 24 * 60 * 60 * 1000;
  
  let deletedCount = 0;
  
  // Behalte die neuesten keepCount Backups
  const backupsToKeep = backups.slice(0, keepCount);
  const backupsToCheck = backups.slice(keepCount);
  
  for (const backup of backupsToCheck) {
    const backupDate = new Date(backup.timestamp);
    const ageMs = now.getTime() - backupDate.getTime();
    
    if (ageMs > maxAgeMs) {
      if (deleteBackup(backup.id)) {
        deletedCount++;
      }
    }
  }
  
  return deletedCount;
}
