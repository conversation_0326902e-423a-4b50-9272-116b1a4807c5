"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";

/**
 * Component Optimization Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen zur Optimierung von React-Komponenten.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

/**
 * HOC für die Memoization von Komponenten mit Tiefenvergleich der Props
 * @param Component Die zu memoisierte Komponente
 * @param propsAreEqual Optionale Funktion zum Vergleich der Props
 * @returns Memoized Component
 */
export function memoWithDeepCompare<P extends object>(
  Component: React.ComponentType<P>,
  propsAreEqual?: (prevProps: Readonly<P>, nextProps: Readonly<P>) => boolean
) {
  function deepCompare(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;
    
    if (
      typeof obj1 !== "object" ||
      typeof obj2 !== "object" ||
      obj1 === null ||
      obj2 === null
    ) {
      return obj1 === obj2;
    }
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    return keys1.every(key => {
      if (!Object.prototype.hasOwnProperty.call(obj2, key)) return false;
      return deepCompare(obj1[key], obj2[key]);
    });
  }
  
  function arePropsEqual(prevProps: Readonly<P>, nextProps: Readonly<P>): boolean {
    if (propsAreEqual) {
      return propsAreEqual(prevProps, nextProps);
    }
    return deepCompare(prevProps, nextProps);
  }
  
  const MemoizedComponent = React.memo(Component, arePropsEqual);
  
  // Kopiere displayName und andere statische Eigenschaften
  if (Component.displayName) {
    MemoizedComponent.displayName = `MemoDeep(${Component.displayName})`;
  } else if (Component.name) {
    MemoizedComponent.displayName = `MemoDeep(${Component.name})`;
  }
  
  return MemoizedComponent;
}

/**
 * Hook für die Debounce-Funktion
 * @param value Der zu debouncende Wert
 * @param delay Die Verzögerung in Millisekunden
 * @returns Der debounced Wert
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * Hook für die Throttle-Funktion
 * @param callback Die zu throttlende Funktion
 * @param delay Die Verzögerung in Millisekunden
 * @returns Die throttled Funktion
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  const lastCall = useRef<number>(0);
  const lastCallTimer = useRef<NodeJS.Timeout | null>(null);
  
  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCall.current;
      
      if (timeSinceLastCall >= delay) {
        lastCall.current = now;
        callback(...args);
      } else {
        // Bereinige den vorherigen Timer
        if (lastCallTimer.current) {
          clearTimeout(lastCallTimer.current);
        }
        
        // Setze einen neuen Timer für die verbleibende Zeit
        lastCallTimer.current = setTimeout(() => {
          lastCall.current = Date.now();
          callback(...args);
        }, delay - timeSinceLastCall);
      }
    },
    [callback, delay]
  );
}

/**
 * Hook für die Verwendung eines Werts, der nur bei Änderungen aktualisiert wird
 * @param value Der zu beobachtende Wert
 * @returns Der Wert, der nur bei Änderungen aktualisiert wird
 */
export function useConstant<T>(value: T): T {
  const ref = useRef<T>(value);
  return ref.current;
}

/**
 * Hook für die Messung der Renderzeit einer Komponente
 * @param componentName Der Name der Komponente
 * @param enabled Ob die Messung aktiviert ist
 */
export function useRenderTime(componentName: string, enabled: boolean = true) {
  useEffect(() => {
    if (!enabled) return;
    
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      console.log(`[Performance] ${componentName} rendered in ${endTime - startTime}ms`);
    };
  }, [componentName, enabled]);
}

/**
 * HOC für die Messung der Renderzeit einer Komponente
 * @param Component Die zu messende Komponente
 * @param options Optionen für die Messung
 * @returns Die Komponente mit Renderzeitmessung
 */
export function withRenderTimeLogging<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    enabled?: boolean;
    logToConsole?: boolean;
    onRenderComplete?: (renderTime: number) => void;
  } = {}
) {
  const {
    enabled = true,
    logToConsole = true,
    onRenderComplete
  } = options;
  
  function ComponentWithRenderTimeLogging(props: P) {
    const startTimeRef = useRef<number>(0);
    const componentName = Component.displayName || Component.name || "Component";
    
    useEffect(() => {
      if (!enabled) return;
      
      startTimeRef.current = performance.now();
      
      return () => {
        const renderTime = performance.now() - startTimeRef.current;
        
        if (logToConsole) {
          console.log(`[Performance] ${componentName} rendered in ${renderTime}ms`);
        }
        
        if (onRenderComplete) {
          onRenderComplete(renderTime);
        }
      };
    }, []);
    
    return <Component {...props} />;
  }
  
  ComponentWithRenderTimeLogging.displayName = `WithRenderTimeLogging(${Component.displayName || Component.name || "Component"})`;
  
  return ComponentWithRenderTimeLogging;
}
