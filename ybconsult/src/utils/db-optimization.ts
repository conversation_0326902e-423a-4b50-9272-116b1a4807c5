/**
 * Database Optimization Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen zur Optimierung von Datenbankabfragen.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

import { PrismaClient } from "@prisma/client";

// Singleton-Instanz des PrismaClient
let prismaInstance: PrismaClient | undefined;

/**
 * Gibt eine Singleton-Instanz des PrismaClient zurück
 * @returns PrismaClient-Instanz
 */
export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = new PrismaClient({
      log: process.env.NODE_ENV === "development" ? ["query", "error", "warn"] : ["error"],
    });
  }
  return prismaInstance;
}

/**
 * Typen für die Abfrage-Statistiken
 */
export interface QueryStats {
  queryName: string;
  executionTime: number;
  timestamp: Date;
  query: string;
  params?: any;
}

/**
 * Speicher für Abfrage-Statistiken
 */
const queryStats: QueryStats[] = [];

/**
 * Misst die Ausführungszeit einer Datenbankabfrage
 * @param queryName Name der Abfrage
 * @param queryFn Funktion, die die Abfrage ausführt
 * @param params Parameter für die Abfrage
 * @returns Ergebnis der Abfrage
 */
export async function measureQueryPerformance<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  params?: any
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await queryFn();
    const executionTime = performance.now() - startTime;
    
    // Speichere Statistiken
    queryStats.push({
      queryName,
      executionTime,
      timestamp: new Date(),
      query: queryFn.toString(),
      params,
    });
    
    // Logge langsame Abfragen
    if (executionTime > 500) {
      console.warn(`[DB Performance] Slow query detected: ${queryName} took ${executionTime}ms`);
    }
    
    return result;
  } catch (error) {
    const executionTime = performance.now() - startTime;
    console.error(`[DB Error] Query ${queryName} failed after ${executionTime}ms:`, error);
    throw error;
  }
}

/**
 * Gibt die Statistiken für alle Abfragen zurück
 * @returns Abfrage-Statistiken
 */
export function getQueryStats(): QueryStats[] {
  return [...queryStats];
}

/**
 * Gibt die durchschnittliche Ausführungszeit für eine bestimmte Abfrage zurück
 * @param queryName Name der Abfrage
 * @returns Durchschnittliche Ausführungszeit in Millisekunden
 */
export function getAverageQueryTime(queryName: string): number {
  const relevantStats = queryStats.filter(stat => stat.queryName === queryName);
  
  if (relevantStats.length === 0) {
    return 0;
  }
  
  const totalTime = relevantStats.reduce((sum, stat) => sum + stat.executionTime, 0);
  return totalTime / relevantStats.length;
}

/**
 * Optimiert eine Abfrage durch Paginierung
 * @param page Seitennummer (1-basiert)
 * @param pageSize Seitengröße
 * @returns Prisma-Skip und Take-Parameter
 */
export function getPaginationParams(page: number = 1, pageSize: number = 10) {
  const skip = (page - 1) * pageSize;
  return {
    skip,
    take: pageSize,
  };
}

/**
 * Cache für Datenbankabfragen
 */
const queryCache = new Map<string, { data: any; timestamp: number }>();

/**
 * Führt eine Abfrage mit Caching aus
 * @param cacheKey Schlüssel für den Cache
 * @param queryFn Funktion, die die Abfrage ausführt
 * @param ttlMs Time-to-live in Millisekunden
 * @returns Ergebnis der Abfrage
 */
export async function cachedQuery<T>(
  cacheKey: string,
  queryFn: () => Promise<T>,
  ttlMs: number = 60000 // 1 Minute Standard-TTL
): Promise<T> {
  const now = Date.now();
  const cached = queryCache.get(cacheKey);
  
  // Wenn im Cache und noch gültig
  if (cached && now - cached.timestamp < ttlMs) {
    return cached.data as T;
  }
  
  // Führe Abfrage aus und speichere im Cache
  const result = await queryFn();
  queryCache.set(cacheKey, { data: result, timestamp: now });
  
  return result;
}

/**
 * Löscht einen Eintrag aus dem Cache
 * @param cacheKey Schlüssel für den Cache
 */
export function invalidateCache(cacheKey: string): void {
  queryCache.delete(cacheKey);
}

/**
 * Löscht alle Einträge aus dem Cache
 */
export function clearCache(): void {
  queryCache.clear();
}

/**
 * Optimiert eine Abfrage durch Auswahl spezifischer Felder
 * @param fields Array von Feldnamen
 * @returns Prisma-Select-Objekt
 */
export function selectFields<T extends string>(fields: T[]): Record<T, boolean> {
  return fields.reduce((acc, field) => {
    acc[field] = true;
    return acc;
  }, {} as Record<T, boolean>);
}

/**
 * Optimiert eine Abfrage durch Batch-Verarbeitung
 * @param items Items für die Batch-Verarbeitung
 * @param batchSize Größe eines Batches
 * @param processFn Funktion zur Verarbeitung eines Batches
 * @returns Ergebnisse der Batch-Verarbeitung
 */
export async function processBatch<T, R>(
  items: T[],
  batchSize: number,
  processFn: (batch: T[]) => Promise<R[]>
): Promise<R[]> {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await processFn(batch);
    results.push(...batchResults);
  }
  
  return results;
}
