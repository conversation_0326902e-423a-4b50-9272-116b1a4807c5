/**
 * Server-Side Caching Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für serverseitiges Caching.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

/**
 * Einfacher In-Memory-Cache
 */
class MemoryCache {
  private cache: Map<string, { value: any; expires: number | null }> = new Map();

  /**
   * Speichert einen Wert im Cache
   * @param key Cache-Schlüssel
   * @param value Zu speichernder Wert
   * @param ttlMs Time-to-live in Millisekunden (null für unbegrenzte Lebensdauer)
   */
  set(key: string, value: any, ttlMs: number | null = null): void {
    const expires = ttlMs ? Date.now() + ttlMs : null;
    this.cache.set(key, { value, expires });
  }

  /**
   * Ruft einen Wert aus dem Cache ab
   * @param key Cache-Schlüssel
   * @returns Der gespeicherte Wert oder undefined, wenn nicht gefunden oder abgelaufen
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    
    if (!item) return undefined;
    
    // Prüfe, ob der Eintrag abgelaufen ist
    if (item.expires && item.expires < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }
    
    return item.value as T;
  }

  /**
   * Löscht einen Eintrag aus dem Cache
   * @param key Cache-Schlüssel
   * @returns true, wenn der Eintrag gelöscht wurde, sonst false
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Löscht alle abgelaufenen Einträge aus dem Cache
   * @returns Anzahl der gelöschten Einträge
   */
  cleanup(): number {
    const now = Date.now();
    let deletedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expires && item.expires < now) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  /**
   * Löscht alle Einträge aus dem Cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Gibt die Anzahl der Einträge im Cache zurück
   * @returns Anzahl der Einträge
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Gibt alle Schlüssel im Cache zurück
   * @returns Array von Schlüsseln
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }
}

// Singleton-Instanz des Memory-Cache
const memoryCache = new MemoryCache();

/**
 * Gibt die Singleton-Instanz des Memory-Cache zurück
 * @returns Memory-Cache-Instanz
 */
export function getMemoryCache(): MemoryCache {
  return memoryCache;
}

/**
 * Führt eine Funktion mit Caching aus
 * @param key Cache-Schlüssel
 * @param fn Auszuführende Funktion
 * @param ttlMs Time-to-live in Millisekunden
 * @returns Ergebnis der Funktion
 */
export async function withCache<T>(
  key: string,
  fn: () => Promise<T>,
  ttlMs: number = 60000 // 1 Minute Standard-TTL
): Promise<T> {
  const cached = memoryCache.get<T>(key);
  
  if (cached !== undefined) {
    return cached;
  }
  
  const result = await fn();
  memoryCache.set(key, result, ttlMs);
  
  return result;
}

/**
 * Generiert einen Cache-Schlüssel aus einem Präfix und Parametern
 * @param prefix Präfix für den Schlüssel
 * @param params Parameter für den Schlüssel
 * @returns Cache-Schlüssel
 */
export function generateCacheKey(prefix: string, params: Record<string, any> = {}): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${JSON.stringify(params[key])}`)
    .join('&');
  
  return `${prefix}:${sortedParams}`;
}

/**
 * Löscht alle Einträge mit einem bestimmten Präfix aus dem Cache
 * @param prefix Präfix für die zu löschenden Schlüssel
 * @returns Anzahl der gelöschten Einträge
 */
export function invalidateCacheByPrefix(prefix: string): number {
  const keys = memoryCache.keys().filter(key => key.startsWith(`${prefix}:`));
  let count = 0;
  
  for (const key of keys) {
    if (memoryCache.delete(key)) {
      count++;
    }
  }
  
  return count;
}

/**
 * Startet einen regelmäßigen Cache-Cleanup
 * @param intervalMs Intervall in Millisekunden
 * @returns Cleanup-Funktion
 */
export function startCacheCleanup(intervalMs: number = 300000): () => void {
  const interval = setInterval(() => {
    const deletedCount = memoryCache.cleanup();
    if (deletedCount > 0) {
      console.log(`[Cache] Cleaned up ${deletedCount} expired cache entries`);
    }
  }, intervalMs);
  
  return () => clearInterval(interval);
}

/**
 * Dekorator für Next.js API-Routen mit Caching
 * @param handler API-Route-Handler
 * @param options Caching-Optionen
 * @returns API-Route-Handler mit Caching
 */
export function withRouteCache(
  handler: any,
  options: {
    ttlMs?: number;
    keyGenerator?: (req: Request) => string;
    methods?: string[];
  } = {}
) {
  const {
    ttlMs = 60000,
    keyGenerator = (req: Request) => `route:${req.url}`,
    methods = ['GET']
  } = options;
  
  return async function cachedHandler(req: Request, ...args: any[]) {
    const method = req.method?.toUpperCase() || 'GET';
    
    // Nur GET-Anfragen cachen (oder andere konfigurierte Methoden)
    if (!methods.includes(method)) {
      return handler(req, ...args);
    }
    
    const cacheKey = keyGenerator(req);
    
    return withCache(
      cacheKey,
      () => handler(req, ...args),
      ttlMs
    );
  };
}
