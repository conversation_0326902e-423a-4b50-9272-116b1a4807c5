/**
 * Monitoring Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für das Monitoring der Anwendung.
 * Teil der Phase 8: Scalability & Reliability Architecture Review (YM-804)
 */

import { logError, LogLevel } from "./error-handling";
import { checkDatabaseHealth } from "./db-connection";
import os from "os";

/**
 * Interface für Systemmetriken
 */
export interface SystemMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    loadAvg: number[];
  };
  memory: {
    total: number;
    free: number;
    used: number;
    usagePercent: number;
  };
  uptime: number;
}

/**
 * Interface für Anwendungsmetriken
 */
export interface AppMetrics {
  timestamp: string;
  requests: {
    total: number;
    success: number;
    error: number;
    avgResponseTime: number;
  };
  database: {
    connectionCount: number;
    queryCount: number;
    avgQueryTime: number;
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
  };
}

/**
 * Interface für Gesundheitsstatus
 */
export interface HealthStatus {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
  services: {
    app: {
      status: "healthy" | "degraded" | "unhealthy";
      message?: string;
    };
    database: {
      status: "healthy" | "degraded" | "unhealthy";
      responseTime?: number;
      message?: string;
    };
    cache: {
      status: "healthy" | "degraded" | "unhealthy";
      message?: string;
    };
    externalApis?: Record<string, {
      status: "healthy" | "degraded" | "unhealthy";
      responseTime?: number;
      message?: string;
    }>;
  };
}

// Speicher für Metriken
const metricsHistory: {
  system: SystemMetrics[];
  app: AppMetrics[];
} = {
  system: [],
  app: [],
};

// Zähler für Anwendungsmetriken
const appMetricsCounters = {
  requests: {
    total: 0,
    success: 0,
    error: 0,
    responseTimes: [] as number[],
  },
  database: {
    queryCount: 0,
    queryTimes: [] as number[],
  },
  cache: {
    hits: 0,
    misses: 0,
  },
};

/**
 * Sammelt Systemmetriken
 * @returns Systemmetriken
 */
export function collectSystemMetrics(): SystemMetrics {
  const cpus = os.cpus();
  const totalCpuTime = cpus.reduce((acc, cpu) => {
    return acc + Object.values(cpu.times).reduce((sum, time) => sum + time, 0);
  }, 0);
  
  const idleCpuTime = cpus.reduce((acc, cpu) => {
    return acc + cpu.times.idle;
  }, 0);
  
  const cpuUsage = 100 - (idleCpuTime / totalCpuTime) * 100;
  
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;
  
  const metrics: SystemMetrics = {
    timestamp: new Date().toISOString(),
    cpu: {
      usage: cpuUsage,
      loadAvg: os.loadavg(),
    },
    memory: {
      total: totalMemory,
      free: freeMemory,
      used: usedMemory,
      usagePercent: memoryUsagePercent,
    },
    uptime: os.uptime(),
  };
  
  // Speichere Metriken in der Historie
  metricsHistory.system.push(metrics);
  
  // Begrenze die Größe der Historie
  if (metricsHistory.system.length > 100) {
    metricsHistory.system.shift();
  }
  
  return metrics;
}

/**
 * Sammelt Anwendungsmetriken
 * @returns Anwendungsmetriken
 */
export function collectAppMetrics(): AppMetrics {
  const totalRequests = appMetricsCounters.requests.total;
  const successRequests = appMetricsCounters.requests.success;
  const errorRequests = appMetricsCounters.requests.error;
  
  const avgResponseTime = appMetricsCounters.requests.responseTimes.length > 0
    ? appMetricsCounters.requests.responseTimes.reduce((sum, time) => sum + time, 0) / appMetricsCounters.requests.responseTimes.length
    : 0;
  
  const avgQueryTime = appMetricsCounters.database.queryTimes.length > 0
    ? appMetricsCounters.database.queryTimes.reduce((sum, time) => sum + time, 0) / appMetricsCounters.database.queryTimes.length
    : 0;
  
  const cacheHits = appMetricsCounters.cache.hits;
  const cacheMisses = appMetricsCounters.cache.misses;
  const cacheHitRate = (cacheHits + cacheMisses) > 0
    ? cacheHits / (cacheHits + cacheMisses)
    : 0;
  
  const metrics: AppMetrics = {
    timestamp: new Date().toISOString(),
    requests: {
      total: totalRequests,
      success: successRequests,
      error: errorRequests,
      avgResponseTime,
    },
    database: {
      connectionCount: 0, // Wird später aktualisiert
      queryCount: appMetricsCounters.database.queryCount,
      avgQueryTime,
    },
    cache: {
      hits: cacheHits,
      misses: cacheMisses,
      hitRate: cacheHitRate,
    },
  };
  
  // Aktualisiere Datenbankverbindungen
  checkDatabaseHealth().then(health => {
    metrics.database.connectionCount = health.connectionCount;
  }).catch(error => {
    logError(
      LogLevel.ERROR,
      "Failed to check database health",
      error
    );
  });
  
  // Speichere Metriken in der Historie
  metricsHistory.app.push(metrics);
  
  // Begrenze die Größe der Historie
  if (metricsHistory.app.length > 100) {
    metricsHistory.app.shift();
  }
  
  // Setze die Zähler für Antwortzeiten und Abfragezeiten zurück
  appMetricsCounters.requests.responseTimes = [];
  appMetricsCounters.database.queryTimes = [];
  
  return metrics;
}

/**
 * Zeichnet eine Anfrage auf
 * @param responseTime Antwortzeit in Millisekunden
 * @param success Ob die Anfrage erfolgreich war
 */
export function recordRequest(responseTime: number, success: boolean): void {
  appMetricsCounters.requests.total++;
  appMetricsCounters.requests.responseTimes.push(responseTime);
  
  if (success) {
    appMetricsCounters.requests.success++;
  } else {
    appMetricsCounters.requests.error++;
  }
}

/**
 * Zeichnet eine Datenbankabfrage auf
 * @param queryTime Abfragezeit in Millisekunden
 */
export function recordDatabaseQuery(queryTime: number): void {
  appMetricsCounters.database.queryCount++;
  appMetricsCounters.database.queryTimes.push(queryTime);
}

/**
 * Zeichnet einen Cache-Zugriff auf
 * @param hit Ob es ein Cache-Hit war
 */
export function recordCacheAccess(hit: boolean): void {
  if (hit) {
    appMetricsCounters.cache.hits++;
  } else {
    appMetricsCounters.cache.misses++;
  }
}

/**
 * Prüft den Gesundheitsstatus der Anwendung
 * @returns Gesundheitsstatus
 */
export async function checkHealth(): Promise<HealthStatus> {
  const timestamp = new Date().toISOString();
  
  // Prüfe Datenbankgesundheit
  let dbStatus: HealthStatus["services"]["database"] = {
    status: "unhealthy",
    message: "Database health check not performed",
  };
  
  try {
    const dbHealth = await checkDatabaseHealth();
    
    if (dbHealth.healthy) {
      dbStatus = {
        status: "healthy",
        responseTime: dbHealth.responseTime,
      };
    } else {
      dbStatus = {
        status: "unhealthy",
        responseTime: dbHealth.responseTime,
        message: dbHealth.error || "Database health check failed",
      };
    }
  } catch (error) {
    dbStatus = {
      status: "unhealthy",
      message: error instanceof Error ? error.message : String(error),
    };
  }
  
  // Prüfe Cache-Gesundheit (vereinfacht)
  const cacheStatus: HealthStatus["services"]["cache"] = {
    status: "healthy",
  };
  
  // Prüfe App-Gesundheit basierend auf Fehlerraten
  let appStatus: HealthStatus["services"]["app"] = {
    status: "healthy",
  };
  
  const recentMetrics = metricsHistory.app.slice(-5);
  if (recentMetrics.length > 0) {
    const totalRequests = recentMetrics.reduce((sum, m) => sum + m.requests.total, 0);
    const errorRequests = recentMetrics.reduce((sum, m) => sum + m.requests.error, 0);
    
    if (totalRequests > 0) {
      const errorRate = errorRequests / totalRequests;
      
      if (errorRate > 0.1) {
        appStatus = {
          status: "degraded",
          message: `High error rate: ${(errorRate * 100).toFixed(2)}%`,
        };
      }
      
      if (errorRate > 0.25) {
        appStatus = {
          status: "unhealthy",
          message: `Critical error rate: ${(errorRate * 100).toFixed(2)}%`,
        };
      }
    }
  }
  
  // Bestimme Gesamtstatus
  let overallStatus: HealthStatus["status"] = "healthy";
  
  if (dbStatus.status === "unhealthy" || appStatus.status === "unhealthy") {
    overallStatus = "unhealthy";
  } else if (dbStatus.status === "degraded" || appStatus.status === "degraded") {
    overallStatus = "degraded";
  }
  
  return {
    status: overallStatus,
    timestamp,
    services: {
      app: appStatus,
      database: dbStatus,
      cache: cacheStatus,
    },
  };
}
