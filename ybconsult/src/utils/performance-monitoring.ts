/**
 * Performance Monitoring Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen zur Überwachung der Anwendungsleistung.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

/**
 * Typen für Performance-Metriken
 */
export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
}

/**
 * Typen für Web Vitals
 */
export interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'LCP' | 'FCP' | 'TTFB' | 'INP';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

// Speicher für Performance-Metriken
const performanceMetrics: PerformanceMetric[] = [];

/**
 * Speichert eine Performance-Metrik
 * @param name Name der Metrik
 * @param value Wert der Metrik
 */
export function recordMetric(name: string, value: number): void {
  performanceMetrics.push({
    name,
    value,
    timestamp: Date.now(),
  });
  
  // Logge Metriken im Entwicklungsmodus
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${name}: ${value}`);
  }
}

/**
 * Gibt alle gespeicherten Metriken zurück
 * @returns Array von Performance-Metriken
 */
export function getAllMetrics(): PerformanceMetric[] {
  return [...performanceMetrics];
}

/**
 * Gibt die Metriken für einen bestimmten Namen zurück
 * @param name Name der Metrik
 * @returns Array von Performance-Metriken
 */
export function getMetricsByName(name: string): PerformanceMetric[] {
  return performanceMetrics.filter(metric => metric.name === name);
}

/**
 * Misst die Ausführungszeit einer Funktion
 * @param fn Auszuführende Funktion
 * @param metricName Name der Metrik
 * @returns Ergebnis der Funktion
 */
export async function measureExecutionTime<T>(
  fn: () => Promise<T> | T,
  metricName: string
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await fn();
    const executionTime = performance.now() - startTime;
    
    recordMetric(metricName, executionTime);
    
    return result;
  } catch (error) {
    const executionTime = performance.now() - startTime;
    recordMetric(`${metricName}_error`, executionTime);
    throw error;
  }
}

/**
 * Bewertet einen Web Vitals-Wert
 * @param name Name der Metrik
 * @param value Wert der Metrik
 * @returns Bewertung der Metrik
 */
export function rateWebVital(
  name: WebVitalsMetric['name'],
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  // Grenzwerte gemäß Web Vitals
  switch (name) {
    case 'CLS':
      return value <= 0.1 ? 'good' : value <= 0.25 ? 'needs-improvement' : 'poor';
    case 'FID':
      return value <= 100 ? 'good' : value <= 300 ? 'needs-improvement' : 'poor';
    case 'LCP':
      return value <= 2500 ? 'good' : value <= 4000 ? 'needs-improvement' : 'poor';
    case 'FCP':
      return value <= 1800 ? 'good' : value <= 3000 ? 'needs-improvement' : 'poor';
    case 'TTFB':
      return value <= 800 ? 'good' : value <= 1800 ? 'needs-improvement' : 'poor';
    case 'INP':
      return value <= 200 ? 'good' : value <= 500 ? 'needs-improvement' : 'poor';
    default:
      return 'needs-improvement';
  }
}

/**
 * Sammelt Web Vitals-Metriken
 * @param onPerfEntry Callback-Funktion für Performance-Einträge
 */
export function reportWebVitals(
  onPerfEntry?: (metric: WebVitalsMetric) => void
): void {
  if (typeof window !== 'undefined' && onPerfEntry && typeof onPerfEntry === 'function') {
    import('web-vitals').then(({ onCLS, onFID, onLCP, onFCP, onTTFB, onINP }) => {
      onCLS(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'CLS',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('CLS', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('CLS', metric.value);
      });
      onFID(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'FID',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('FID', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('FID', metric.value);
      });
      onLCP(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'LCP',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('LCP', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('LCP', metric.value);
      });
      onFCP(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'FCP',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('FCP', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('FCP', metric.value);
      });
      onTTFB(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'TTFB',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('TTFB', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('TTFB', metric.value);
      });
      onINP(metric => {
        const webVitalsMetric: WebVitalsMetric = {
          name: 'INP',
          value: metric.value,
          delta: metric.delta,
          id: metric.id,
          rating: rateWebVital('INP', metric.value),
        };
        onPerfEntry(webVitalsMetric);
        recordMetric('INP', metric.value);
      });
    });
  }
}

/**
 * Misst die Ressourcenladezeit für eine URL
 * @param url URL der Ressource
 * @returns Promise mit der Ladezeit in Millisekunden
 */
export function measureResourceLoadTime(url: string): Promise<number> {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('measureResourceLoadTime can only be used in the browser'));
      return;
    }
    
    const startTime = performance.now();
    
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
      // Bild laden
      const img = new Image();
      img.onload = () => resolve(performance.now() - startTime);
      img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
      img.src = url;
    } else if (url.match(/\.(css)$/i)) {
      // CSS laden
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = url;
      link.onload = () => {
        resolve(performance.now() - startTime);
        link.remove();
      };
      link.onerror = () => reject(new Error(`Failed to load CSS: ${url}`));
      document.head.appendChild(link);
    } else if (url.match(/\.(js)$/i)) {
      // JavaScript laden
      const script = document.createElement('script');
      script.src = url;
      script.onload = () => {
        resolve(performance.now() - startTime);
        script.remove();
      };
      script.onerror = () => reject(new Error(`Failed to load script: ${url}`));
      document.head.appendChild(script);
    } else {
      // Andere Ressourcen über Fetch laden
      fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.blob();
        })
        .then(() => resolve(performance.now() - startTime))
        .catch(error => reject(error));
    }
  });
}
