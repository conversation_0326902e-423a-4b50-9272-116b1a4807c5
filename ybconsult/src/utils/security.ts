/**
 * Security Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für die Sicherheit der Anwendung.
 * Teil der Phase 8: Security Hardening & GDPR Compliance Audit (YM-803)
 */

import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import crypto from "crypto";

/**
 * Generiert einen sicheren Token
 * @param length Länge des Tokens
 * @returns Sicherer Token
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString("hex");
}

/**
 * Hasht einen String mit einem Salt
 * @param data Zu hashender String
 * @param salt Salt für den Hash (optional)
 * @returns Hash und Salt
 */
export function hashData(
  data: string,
  salt: string = crypto.randomBytes(16).toString("hex")
): { hash: string; salt: string } {
  const hmac = crypto.createHmac("sha256", salt);
  hmac.update(data);
  const hash = hmac.digest("hex");
  return { hash, salt };
}

/**
 * Verifiziert einen Hash
 * @param data Zu verifizierender String
 * @param hash Zu verifizierender Hash
 * @param salt Salt für den Hash
 * @returns true, wenn der Hash korrekt ist, sonst false
 */
export function verifyHash(data: string, hash: string, salt: string): boolean {
  const { hash: computedHash } = hashData(data, salt);
  return crypto.timingSafeEqual(
    Buffer.from(computedHash, "hex"),
    Buffer.from(hash, "hex")
  );
}

/**
 * Verschlüsselt Daten
 * @param data Zu verschlüsselnde Daten
 * @param key Schlüssel für die Verschlüsselung
 * @returns Verschlüsselte Daten und IV
 */
export function encryptData(
  data: string,
  key: string
): { encryptedData: string; iv: string } {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(
    "aes-256-cbc",
    Buffer.from(key.padEnd(32, "0").slice(0, 32)),
    iv
  );
  let encrypted = cipher.update(data, "utf8", "hex");
  encrypted += cipher.final("hex");
  return { encryptedData: encrypted, iv: iv.toString("hex") };
}

/**
 * Entschlüsselt Daten
 * @param encryptedData Verschlüsselte Daten
 * @param key Schlüssel für die Entschlüsselung
 * @param iv IV für die Entschlüsselung
 * @returns Entschlüsselte Daten
 */
export function decryptData(
  encryptedData: string,
  key: string,
  iv: string
): string {
  const decipher = crypto.createDecipheriv(
    "aes-256-cbc",
    Buffer.from(key.padEnd(32, "0").slice(0, 32)),
    Buffer.from(iv, "hex")
  );
  let decrypted = decipher.update(encryptedData, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

/**
 * Validiert Eingabedaten mit Zod
 * @param schema Zod-Schema
 * @param data Zu validierende Daten
 * @returns Validierte Daten oder null bei Fehler
 */
export function validateInput<T extends z.ZodType>(
  schema: T,
  data: unknown
): z.infer<T> | null {
  try {
    return schema.parse(data);
  } catch (error) {
    console.error("Validation error:", error);
    return null;
  }
}

/**
 * Middleware für die Validierung von API-Anfragen
 * @param schema Zod-Schema
 * @returns Middleware-Funktion
 */
export function validateApiInput<T extends z.ZodType>(schema: T) {
  return async (req: NextRequest) => {
    try {
      const body = await req.json();
      const validatedData = schema.parse(body);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: {
            message: "Validation failed",
            errors: error.errors,
          },
        };
      }
      return {
        success: false,
        error: {
          message: "Invalid request",
        },
      };
    }
  };
}

/**
 * Sanitiert HTML-Eingaben
 * @param html HTML-String
 * @returns Sanitierter HTML-String
 */
export function sanitizeHtml(html: string): string {
  // Einfache Implementierung, in Produktion sollte eine Bibliothek wie DOMPurify verwendet werden
  return html
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Prüft, ob ein String potenziell schädlichen Code enthält
 * @param input Zu prüfender String
 * @returns true, wenn der String potenziell schädlichen Code enthält, sonst false
 */
export function containsMaliciousCode(input: string): boolean {
  const maliciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // <script> Tags
    /javascript\s*:/gi, // javascript: URLs
    /on\w+\s*=/gi, // Event-Handler (onclick, onload, etc.)
    /data\s*:/gi, // data: URLs
  ];
  
  return maliciousPatterns.some(pattern => pattern.test(input));
}

/**
 * Generiert sichere HTTP-Header
 * @returns Objekt mit sicheren HTTP-Headern
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    "Content-Security-Policy":
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self'; frame-src 'self'; object-src 'none'; base-uri 'self';",
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    "Strict-Transport-Security": "max-age=63072000; includeSubDomains; preload",
  };
}

/**
 * Middleware für die Anwendung von Sicherheits-Headern
 * @param req NextRequest
 * @returns NextResponse mit Sicherheits-Headern
 */
export function applySecurityHeaders(req: NextRequest): NextResponse {
  const response = NextResponse.next();
  const headers = getSecurityHeaders();
  
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Prüft, ob eine E-Mail-Adresse gültig ist
 * @param email E-Mail-Adresse
 * @returns true, wenn die E-Mail-Adresse gültig ist, sonst false
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

/**
 * Anonymisiert eine E-Mail-Adresse für GDPR-Compliance
 * @param email E-Mail-Adresse
 * @returns Anonymisierte E-Mail-Adresse
 */
export function anonymizeEmail(email: string): string {
  const [username, domain] = email.split("@");
  const anonymizedUsername =
    username.charAt(0) + "*".repeat(username.length - 2) + username.charAt(username.length - 1);
  return `${anonymizedUsername}@${domain}`;
}

/**
 * Anonymisiert eine IP-Adresse für GDPR-Compliance
 * @param ip IP-Adresse
 * @returns Anonymisierte IP-Adresse
 */
export function anonymizeIp(ip: string): string {
  // IPv4
  if (ip.includes(".")) {
    const parts = ip.split(".");
    return `${parts[0]}.${parts[1]}.0.0`;
  }
  // IPv6
  if (ip.includes(":")) {
    const parts = ip.split(":");
    return `${parts[0]}:${parts[1]}:${parts[2]}:${parts[3]}:0:0:0:0`;
  }
  return ip;
}
