/**
 * Test Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen für Tests.
 * Teil der Phase 8: Comprehensive Automated Testing (YM-805)
 */

import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ThemeProvider } from "next-themes";

/**
 * Interface für erweiterte Render-Optionen
 */
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  theme?: "light" | "dark";
  route?: string;
  initialState?: Record<string, any>;
}

/**
 * Wrapper-Komponente für Tests
 */
function AllTheProviders({
  children,
  theme = "light",
  initialState = {},
}: {
  children: React.ReactNode;
  theme?: "light" | "dark";
  initialState?: Record<string, any>;
}) {
  // Hier können weitere Provider hinzugefügt werden, z.B. für Redux, Context, etc.
  return (
    <ThemeProvider defaultTheme={theme} enableSystem={false}>
      {children}
    </ThemeProvider>
  );
}

/**
 * Erweiterte render-Funktion mit Providern
 * @param ui Komponente, die gerendert werden soll
 * @param options Render-Optionen
 * @returns Ergebnis der render-Funktion
 */
export function customRender(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { theme = "light", initialState = {}, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: (props) => (
      <AllTheProviders {...props} theme={theme} initialState={initialState} />
    ),
    ...renderOptions,
  });
}

/**
 * Erstellt einen Mock für Next.js useRouter
 * @param props Eigenschaften für den Router-Mock
 * @returns Mock für useRouter
 */
export function mockRouter(props: {
  pathname?: string;
  route?: string;
  query?: Record<string, string>;
  asPath?: string;
  push?: jest.Mock;
  replace?: jest.Mock;
  back?: jest.Mock;
} = {}) {
  const {
    pathname = "/",
    route = "/",
    query = {},
    asPath = "/",
    push = jest.fn(),
    replace = jest.fn(),
    back = jest.fn(),
  } = props;
  
  return {
    pathname,
    route,
    query,
    asPath,
    push,
    replace,
    back,
    prefetch: jest.fn(() => Promise.resolve()),
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
    isFallback: false,
    isReady: true,
    isPreview: false,
  };
}

/**
 * Erstellt einen Mock für Next.js useParams
 * @param params Parameter für den Params-Mock
 * @returns Mock für useParams
 */
export function mockParams(params: Record<string, string> = {}) {
  return params;
}

/**
 * Erstellt einen Mock für Next.js useSearchParams
 * @param params Suchparameter für den SearchParams-Mock
 * @returns Mock für useSearchParams
 */
export function mockSearchParams(params: Record<string, string> = {}) {
  return {
    get: (key: string) => params[key] || null,
    getAll: (key: string) => (params[key] ? [params[key]] : []),
    has: (key: string) => key in params,
    forEach: (callback: (value: string, key: string) => void) => {
      Object.entries(params).forEach(([key, value]) => callback(value, key));
    },
    entries: () => Object.entries(params)[Symbol.iterator](),
    keys: () => Object.keys(params)[Symbol.iterator](),
    values: () => Object.values(params)[Symbol.iterator](),
    toString: () => new URLSearchParams(params).toString(),
  };
}

/**
 * Erstellt einen Mock für fetch
 * @param response Antwort für den Fetch-Mock
 * @returns Mock für fetch
 */
export function mockFetch(response: any) {
  return jest.fn().mockImplementation(() =>
    Promise.resolve({
      ok: true,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response)),
      status: 200,
      statusText: "OK",
      headers: new Headers(),
    })
  );
}

/**
 * Erstellt einen Mock für fetch mit Fehler
 * @param status HTTP-Statuscode
 * @param statusText HTTP-Statustext
 * @param errorData Fehlerdaten
 * @returns Mock für fetch mit Fehler
 */
export function mockFetchError(
  status: number = 500,
  statusText: string = "Internal Server Error",
  errorData: any = { message: "Something went wrong" }
) {
  return jest.fn().mockImplementation(() =>
    Promise.resolve({
      ok: false,
      json: () => Promise.resolve(errorData),
      text: () => Promise.resolve(JSON.stringify(errorData)),
      status,
      statusText,
      headers: new Headers(),
    })
  );
}

/**
 * Erstellt einen Mock für localStorage
 * @returns Mock für localStorage
 */
export function mockLocalStorage() {
  const store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach((key) => {
        delete store[key];
      });
    }),
    key: jest.fn((index: number) => Object.keys(store)[index] || null),
    length: Object.keys(store).length,
  };
}

/**
 * Wartet auf eine bestimmte Zeit
 * @param ms Zeit in Millisekunden
 * @returns Promise, das nach der angegebenen Zeit aufgelöst wird
 */
export function wait(ms: number = 0): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Erstellt einen Mock für ResizeObserver
 */
export function mockResizeObserver() {
  class ResizeObserverMock {
    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
  }
  
  window.ResizeObserver = ResizeObserverMock as any;
}

/**
 * Erstellt einen Mock für IntersectionObserver
 */
export function mockIntersectionObserver() {
  class IntersectionObserverMock {
    constructor(callback: IntersectionObserverCallback) {
      this.callback = callback;
    }
    
    callback: IntersectionObserverCallback;
    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
    
    // Hilfsmethode zum Simulieren von Intersections
    simulateIntersection(entries: IntersectionObserverEntry[]) {
      this.callback(entries, this);
    }
  }
  
  window.IntersectionObserver = IntersectionObserverMock as any;
}

// Re-export alles von @testing-library/react und @testing-library/user-event
export * from "@testing-library/react";
export { userEvent };
