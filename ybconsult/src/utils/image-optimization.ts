/**
 * Image Optimization Utilities
 * 
 * Diese Datei enthält Hilfsfunktionen zur Optimierung von Bildern für bessere Performance.
 * Teil der Phase 8: Performance Optimization & Core Web Vitals (YM-802)
 */

import { ImageProps } from "next/image";

/**
 * Standardwerte für optimierte Bilder
 */
export const DEFAULT_QUALITY = 80;
export const DEFAULT_PLACEHOLDER = "blur";
export const DEFAULT_BLUR_DATA_URL = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8H/QAAAABJRU5ErkJggg==";

/**
 * Generiert optimierte Image-Props für next/image
 * @param src Bildquelle (URL oder lokaler Pfad)
 * @param alt Alternativer Text für das Bild
 * @param options Zusätzliche Optionen für das Bild
 * @returns Optimierte Image-Props
 */
export function getOptimizedImageProps(
  src: string,
  alt: string,
  options?: {
    width?: number;
    height?: number;
    quality?: number;
    priority?: boolean;
    placeholder?: "blur" | "empty";
    blurDataURL?: string;
    sizes?: string;
  }
): Partial<ImageProps> {
  // Standardwerte für Bildgrößen basierend auf Bildtyp
  const isHero = options?.width && options.width > 1000;
  const isThumbnail = options?.width && options.width < 300;

  // Generiere optimierte Image-Props
  return {
    src,
    alt,
    width: options?.width || (isHero ? 1920 : isThumbnail ? 300 : 800),
    height: options?.height,
    quality: options?.quality || DEFAULT_QUALITY,
    priority: options?.priority || isHero, // Hero-Bilder sollten Priorität haben
    placeholder: options?.placeholder || DEFAULT_PLACEHOLDER,
    blurDataURL: options?.blurDataURL || DEFAULT_BLUR_DATA_URL,
    sizes: options?.sizes || getResponsiveSizes(isHero, isThumbnail),
    loading: options?.priority ? "eager" : "lazy",
  };
}

/**
 * Generiert responsive Größenangaben für verschiedene Bildtypen
 * @param isHero Ob es sich um ein Hero-Bild handelt
 * @param isThumbnail Ob es sich um ein Thumbnail handelt
 * @returns Responsive Größenangaben für das sizes-Attribut
 */
function getResponsiveSizes(isHero: boolean, isThumbnail: boolean): string {
  if (isHero) {
    return "(max-width: 640px) 100vw, (max-width: 1024px) 90vw, 1200px";
  } else if (isThumbnail) {
    return "(max-width: 640px) 33vw, (max-width: 1024px) 25vw, 300px";
  } else {
    return "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 800px";
  }
}

/**
 * Generiert eine Basis64-kodierte Blur-Placeholder für ein Bild
 * (In einer echten Implementierung würde dies das Bild tatsächlich verarbeiten)
 * @param color Farbe für den Placeholder
 * @returns Base64-kodierter Blur-Placeholder
 */
export function generateBlurPlaceholder(color: string = "#F0F0F0"): string {
  // In einer echten Implementierung würde hier das Bild verarbeitet werden
  // Dies ist nur ein einfacher Platzhalter für Demonstrationszwecke
  return DEFAULT_BLUR_DATA_URL;
}

/**
 * Optimiert Bilder für verschiedene Geräte und Bildschirmgrößen
 * @param imagePath Pfad zum Bild
 * @returns Ein Objekt mit verschiedenen Bildgrößen für verschiedene Geräte
 */
export function getResponsiveImageSources(imagePath: string): {
  mobile: string;
  tablet: string;
  desktop: string;
} {
  // Extrahiere Dateiname und Erweiterung
  const lastDotIndex = imagePath.lastIndexOf(".");
  const basePath = imagePath.substring(0, lastDotIndex);
  const extension = imagePath.substring(lastDotIndex);

  // Generiere Pfade für verschiedene Geräte
  return {
    mobile: `${basePath}-mobile${extension}`,
    tablet: `${basePath}-tablet${extension}`,
    desktop: imagePath,
  };
}

/**
 * Prüft, ob ein Bild im Viewport ist und geladen werden sollte
 * @param element Das zu prüfende Element
 * @param threshold Der Schwellenwert für die Sichtbarkeit (0-1)
 * @returns Ein Promise, das resolved, wenn das Element sichtbar ist
 */
export function isElementInViewport(
  element: HTMLElement,
  threshold: number = 0.1
): Promise<boolean> {
  return new Promise((resolve) => {
    // Verwende Intersection Observer API für bessere Performance
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            observer.disconnect();
            resolve(true);
          }
        });
      },
      { threshold }
    );

    observer.observe(element);

    // Cleanup-Funktion
    return () => {
      observer.disconnect();
    };
  });
}
