// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Defines the data source, in this case PostgreSQL.
// The URL is typically set via an environment variable.
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Defines the Prisma Client generator.
// This generates the TypeScript types for your database models.
generator client {
  provider = "prisma-client-js"
}

// -----------------------------------------------------------------------------
// ENUMS - Define all enumerated types used across the schema.
// -----------------------------------------------------------------------------

/// User roles within the system.
enum Role {
  CLIENT // Business client
  DRIVER // YoungMover driver
  ADMIN  // Platform administrator
}

/// Status of a user account.
enum UserStatus {
  PENDING_EMAIL_VERIFICATION // User registered, email not yet verified
  PENDING_ADMIN_APPROVAL     // Email verified, awaiting admin approval (e.g., for drivers)
  ACTIVE                     // User account is active and can be used
  SUSPENDED                  // User account is temporarily suspended
  DELETED                    // User account is soft-deleted
}

/// Type of vehicle being transported.
enum VehicleType {
  CAR
  VAN
  TRUCK
  MOTORCYCLE
  OTHER
}

/// Status of a transport order.
enum OrderStatus {
  DRAFT                      // Order created by client, not yet submitted
  PENDING_CONFIRMATION       // Order submitted, awaiting platform/admin confirmation or pricing
  AWAITING_DRIVER_ASSIGNMENT // Order confirmed, ready for driver assignment/bidding
  DRIVER_ASSIGNED            // A driver has been assigned, awaiting driver acceptance
  ACCEPTED_BY_DRIVER         // Driver has accepted the assignment
  EN_ROUTE_TO_PICKUP         // Driver is on the way to pick up the vehicle
  VEHICLE_PICKED_UP          // Driver has picked up the vehicle
  EN_ROUTE_TO_DELIVERY       // Driver is transporting the vehicle to the delivery location
  VEHICLE_DELIVERED          // Vehicle has been delivered
  COMPLETED                  // Order is fully completed (e.g., after payment, ratings)
  CANCELLED_BY_CLIENT        // Order cancelled by the client
  CANCELLED_BY_ADMIN         // Order cancelled by an admin
  DISPUTED                   // Order is under dispute
}

/// Status of a bid made by a driver for an order.
enum BidStatus {
  SUBMITTED // Bid has been submitted by the driver
  WITHDRAWN // Bid was withdrawn by the driver
  REJECTED  // Bid was rejected (e.g., by client or admin)
  ACCEPTED  // Bid was accepted, leading to potential assignment
}

/// Status of a driver's assignment to an order.
enum AssignmentStatus {
  PENDING_DRIVER_ACCEPTANCE // Driver has been assigned, awaiting their confirmation
  ACCEPTED_BY_DRIVER        // Driver has accepted the assignment
  DECLINED_BY_DRIVER        // Driver has declined the assignment
  IN_PROGRESS               // Driver is actively working on the order (pickup/delivery)
  COMPLETED                 // Driver has completed their part of the order
  CANCELLED                 // Assignment was cancelled
}

/// Type of document uploaded by a user (typically a driver).
enum DocumentType {
  DRIVING_LICENSE
  ID_CARD
  INSURANCE_POLICY
  TRANSPORT_LICENSE // Gewerbeschein / Transportlizenz
  VEHICLE_REGISTRATION
  COMPANY_REGISTRATION // For business clients, if needed
  OTHER
}

/// Verification status of an uploaded document.
enum DocumentVerificationStatus {
  PENDING  // Document uploaded, awaiting verification
  VERIFIED // Document has been verified by an admin
  REJECTED // Document was rejected
}

/// Status of an invoice.
enum InvoiceStatus {
  DRAFT    // Invoice created but not yet finalized or sent
  SENT     // Invoice sent to the client
  PAID     // Invoice has been fully paid
  PARTIALLY_PAID // Invoice has been partially paid
  OVERDUE  // Invoice payment is overdue
  CANCELLED // Invoice has been cancelled
  VOID     // Invoice has been voided
}

/// Status of a payment.
enum PaymentStatus {
  PENDING   // Payment initiated but not yet confirmed
  SUCCEEDED // Payment was successful
  FAILED    // Payment failed
  REFUNDED  // Payment was refunded
  PARTIALLY_REFUNDED // Payment was partially refunded
}

/// Availability status of a driver.
enum AvailabilityStatus {
  AVAILABLE    // Driver is available for new orders
  UNAVAILABLE  // Driver is not currently available
  ON_ASSIGNMENT // Driver is currently on an assignment
}

// -----------------------------------------------------------------------------
// MODELS - Define database tables and their relationships.
// -----------------------------------------------------------------------------

/// Represents a user in the system (Client, Driver, or Admin).
model User {
  id                String    @id @default(cuid())
  email             String    @unique // User's email address, used for login
  hashedPassword    String // Hashed password for security
  role              Role // User's role (CLIENT, DRIVER, ADMIN)
  status            UserStatus @default(PENDING_EMAIL_VERIFICATION) // Current status of the user account
  emailVerifiedAt   DateTime? // Timestamp when the user's email was verified
  lastLoginAt       DateTime? // Timestamp of the user's last login

  createdAt         DateTime  @default(now()) // Timestamp of user creation
  updatedAt         DateTime  @updatedAt // Timestamp of last user update
  deletedAt         DateTime? // Timestamp for soft deletion

  // Relations
  clientProfile     UserProfileClient? // Link to client-specific profile (if role is CLIENT)
  driverProfile     UserProfileDriver? // Link to driver-specific profile (if role is DRIVER)
  
  sentMessages      Message[] @relation("SentMessages") // Messages sent by this user
  receivedMessages  Message[] @relation("ReceivedMessages") // Messages received by this user
  
  givenRatings      Rating[]  @relation("GivenRatings") // Ratings given by this user
  receivedRatings   Rating[]  @relation("ReceivedRatings") // Ratings received by this user
  
  notifications     Notification[] // Notifications for this user
  
  invoicesAsClient  Invoice[] @relation("ClientInvoices") // Invoices issued to this user (if client)
  paymentsAsClient  Payment[] @relation("ClientPayments") // Payments made by this user (if client)

  adminActions      AdminLog[] // Log of actions performed by this user (if admin)

  @@index([email])
  @@index([role])
  @@index([status])
}

/// Profile details specific to Business Clients.
model UserProfileClient {
  id                  String   @id @default(cuid())
  userId              String   @unique // Foreign key to the User model
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  companyName         String
  contactPersonName   String
  addressLine1        String
  addressLine2        String?
  city                String
  postalCode          String
  country             String   // Consider using an ENUM or a separate Country model if fixed list
  vatId               String?  @unique
  phoneNumber         String?

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  orders              Order[] // Orders created by this client

  @@index([userId])
  @@index([companyName])
}

/// Profile details specific to Drivers (YoungMovers).
model UserProfileDriver {
  id                  String   @id @default(cuid())
  userId              String   @unique // Foreign key to the User model
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  firstName           String
  lastName            String
  phoneNumber         String   @unique
  addressLine1        String
  addressLine2        String?
  city                String
  postalCode          String
  country             String
  dateOfBirth         DateTime?
  
  availabilityStatus  AvailabilityStatus @default(AVAILABLE) // Driver's current availability
  transportationTypes Json? // e.g., ["own_axis", "trailer_transport"] - types of transport driver can do
  licensePlate        String?  // If driver uses their own vehicle for transport tasks (e.g. trailer)
  
  averageRating       Float?   @default(0) // Calculated average rating from completed orders
  completedTransfers  Int      @default(0) // Number of successfully completed transfers

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relations
  documents           Document[]   // Documents uploaded by this driver
  bids                Bid[]        // Bids made by this driver
  assignments         Assignment[] // Orders assigned to this driver

  @@index([userId])
  @@index([availabilityStatus])
}

/// Represents the vehicle being transported in an order.
model Vehicle {
  id             String      @id @default(cuid())
  orderId        String      @unique // Each vehicle belongs to one order
  order          Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  vin            String      @unique // Vehicle Identification Number
  make           String      // e.g., Volkswagen
  model          String      // e.g., Golf
  year           Int?        // Manufacturing year
  color          String?
  vehicleType    VehicleType // Type of vehicle (CAR, VAN, etc.)
  licensePlate   String?     // License plate of the vehicle being transported
  notes          String?     // Any specific notes about the vehicle

  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([vin])
  @@index([orderId])
}

/// Represents a transport order created by a client.
model Order {
  id                  String      @id @default(cuid())
  orderReference      String      @unique @default(cuid()) // Public-facing unique order ID
  
  clientId            String      // Foreign key to the UserProfileClient (or User with role CLIENT)
  client              UserProfileClient @relation(fields: [clientId], references: [id])
  
  status              OrderStatus @default(DRAFT) // Current status of the order

  // Pickup Details
  pickupAddressLine1    String
  pickupAddressLine2    String?
  pickupCity            String
  pickupPostalCode      String
  pickupCountry         String
  pickupLatitude        Float?
  pickupLongitude       Float?
  pickupContactName     String?
  pickupContactPhone    String?
  pickupDateFrom        DateTime    // Desired pickup start date/time
  pickupDateTo          DateTime    // Desired pickup end date/time

  // Delivery Details
  deliveryAddressLine1  String
  deliveryAddressLine2  String?
  deliveryCity          String
  deliveryPostalCode    String
  deliveryCountry       String
  deliveryLatitude      Float?
  deliveryLongitude     Float?
  deliveryContactName   String?
  deliveryContactPhone  String?
  deliveryDateFrom      DateTime    // Desired delivery start date/time
  deliveryDateTo        DateTime    // Desired delivery end date/time

  specialInstructions String?     // Any special instructions for the order
  
  // Pricing & Payment
  estimatedPrice      Decimal?    // Initial estimated price
  finalPrice          Decimal?    // Final price after all calculations/agreements
  currency            String      @default("EUR") // Currency for the price

  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt
  deletedAt           DateTime?   // For soft deletion

  // Relations
  vehicle             Vehicle?     // The vehicle to be transported in this order
  bids                Bid[]        // Bids received for this order
  assignment          Assignment?  // The driver assignment for this order
  messages            Message[]    // Messages related to this order
  ratings             Rating[]     // Ratings related to this order
  invoice             Invoice?     // Invoice for this order
  orderEvents         OrderEvent[] // History of events for this order

  @@index([clientId])
  @@index([status])
  @@index([pickupCity])
  @@index([deliveryCity])
}

/// Represents a bid made by a driver for an order.
model Bid {
  id          String    @id @default(cuid())
  orderId     String    // Foreign key to the Order model
  order       Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  driverId    String    // Foreign key to the UserProfileDriver
  driver      UserProfileDriver @relation(fields: [driverId], references: [id])
  
  bidAmount   Decimal?  // Amount the driver bids for the order (if applicable)
  currency    String    @default("EUR")
  notes       String?   // Any notes from the driver with the bid
  status      BidStatus @default(SUBMITTED) // Current status of the bid

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([orderId, driverId]) // A driver can only bid once per order
  @@index([orderId])
  @@index([driverId])
  @@index([status])
}

/// Represents the assignment of a driver to an order.
model Assignment {
  id            String   @id @default(cuid())
  orderId       String   @unique // Each order can only have one active assignment
  order         Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  driverId      String   // Foreign key to the UserProfileDriver
  driver        UserProfileDriver @relation(fields: [driverId], references: [id])
  
  assignedAt    DateTime @default(now()) // Timestamp when the assignment was made
  status        AssignmentStatus @default(PENDING_DRIVER_ACCEPTANCE) // Status of the assignment

  // Timestamps for key events in the assignment lifecycle
  driverAcceptedAt    DateTime?
  enRouteToPickupAt   DateTime?
  vehiclePickedUpAt   DateTime?
  enRouteToDeliveryAt DateTime?
  vehicleDeliveredAt  DateTime?
  completedAt         DateTime? // When the driver confirms completion from their side

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([orderId])
  @@index([driverId])
  @@index([status])
}

/// Represents documents uploaded by users (typically drivers for verification).
model Document {
  id                  String    @id @default(cuid())
  driverId            String    // Foreign key to the UserProfileDriver
  driver              UserProfileDriver @relation(fields: [driverId], references: [id], onDelete: Cascade)
  
  documentType        DocumentType // Type of document (e.g., DRIVING_LICENSE)
  fileName            String    // Original name of the uploaded file
  fileUrl             String    // URL to the stored file (e.g., S3 link)
  mimeType            String    // Mime type of the file
  fileSize            Int       // File size in bytes
  
  uploadedAt          DateTime  @default(now())
  expiresAt           DateTime? // Expiry date of the document, if applicable
  
  verificationStatus  DocumentVerificationStatus @default(PENDING)
  verifiedAt          DateTime? // Timestamp when an admin verified the document
  verifiedByUserId    String?   // ID of the admin who verified it
  // verifiedByUser   User?     @relation("VerifiedDocuments", fields: [verifiedByUserId], references: [id]) // Needs careful thought on relation naming if User model is involved
  verificationNotes   String?   // Notes from admin regarding verification (e.g., reason for rejection)

  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  @@index([driverId])
  @@index([documentType])
  @@index([verificationStatus])
}

/// Represents a message exchanged between users, typically related to an order.
model Message {
  id            String   @id @default(cuid())
  orderId       String?  // Foreign key to the Order, if message is order-specific
  order         Order?   @relation(fields: [orderId], references: [id], onDelete:SetNull) // SetNull if order is deleted but messages might be kept
  
  senderId      String   // Foreign key to the User who sent the message
  sender        User     @relation("SentMessages", fields: [senderId], references: [id])
  
  receiverId    String   // Foreign key to the User who should receive the message
  receiver      User     @relation("ReceivedMessages", fields: [receiverId], references: [id])
  
  content       String   // Content of the message
  isRead        Boolean  @default(false) // Whether the receiver has read the message
  readAt        DateTime? // Timestamp when the message was read

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([orderId])
  @@index([senderId])
  @@index([receiverId])
  @@index([isRead])
}

/// Represents a rating given by one user to another, typically after an order is completed.
model Rating {
  id            String   @id @default(cuid())
  orderId       String   // Foreign key to the Order for which the rating is given
  order         Order    @relation(fields: [orderId], references: [id], onDelete:Cascade)
  
  raterId       String   // Foreign key to the User who is giving the rating
  rater         User     @relation("GivenRatings", fields: [raterId], references: [id])
  
  ratedUserId   String   // Foreign key to the User who is being rated
  ratedUser     User     @relation("ReceivedRatings", fields: [ratedUserId], references: [id])
  
  ratingValue   Int      // The rating value (e.g., 1 to 5 stars)
  comment       String?  // Optional comment with the rating

  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([orderId, raterId, ratedUserId]) // Ensure a user can rate another user only once per order
  @@index([orderId])
  @@index([raterId])
  @@index([ratedUserId])
  @@index([ratingValue])
}

/// Represents an invoice generated for an order.
model Invoice {
  id                  String        @id @default(cuid())
  orderId             String        @unique // Each order has one invoice
  order               Order         @relation(fields: [orderId], references: [id], onDelete:Cascade)
  
  clientId            String        // Foreign key to the User (Client) to whom the invoice is issued
  client              User          @relation("ClientInvoices", fields: [clientId], references: [id])
  
  invoiceNumber       String        @unique // Unique invoice number/identifier
  issueDate           DateTime      // Date when the invoice was issued
  dueDate             DateTime      // Date when the payment for the invoice is due
  
  amount              Decimal       // Total amount of the invoice
  currency            String        @default("EUR")
  status              InvoiceStatus @default(DRAFT) // Current status of the invoice
  
  pdfUrl              String?       // URL to the generated PDF of the invoice
  lineItems           Json?         // Detailed breakdown of invoice items (e.g., [{description: "Base Fee", amount: 100.00}, ...])

  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Relations
  payment             Payment?      // Payment associated with this invoice

  @@index([orderId])
  @@index([clientId])
  @@index([status])
  @@index([issueDate])
  @@index([dueDate])
}

/// Represents a payment made for an invoice or order.
model Payment {
  id                  String        @id @default(cuid())
  invoiceId           String?       @unique // Link to an invoice, if applicable
  invoice             Invoice?      @relation(fields: [invoiceId], references: [id])
  
  orderId             String        // Foreign key to the Order for which payment is made (even if via invoice)
  order               Order         @relation(fields: [orderId], references: [id])

  clientId            String        // Foreign key to the User (Client) who made the payment
  client              User          @relation("ClientPayments", fields: [clientId], references: [id])
  
  amount              Decimal       // Amount paid
  currency            String        @default("EUR")
  paymentMethod       String        // e.g., "stripe", "credit_card", "bank_transfer"
  transactionId       String?       @unique // Unique transaction ID from the payment gateway
  status              PaymentStatus @default(PENDING) // Current status of the payment
  
  paidAt              DateTime?     // Timestamp when the payment was successfully processed
  paymentGatewayData  Json?         // Any additional data from the payment gateway (e.g., Stripe charge ID)

  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  @@index([invoiceId])
  @@index([orderId])
  @@index([clientId])
  @@index([status])
  @@index([transactionId])
}

/// Represents notifications sent to users.
model Notification {
  id          String    @id @default(cuid())
  userId      String    // Foreign key to the User who receives the notification
  user        User      @relation(fields: [userId], references: [id], onDelete:Cascade)
  
  type        String    // Type of notification (e.g., "NEW_ORDER_BID", "ORDER_STATUS_UPDATE")
  title       String
  message     String
  isRead      Boolean   @default(false)
  readAt      DateTime?
  
  relatedEntityType String? // e.g., "Order", "Bid"
  relatedEntityId   String? // ID of the related entity

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([userId])
  @@index([isRead])
  @@index([type])
}

/// Logs significant events related to an order.
model OrderEvent {
  id          String    @id @default(cuid())
  orderId     String
  order       Order     @relation(fields: [orderId], references: [id], onDelete:Cascade)
  
  eventTimestamp DateTime @default(now())
  eventType   String    // e.g., "STATUS_CHANGED", "DRIVER_ASSIGNED", "NOTE_ADDED"
  description String    // Detailed description of the event
  actorType   String?   // "USER", "SYSTEM", "DRIVER", "CLIENT"
  actorId     String?   // ID of the user/system component that triggered the event
  eventData   Json?     // Additional data related to the event (e.g., old_status, new_status)

  @@index([orderId])
  @@index([eventType])
}

/// Logs actions performed by administrators.
model AdminLog {
  id          String    @id @default(cuid())
  adminUserId String    // ID of the admin performing the action
  adminUser   User      @relation(fields: [adminUserId], references: [id])
  
  action      String    // e.g., "USER_SUSPENDED", "ORDER_CANCELLED", "DOCUMENT_VERIFIED"
  targetEntityType String? // e.g., "User", "Order", "Document"
  targetEntityId   String?
  details     Json?     // Additional details about the action
  timestamp   DateTime  @default(now())

  @@index([adminUserId])
  @@index([action])
  @@index([targetEntityType, targetEntityId])
}