// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  // Output wird auf den Standardwert node_modules/.prisma/client gesetzt,
  // damit der Import über @prisma/client funktioniert.
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  BUSINESS_CLIENT
  DRIVER
}

enum UserStatus {
  PENDING_EMAIL_VERIFICATION
  PENDING_ADMIN_APPROVAL
  ACTIVE
  SUSPENDED
  DELETED
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String
  role              UserRole  @default(BUSINESS_CLIENT)
  status            UserStatus @default(PENDING_EMAIL_VERIFICATION)
  emailVerified     DateTime? @map("email_verified")
  lastLoginAt       DateTime? @map("last_login_at")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  clientProfile     UserProfileClient? // Back-relation, FK is in UserProfileClient (userId)
  driverProfile     UserProfileDriver? // Back-relation, FK is in UserProfileDriver (userId)

  // Für Passwort-Reset
  resetToken        String?   @map("reset_token")
  resetTokenExpiry  DateTime? @map("reset_token_expiry")

  // Relations
  orders            Order[]   // Orders created by this user (as client)
  bids              Bid[]     // Bids made by this user (as driver)
  payments          Payment[] // Payments received by this user (as driver)
  assignments       Assignment[] // Assignments for this user (as driver)

  // Phase 6 Relations
  sentMessages      Message[] @relation("SentMessages") // Messages sent by this user
  receivedMessages  Message[] @relation("ReceivedMessages") // Messages received by this user
  givenRatings      Rating[]  @relation("GivenRatings") // Ratings given by this user
  receivedRatings   Rating[]  @relation("ReceivedRatings") // Ratings received by this user
  notifications     Notification[] // Notifications for this user

  // Phase 7 Relations
  supportTickets    SupportTicket[] // Support tickets created by this user
  supportResponses  SupportTicketResponse[] // Responses to support tickets by this user (admin)
  apiKeys           ApiKey[] // API keys for this user (business client)

  @@map("users")
  @@index([email])
  @@index([role])
  @@index([status])
}

model UserProfileClient {
  id                  String   @id @default(cuid())
  companyName         String   @map("company_name")
  contactPersonName   String   @map("contact_person_name")
  phoneNumber         String?  @map("phone_number")
  addressLine1        String   @map("address_line1")
  addressLine2        String?  @map("address_line2")
  city                String
  postalCode          String   @map("postal_code")
  country             String
  vatId               String?  @unique @map("vat_id")
  userId              String   @unique @map("user_id")
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  orders              Order[]  // Orders created by this client

  @@map("user_profiles_client")
  @@index([userId])
  @@index([companyName])
}

model UserProfileDriver {
  id                  String   @id @default(cuid())
  firstName           String   @map("first_name")
  lastName            String   @map("last_name")
  phoneNumber         String   @unique @map("phone_number")
  addressLine1        String   @map("address_line1")
  addressLine2        String?  @map("address_line2")
  city                String
  postalCode          String   @map("postal_code")
  country             String
  dateOfBirth         DateTime? @map("date_of_birth")
  userId              String   @unique @map("user_id")
  user                User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  documents           Document[]
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  bids                Bid[]    // Bids made by this driver
  assignments         Assignment[] // Assignments for this driver

  @@map("user_profiles_driver")
  @@index([userId])
  @@index([firstName, lastName])
}

enum DocumentType {
  DRIVING_LICENSE
  BUSINESS_REGISTRATION
  INSURANCE
  ID_CARD
  OTHER
}

enum DocumentVerificationStatus {
  PENDING
  VERIFIED
  REJECTED
}

model Document {
  id                  String   @id @default(cuid())
  documentType        DocumentType @map("document_type")
  fileName            String   @map("file_name")
  fileUrl             String   @map("file_url") // URL to the stored file (e.g., S3 link)
  mimeType            String   @map("mime_type")
  fileSize            Int      @map("file_size") // File size in bytes

  driverProfileId     String   @map("driver_profile_id")
  driverProfile       UserProfileDriver @relation(fields: [driverProfileId], references: [id], onDelete: Cascade)

  uploadedAt          DateTime @default(now()) @map("uploaded_at")
  expiresAt           DateTime? @map("expires_at")

  verificationStatus  DocumentVerificationStatus @default(PENDING) @map("verification_status")
  verifiedAt          DateTime? @map("verified_at")
  verifiedByUserId    String?   @map("verified_by_user_id")
  verificationNotes   String?   @map("verification_notes")

  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  @@map("documents")
  @@index([driverProfileId])
  @@index([verificationStatus])
}

enum OrderStatus {
  DRAFT
  POSTED
  ASSIGNED
  EN_ROUTE_TO_PICKUP
  PICKED_UP
  EN_ROUTE_TO_DELIVERY
  DELIVERED
  COMPLETED
  CANCELLED
}

enum VehicleType {
  SEDAN
  SUV
  VAN
  TRUCK
  LUXURY
  SPORTS
  OTHER
}

enum OrderUrgency {
  STANDARD
  URGENT
  EMERGENCY
}

model Order {
  id                  String    @id @default(cuid())
  title               String
  description         String?
  vehicleType         VehicleType @map("vehicle_type")
  vehicleMake         String?   @map("vehicle_make")
  vehicleModel        String?   @map("vehicle_model")
  vehicleYear         Int?      @map("vehicle_year")

  pickupAddress       String    @map("pickup_address")
  pickupCity          String    @map("pickup_city")
  pickupPostalCode    String    @map("pickup_postal_code")
  pickupCountry       String    @map("pickup_country")
  pickupLat           Float?    @map("pickup_lat")
  pickupLng           Float?    @map("pickup_lng")
  pickupDate          DateTime  @map("pickup_date")
  pickupTimeWindow    String?   @map("pickup_time_window") // e.g., "9:00-12:00"

  deliveryAddress     String    @map("delivery_address")
  deliveryCity        String    @map("delivery_city")
  deliveryPostalCode  String    @map("delivery_postal_code")
  deliveryCountry     String    @map("delivery_country")
  deliveryLat         Float?    @map("delivery_lat")
  deliveryLng         Float?    @map("delivery_lng")
  deliveryDate        DateTime  @map("delivery_date")
  deliveryTimeWindow  String?   @map("delivery_time_window") // e.g., "13:00-17:00"

  distance            Float?    // Calculated distance in km
  estimatedDuration   Int?      @map("estimated_duration") // In minutes

  price               Float?    // Final price (can be null if bidding is enabled)
  currency            String    @default("EUR")
  urgency             OrderUrgency @default(STANDARD)

  status              OrderStatus @default(DRAFT)

  specialInstructions String?   @map("special_instructions")

  // External reference for API integration
  externalReference   String?   @map("external_reference")

  // Relations
  clientId            String    @map("client_id")
  client              User      @relation(fields: [clientId], references: [id])

  clientProfileId     String    @map("client_profile_id")
  clientProfile       UserProfileClient @relation(fields: [clientProfileId], references: [id])

  bids                Bid[]
  assignment          Assignment?

  // Photos for handover
  pickupPhotos        HandoverPhoto[] @relation("PickupPhotos")
  deliveryPhotos      HandoverPhoto[] @relation("DeliveryPhotos")

  // Phase 6 relations
  messages            Message[]
  ratings             Rating[]

  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  @@map("orders")
  @@index([clientId])
  @@index([clientProfileId])
  @@index([externalReference])
  @@index([status])
  @@index([vehicleType])
  @@index([pickupDate])
  @@index([deliveryDate])
  @@index([urgency])
}

enum BidStatus {
  PENDING
  ACCEPTED
  REJECTED
  EXPIRED
  WITHDRAWN
}

model Bid {
  id                  String    @id @default(cuid())
  amount              Float?    // Optional if just expressing interest
  currency            String    @default("EUR")
  message             String?   // Optional message from driver
  status              BidStatus @default(PENDING)

  // Relations
  orderId             String    @map("order_id")
  order               Order     @relation(fields: [orderId], references: [id])

  driverId            String    @map("driver_id")
  driver              User      @relation(fields: [driverId], references: [id])

  driverProfileId     String    @map("driver_profile_id")
  driverProfile       UserProfileDriver @relation(fields: [driverProfileId], references: [id])

  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("bids")
  @@index([orderId])
  @@index([driverId])
  @@index([driverProfileId])
  @@index([status])
}

model Assignment {
  id                  String    @id @default(cuid())

  // Relations
  orderId             String    @unique @map("order_id")
  order               Order     @relation(fields: [orderId], references: [id])

  driverId            String    @map("driver_id")
  driver              User      @relation(fields: [driverId], references: [id])

  driverProfileId     String    @map("driver_profile_id")
  driverProfile       UserProfileDriver @relation(fields: [driverProfileId], references: [id])

  // Status tracking
  assignedAt          DateTime  @default(now()) @map("assigned_at")
  acceptedAt          DateTime? @map("accepted_at")
  enRouteToPickupAt   DateTime? @map("en_route_to_pickup_at")
  pickedUpAt          DateTime? @map("picked_up_at")
  enRouteToDeliveryAt DateTime? @map("en_route_to_delivery_at")
  deliveredAt         DateTime? @map("delivered_at")
  completedAt         DateTime? @map("completed_at")

  // Payment info
  paymentAmount       Float?    @map("payment_amount")
  paymentCurrency     String    @default("EUR") @map("payment_currency")
  payment             Payment?

  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("assignments")
  @@index([orderId])
  @@index([driverId])
  @@index([driverProfileId])
}

enum PaymentStatus {
  PENDING
  PROCESSING
  PAID
  FAILED
}

model Payment {
  id                  String    @id @default(cuid())
  amount              Float
  currency            String    @default("EUR")
  status              PaymentStatus @default(PENDING)

  // Relations
  assignmentId        String    @unique @map("assignment_id")
  assignment          Assignment @relation(fields: [assignmentId], references: [id])

  driverId            String    @map("driver_id")
  driver              User      @relation(fields: [driverId], references: [id])

  transactionId       String?   @map("transaction_id") // External payment processor ID
  paymentMethod       String?   @map("payment_method")

  processedAt         DateTime? @map("processed_at")

  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("payments")
  @@index([driverId])
  @@index([assignmentId])
  @@index([status])
}

model HandoverPhoto {
  id                  String    @id @default(cuid())
  fileName            String    @map("file_name")
  fileUrl             String    @map("file_url")
  mimeType            String    @map("mime_type")
  fileSize            Int       @map("file_size")

  // Relations - either pickup or delivery, not both
  pickupOrderId       String?   @map("pickup_order_id")
  pickupOrder         Order?    @relation("PickupPhotos", fields: [pickupOrderId], references: [id])

  deliveryOrderId     String?   @map("delivery_order_id")
  deliveryOrder       Order?    @relation("DeliveryPhotos", fields: [deliveryOrderId], references: [id])

  uploadedAt          DateTime  @default(now()) @map("uploaded_at")
  uploadedById        String    @map("uploaded_by_id") // User ID who uploaded

  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("handover_photos")
  @@index([pickupOrderId])
  @@index([deliveryOrderId])
}

// Phase 6: Supporting Features Models

model Message {
  id            String    @id @default(cuid())
  content       String    @db.Text
  isRead        Boolean   @default(false) @map("is_read")
  readAt        DateTime? @map("read_at")

  // Relations
  orderId       String    @map("order_id")
  order         Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  senderId      String    @map("sender_id")
  sender        User      @relation("SentMessages", fields: [senderId], references: [id])

  receiverId    String    @map("receiver_id")
  receiver      User      @relation("ReceivedMessages", fields: [receiverId], references: [id])

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("messages")
  @@index([orderId])
  @@index([senderId])
  @@index([receiverId])
  @@index([isRead])
}

model Rating {
  id            String    @id @default(cuid())
  score         Int       // 1-5 stars
  comment       String?   @db.Text

  // Relations
  orderId       String    @map("order_id")
  order         Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  raterId       String    @map("rater_id")
  rater         User      @relation("GivenRatings", fields: [raterId], references: [id])

  ratedUserId   String    @map("rated_user_id")
  ratedUser     User      @relation("ReceivedRatings", fields: [ratedUserId], references: [id])

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("ratings")
  @@index([orderId])
  @@index([raterId])
  @@index([ratedUserId])
  @@unique([orderId, raterId, ratedUserId]) // Ensure one rating per order per user pair
}

model Notification {
  id                String    @id @default(cuid())
  type              String    // Type of notification (e.g., "NEW_MESSAGE", "ORDER_STATUS_UPDATE")
  title             String
  message           String    @db.Text
  isRead            Boolean   @default(false) @map("is_read")
  readAt            DateTime? @map("read_at")

  // Relations
  userId            String    @map("user_id")
  user              User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  relatedEntityType String?   @map("related_entity_type") // e.g., "Order", "Message"
  relatedEntityId   String?   @map("related_entity_id")   // ID of the related entity

  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  @@map("notifications")
  @@index([userId])
  @@index([isRead])
  @@index([type])
}

// Phase 7: Advanced Features Models

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum SupportTicketPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

model SupportTicket {
  id            String               @id @default(cuid())
  subject       String
  message       String               @db.Text
  status        SupportTicketStatus  @default(OPEN)
  priority      SupportTicketPriority @default(MEDIUM)

  // Relations
  userId        String               @map("user_id")
  user          User                 @relation(fields: [userId], references: [id])

  responses     SupportTicketResponse[]

  createdAt     DateTime             @default(now()) @map("created_at")
  updatedAt     DateTime             @updatedAt @map("updated_at")
  resolvedAt    DateTime?            @map("resolved_at")

  @@map("support_tickets")
  @@index([userId])
  @@index([status])
  @@index([priority])
}

model SupportTicketResponse {
  id            String    @id @default(cuid())
  message       String    @db.Text
  isFromAdmin   Boolean   @default(false) @map("is_from_admin")

  // Relations
  ticketId      String    @map("ticket_id")
  ticket        SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  responderId   String    @map("responder_id")
  responder     User      @relation(fields: [responderId], references: [id])

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("support_ticket_responses")
  @@index([ticketId])
  @@index([responderId])
}

enum FAQCategory {
  GENERAL
  BUSINESS_CLIENT
  DRIVER
  ORDERS
  PAYMENTS
  TECHNICAL
}

model FAQ {
  id            String      @id @default(cuid())
  question      String
  answer        String      @db.Text
  category      FAQCategory @default(GENERAL)
  sortOrder     Int         @default(0) @map("sort_order")
  isPublished   Boolean     @default(true) @map("is_published")

  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")

  @@map("faqs")
  @@index([category])
  @@index([isPublished])
  @@index([sortOrder])
}

// API Key model for external client integration
model ApiKey {
  id            String    @id @default(cuid())
  key           String    @unique
  name          String    // Descriptive name for the API key
  isActive      Boolean   @default(true) @map("is_active")
  expiresAt     DateTime? @map("expires_at")
  lastUsedAt    DateTime? @map("last_used_at")

  // Relations
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("api_keys")
  @@index([userId])
  @@index([isActive])
}