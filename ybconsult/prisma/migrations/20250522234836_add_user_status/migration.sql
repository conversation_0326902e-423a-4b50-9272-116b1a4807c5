/*
  Warnings:

  - You are about to drop the column `type` on the `documents` table. All the data in the column will be lost.
  - You are about to drop the column `url` on the `documents` table. All the data in the column will be lost.
  - You are about to drop the column `verified` on the `documents` table. All the data in the column will be lost.
  - You are about to drop the column `address` on the `user_profiles_client` table. All the data in the column will be lost.
  - You are about to drop the column `contact_person` on the `user_profiles_client` table. All the data in the column will be lost.
  - You are about to drop the column `address` on the `user_profiles_driver` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[vat_id]` on the table `user_profiles_client` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[phone_number]` on the table `user_profiles_driver` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `document_type` to the `documents` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_name` to the `documents` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_size` to the `documents` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_url` to the `documents` table without a default value. This is not possible if the table is not empty.
  - Added the required column `mime_type` to the `documents` table without a default value. This is not possible if the table is not empty.
  - Added the required column `address_line1` to the `user_profiles_client` table without a default value. This is not possible if the table is not empty.
  - Added the required column `city` to the `user_profiles_client` table without a default value. This is not possible if the table is not empty.
  - Added the required column `contact_person_name` to the `user_profiles_client` table without a default value. This is not possible if the table is not empty.
  - Added the required column `country` to the `user_profiles_client` table without a default value. This is not possible if the table is not empty.
  - Added the required column `postal_code` to the `user_profiles_client` table without a default value. This is not possible if the table is not empty.
  - Made the column `company_name` on table `user_profiles_client` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `address_line1` to the `user_profiles_driver` table without a default value. This is not possible if the table is not empty.
  - Added the required column `city` to the `user_profiles_driver` table without a default value. This is not possible if the table is not empty.
  - Added the required column `country` to the `user_profiles_driver` table without a default value. This is not possible if the table is not empty.
  - Added the required column `postal_code` to the `user_profiles_driver` table without a default value. This is not possible if the table is not empty.
  - Made the column `first_name` on table `user_profiles_driver` required. This step will fail if there are existing NULL values in that column.
  - Made the column `last_name` on table `user_profiles_driver` required. This step will fail if there are existing NULL values in that column.
  - Made the column `phone_number` on table `user_profiles_driver` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "UserStatus" AS ENUM ('PENDING_EMAIL_VERIFICATION', 'PENDING_ADMIN_APPROVAL', 'ACTIVE', 'SUSPENDED', 'DELETED');

-- CreateEnum
CREATE TYPE "DocumentType" AS ENUM ('DRIVING_LICENSE', 'BUSINESS_REGISTRATION', 'INSURANCE', 'ID_CARD', 'OTHER');

-- CreateEnum
CREATE TYPE "DocumentVerificationStatus" AS ENUM ('PENDING', 'VERIFIED', 'REJECTED');

-- AlterTable
ALTER TABLE "documents" DROP COLUMN "type",
DROP COLUMN "url",
DROP COLUMN "verified",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "document_type" "DocumentType" NOT NULL,
ADD COLUMN     "file_name" TEXT NOT NULL,
ADD COLUMN     "file_size" INTEGER NOT NULL,
ADD COLUMN     "file_url" TEXT NOT NULL,
ADD COLUMN     "mime_type" TEXT NOT NULL,
ADD COLUMN     "verification_notes" TEXT,
ADD COLUMN     "verification_status" "DocumentVerificationStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "verified_at" TIMESTAMP(3),
ADD COLUMN     "verified_by_user_id" TEXT;

-- AlterTable
ALTER TABLE "user_profiles_client" DROP COLUMN "address",
DROP COLUMN "contact_person",
ADD COLUMN     "address_line1" TEXT NOT NULL,
ADD COLUMN     "address_line2" TEXT,
ADD COLUMN     "city" TEXT NOT NULL,
ADD COLUMN     "contact_person_name" TEXT NOT NULL,
ADD COLUMN     "country" TEXT NOT NULL,
ADD COLUMN     "phone_number" TEXT,
ADD COLUMN     "postal_code" TEXT NOT NULL,
ADD COLUMN     "vat_id" TEXT,
ALTER COLUMN "company_name" SET NOT NULL;

-- AlterTable
ALTER TABLE "user_profiles_driver" DROP COLUMN "address",
ADD COLUMN     "address_line1" TEXT NOT NULL,
ADD COLUMN     "address_line2" TEXT,
ADD COLUMN     "city" TEXT NOT NULL,
ADD COLUMN     "country" TEXT NOT NULL,
ADD COLUMN     "date_of_birth" TIMESTAMP(3),
ADD COLUMN     "postal_code" TEXT NOT NULL,
ALTER COLUMN "first_name" SET NOT NULL,
ALTER COLUMN "last_name" SET NOT NULL,
ALTER COLUMN "phone_number" SET NOT NULL;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "last_login_at" TIMESTAMP(3),
ADD COLUMN     "reset_token" TEXT,
ADD COLUMN     "reset_token_expiry" TIMESTAMP(3),
ADD COLUMN     "status" "UserStatus" NOT NULL DEFAULT 'PENDING_EMAIL_VERIFICATION';

-- CreateIndex
CREATE INDEX "documents_driver_profile_id_idx" ON "documents"("driver_profile_id");

-- CreateIndex
CREATE INDEX "documents_verification_status_idx" ON "documents"("verification_status");

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_client_vat_id_key" ON "user_profiles_client"("vat_id");

-- CreateIndex
CREATE INDEX "user_profiles_client_user_id_idx" ON "user_profiles_client"("user_id");

-- CreateIndex
CREATE INDEX "user_profiles_client_company_name_idx" ON "user_profiles_client"("company_name");

-- CreateIndex
CREATE UNIQUE INDEX "user_profiles_driver_phone_number_key" ON "user_profiles_driver"("phone_number");

-- CreateIndex
CREATE INDEX "user_profiles_driver_user_id_idx" ON "user_profiles_driver"("user_id");

-- CreateIndex
CREATE INDEX "user_profiles_driver_first_name_last_name_idx" ON "user_profiles_driver"("first_name", "last_name");

-- CreateIndex
CREATE INDEX "users_email_idx" ON "users"("email");

-- CreateIndex
CREATE INDEX "users_role_idx" ON "users"("role");

-- CreateIndex
CREATE INDEX "users_status_idx" ON "users"("status");
