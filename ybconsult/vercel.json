{"version": 2, "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm ci", "framework": "nextjs", "regions": ["fra1"], "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self'; frame-src 'self'; object-src 'none'; base-uri 'self';"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubDomains; preload"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "env": {"NEXT_PUBLIC_VERCEL_ENV": "production"}, "github": {"enabled": true, "silent": false}}