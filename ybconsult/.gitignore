# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Node
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment Variables
.env*.local # Glob für alle .env.*.local Dateien
# .env.development.local # Redundant durch obigen Glob
# .env.test.local # Redundant
# .env.production.local # Redundant

# Prisma
# Comment out if you want to commit schema changes without migrations
# src/prisma/migrations/*
# src/prisma/dev.db
# src/prisma/dev.db-journal

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Folders to ignore if you are using Docker
# .dockerignore # Normalerweise nicht in .gitignore, sondern eine eigene Datei
# docker-compose.yml # Normalerweise nicht in .gitignore

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Storybook
storybook-static

# Die folgenden Zeilen waren Duplikate oder bereits abgedeckt:
# .DS_Store
# *.env.local
# *.env.*.local
# npm-debug.log*
# yarn-debug.log*
# yarn-error.log*
*storybook.log
