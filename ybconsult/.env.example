# Environment variables for YoungMobility

# Next.js
# NEXT_PUBLIC_SOME_KEY=your_value

# Database (Prisma) - Update with your actual connection string
DATABASE_URL="postgresql://user:password@host:port/database?schema=public"

# JWT Secrets (Generate strong random strings)
# JWT_SECRET=
# JWT_REFRESH_SECRET=

# API Keys for external services
# NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=
# STRIPE_SECRET_KEY=
# SENDGRID_API_KEY=

# Sentry DSN (optional)
# NEXT_PUBLIC_SENTRY_DSN=