/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      // Füge hier weitere Domains hinzu, falls benötigt
    ],
    // Wenn du Bilder direkt aus dem /public Ordner ohne remotePatterns laden willst,
    // ist keine spezielle Konfiguration für 'localhost' oder relative Pfade nötig,
    // solange sie mit '/' beginnen (z.B. /placeholder-image.jpg).
  },
};

export default nextConfig;
