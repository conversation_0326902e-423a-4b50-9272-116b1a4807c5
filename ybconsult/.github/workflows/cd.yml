name: CD

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy to Vercel (Staging)
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          scope: ${{ secrets.VERCEL_ORG_ID }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          vercel-args: '--prod'
          alias-domains: |
            staging.youngmobility-consulting.de

  e2e-staging:
    name: E2E Tests on Staging
    needs: deploy-staging
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run Playwright tests against staging
        run: npx playwright test
        env:
          PLAYWRIGHT_TEST_BASE_URL: https://staging.youngmobility-consulting.de

      - name: Upload Playwright report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report-staging
          path: playwright-report/
          retention-days: 30

  deploy-production:
    name: Deploy to Production
    needs: e2e-staging
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          scope: ${{ secrets.VERCEL_ORG_ID }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          vercel-args: '--prod'
          alias-domains: |
            youngmobility-consulting.de
            www.youngmobility-consulting.de

  e2e-production:
    name: E2E Tests on Production
    needs: deploy-production
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run Playwright tests against production
        run: npx playwright test
        env:
          PLAYWRIGHT_TEST_BASE_URL: https://www.youngmobility-consulting.de

      - name: Upload Playwright report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report-production
          path: playwright-report/
          retention-days: 30

  notify:
    name: Notify Team
    needs: e2e-production
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify on success
        if: ${{ success() }}
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "✅ Deployment to production successful! Visit https://www.youngmobility-consulting.de"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify on failure
        if: ${{ failure() }}
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "❌ Deployment to production failed! Check the GitHub Actions logs for details."
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
