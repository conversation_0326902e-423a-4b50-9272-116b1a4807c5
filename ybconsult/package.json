{"name": "ybconsult", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,css}\"", "lint:fix": "eslint . --ext .ts,.tsx --fix", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "start:dev:server": "cd src/server && npx nest start --watch", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/platform-express": "^11.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-three/fiber": "^9.1.2", "@splinetool/react-spline": "^4.0.0", "@tanstack/react-query": "^5.76.1", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/three": "^0.176.0", "autoprefixer": "^10.4.21", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.2", "lucide-react": "^0.511.0", "motion": "^12.15.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "zod": "^3.25.23", "zustand": "^5.0.4"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@eslint/eslintrc": "^3", "@nestjs/cli": "^11.0.7", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/blocks": "^8.6.14", "@storybook/experimental-addon-test": "^8.6.14", "@storybook/experimental-nextjs-vite": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@tanstack/react-query-devtools": "^5.76.1", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-storybook": "^0.12.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "playwright": "^1.52.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.8.2", "storybook": "^8.6.14", "tw-animate-css": "^1.3.0", "typescript": "^5", "vitest": "^3.1.4"}}