import type { Preview } from "@storybook/react";

// WICHTIGER DEBUGGING-SCHRITT:
// Kommentieren Sie die folgende Zeile testweise aus.
// Wenn der Fehler danach verschwindet, liegt das Problem wahrscheinlich
// bei der Verarbeitung Ihrer globalen CSS-Datei (globals.css),
// möglicherweise im Zusammenspiel mit Tailwind CSS v4 und React 19.
// import '../src/app/globals.css';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    // Hier können weitere globale Parameter für Storybook eingefügt werden,
    // z.B. für Viewports, Hintergründe, Next.js-Integration etc.
    // nextjs: { // Beispiel für @storybook/nextjs Konfiguration
    //   appDirectory: true, // <PERSON><PERSON>, wenn Sie den Next.js App Router verwenden
    // },
  },
  decorators: [
    // Hier können globale Decorators hinzugefügt werden.
    // Beispiel: Ein Decorator, um einen Theme Provider oder Router Context bereitzustellen.
    // (Story) => (
    //   <MyThemeProvider>
    //     <Story />
    //   </MyThemeProvider>
    // ),
  ],
};

export default preview;