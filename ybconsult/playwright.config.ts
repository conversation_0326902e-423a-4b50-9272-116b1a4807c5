/**
 * Playwright Konfiguration
 * 
 * Diese Datei enthält die Konfiguration für Playwright E2E-Tests.
 * Teil der Phase 8: Comprehensive Automated Testing (YM-805)
 */

import { defineConfig, devices } from "@playwright/test";

/**
 * <PERSON><PERSON><PERSON> https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: "./e2e",
  /* Maximale Zeit, die ein Test laufen darf */
  timeout: 30 * 1000,
  /* <PERSON><PERSON><PERSON><PERSON>, dass Tests innerhalb dieses Zeitraums ausgeführt werden */
  expect: {
    /**
     * Maximale Zeit, die auf ein erwartetes Element gewartet wird
     * Standard ist 5000 ms
     */
    timeout: 5000,
  },
  /* Fehlgeschlagene Tests nicht erneut ausführen */
  fullyParallel: true,
  /* Ausführliche Berichte generieren */
  reporter: [
    ["html", { open: "never" }],
    ["list"],
  ],
  /* Gemeinsame Einstellungen für alle Projekte */
  use: {
    /* Maximale Zeit, die auf das Laden einer Seite gewartet wird */
    navigationTimeout: 15000,
    /* Basis-URL für alle Tests */
    baseURL: "http://localhost:3000",
    /* Erfasse Screenshots bei Fehlern */
    screenshot: "only-on-failure",
    /* Erfasse Traces bei Fehlern */
    trace: "retain-on-failure",
    /* Erfasse Videos bei Fehlern */
    video: "retain-on-failure",
  },
  /* Konfiguration für verschiedene Browser */
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
    {
      name: "firefox",
      use: { ...devices["Desktop Firefox"] },
    },
    {
      name: "webkit",
      use: { ...devices["Desktop Safari"] },
    },
    /* Test auf Mobilgeräten */
    {
      name: "mobile-chrome",
      use: { ...devices["Pixel 5"] },
    },
    {
      name: "mobile-safari",
      use: { ...devices["iPhone 12"] },
    },
  ],
  /* Starte einen Webserver vor den Tests */
  webServer: {
    command: "npm run dev",
    url: "http://localhost:3000",
    reuseExistingServer: !process.env.CI,
    stdout: "pipe",
    stderr: "pipe",
  },
});
